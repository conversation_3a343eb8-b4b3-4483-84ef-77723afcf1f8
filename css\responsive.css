/* Responsive Design - Mobile First Approach */

/* Base styles are for mobile (already defined in styles.css) */

/* Small tablets and large phones (landscape) */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;
    }
    
    .header-controls {
        justify-content: space-between;
        flex-wrap: wrap;
        gap: var(--spacing-3);
    }
    
    .language-toggle {
        order: 1;
    }
    
    .theme-selector {
        order: 2;
        flex: 1;
    }

    .theme-selector select {
        width: 100%;
    }

    .demo-btn {
        order: 3;
        justify-content: center;
        width: 100%;
    }

    .export-btn {
        order: 4;
        justify-content: center;
        width: 100%;
    }
    
    /* Main Layout - Stack vertically on mobile */
    .app-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .form-panel {
        order: 1;
    }
    
    .preview-panel {
        order: 2;
        min-height: 500px;
    }
    
    /* Form adjustments */
    .form-container {
        padding: var(--spacing-4);
    }
    
    .form-container h2 {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-4);
    }
    
    .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .form-group.full-width {
        grid-column: 1;
    }
    
    .form-navigation {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .nav-btn {
        width: 100%;
        justify-content: center;
    }
    
    /* Skills input adjustments */
    .skill-input-group {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .skill-input-group input,
    .skill-input-group select,
    .skill-input-group button {
        width: 100%;
    }
    
    /* Preview adjustments */
    .preview-header {
        padding: var(--spacing-4);
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
    }
    
    .preview-controls {
        justify-content: center;
    }
    
    .preview-wrapper {
        padding: var(--spacing-3);
    }
    
    .cv-preview {
        width: 100%;
        min-height: auto;
        transform: scale(0.8);
        transform-origin: top center;
    }
    
    /* CV content adjustments for mobile */
    .cv-content {
        padding: var(--spacing-4);
    }
    
    .cv-header {
        margin: calc(-1 * var(--spacing-4)) calc(-1 * var(--spacing-4)) var(--spacing-4);
        padding: var(--spacing-4);
    }
    
    .cv-name {
        font-size: var(--font-size-2xl);
    }
    
    .cv-title {
        font-size: var(--font-size-lg);
    }
    
    .cv-contact {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-2);
    }
    
    .cv-section-title {
        font-size: var(--font-size-lg);
    }
    
    .cv-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-1);
    }
    
    .cv-item-date {
        align-self: flex-end;
    }
    
    .cv-skills-grid {
        grid-template-columns: 1fr;
    }
    
    .cv-languages-grid {
        grid-template-columns: 1fr;
    }
}

/* Small phones */
@media (max-width: 480px) {
    :root {
        --spacing-4: 0.75rem;
        --spacing-6: 1rem;
        --spacing-8: 1.25rem;
    }
    
    .header-content {
        padding: var(--spacing-3) 0;
    }
    
    .logo {
        font-size: var(--font-size-lg);
    }
    
    .logo i {
        font-size: var(--font-size-xl);
    }
    
    .form-container {
        padding: var(--spacing-3);
    }
    
    .form-section h3 {
        font-size: var(--font-size-lg);
        gap: var(--spacing-2);
    }
    
    .progress-indicator {
        margin-bottom: var(--spacing-4);
    }
    
    .cv-form {
        max-height: calc(100vh - 250px);
    }
    
    .cv-preview {
        transform: scale(0.7);
    }
    
    .cv-content {
        padding: var(--spacing-3);
    }
    
    .cv-name {
        font-size: var(--font-size-xl);
    }
    
    .cv-title {
        font-size: var(--font-size-base);
    }
    
    .skills-container {
        min-height: 40px;
        padding: var(--spacing-2);
    }
    
    .skill-tag {
        font-size: var(--font-size-xs);
        padding: var(--spacing-1) var(--spacing-2);
    }
}

/* Medium tablets */
@media (min-width: 769px) and (max-width: 1024px) {
    .app-layout {
        grid-template-columns: 350px 1fr;
        gap: var(--spacing-6);
    }
    
    .form-container {
        padding: var(--spacing-5);
    }
    
    .cv-preview {
        transform: scale(0.9);
    }
}

/* Large tablets and small desktops */
@media (min-width: 1025px) and (max-width: 1200px) {
    .app-layout {
        grid-template-columns: 380px 1fr;
        gap: var(--spacing-6);
    }
}

/* Large desktops */
@media (min-width: 1201px) {
    .container {
        max-width: 1600px;
    }
    
    .app-layout {
        grid-template-columns: 420px 1fr;
        gap: var(--spacing-10);
    }
    
    .cv-preview {
        transform: scale(1.1);
    }
}

/* Ultra-wide screens */
@media (min-width: 1600px) {
    .app-layout {
        grid-template-columns: 450px 1fr;
        gap: var(--spacing-12);
    }
    
    .cv-preview {
        transform: scale(1.2);
    }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
    .app-main {
        padding: var(--spacing-3) 0;
    }
    
    .form-container h2 {
        margin-bottom: var(--spacing-3);
    }
    
    .progress-indicator {
        margin-bottom: var(--spacing-4);
    }
    
    .cv-form {
        max-height: calc(100vh - 200px);
    }
    
    .form-section h3 {
        margin-bottom: var(--spacing-3);
    }
    
    .form-navigation {
        margin-top: var(--spacing-3);
        padding-top: var(--spacing-3);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .cv-preview {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .cv-preview.theme-creative .cv-section {
        animation: none !important;
    }
}

/* Dark mode support (if user prefers) */
@media (prefers-color-scheme: dark) {
    /* This can be expanded later for dark theme support */
    .loading-overlay {
        background: rgba(0, 0, 0, 0.9);
    }
}

/* Print styles */
@media print {
    .app-header,
    .form-panel,
    .preview-header,
    .loading-overlay {
        display: none !important;
    }
    
    .app-layout {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .preview-panel {
        background: white;
        box-shadow: none;
        border-radius: 0;
    }
    
    .preview-wrapper {
        padding: 0;
        background: white;
    }
    
    .cv-preview {
        transform: none !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
    }
    
    .cv-content {
        padding: 0;
    }
    
    /* Ensure colors print correctly */
    .cv-preview.theme-modern .cv-header,
    .cv-preview.theme-creative .cv-header::before {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* Focus styles for accessibility */
@media (prefers-reduced-motion: no-preference) {
    .form-group input:focus,
    .form-group textarea:focus,
    .form-group select:focus,
    .lang-btn:focus,
    .export-btn:focus,
    .nav-btn:focus,
    .add-btn:focus,
    .zoom-btn:focus {
        transform: translateY(-1px);
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    .lang-btn,
    .nav-btn,
    .add-btn,
    .zoom-btn,
    .export-btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .form-group input,
    .form-group textarea,
    .form-group select {
        min-height: 44px;
    }
    
    .skill-tag .remove-skill,
    .dynamic-item .remove-btn {
        min-width: 32px;
        min-height: 32px;
    }
}
