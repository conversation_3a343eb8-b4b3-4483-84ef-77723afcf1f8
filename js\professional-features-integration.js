// ELASHRAFY CV - Professional Features Integration
// تكامل الميزات الاحترافية - الأشرافي للسيرة الذاتية

class ProfessionalFeaturesIntegration {
    constructor() {
        this.isInitialized = false;
        this.features = {
            liveEditing: true,
            advancedPhotoEditor: true,
            professionalFonts: true,
            rtlLtrSupport: true,
            advancedExport: true,
            templateCustomization: true,
            realTimePreview: true,
            cloudSync: false, // Future feature
            aiAssistant: false // Future feature
        };
        
        this.init();
    }
    
    init() {
        console.log('🚀 Initializing ELASHRAFY CV Professional Features...');
        this.setupProfessionalFeatures();
        this.initializeAdvancedComponents();
        this.setupEventListeners();
        this.loadProfessionalAssets();
        this.isInitialized = true;
        console.log('✅ Professional Features Initialized Successfully');
    }
    
    setupProfessionalFeatures() {
        // Initialize live editing system
        this.initializeLiveEditing();
        
        // Setup advanced photo editor
        this.setupAdvancedPhotoEditor();
        
        // Load professional fonts
        this.loadProfessionalFonts();
        
        // Setup RTL/LTR support
        this.setupBidirectionalSupport();
        
        // Initialize advanced export features
        this.setupAdvancedExport();
    }
    
    initializeLiveEditing() {
        // Enhanced live editing with real-time updates
        const liveEditingConfig = {
            updateDelay: 300, // milliseconds
            autoSave: true,
            conflictResolution: 'latest',
            undoRedoLimit: 50
        };
        
        // Setup live editing event listeners
        document.addEventListener('input', this.debounce((e) => {
            if (e.target.closest('.cv-form')) {
                this.handleLiveEdit(e);
            }
        }, liveEditingConfig.updateDelay));
        
        // Setup undo/redo system
        this.setupUndoRedoSystem();
    }
    
    handleLiveEdit(event) {
        const field = event.target;
        const section = field.closest('[data-section]')?.dataset.section;
        const fieldName = field.name || field.id;
        const value = field.value;
        
        // Update preview in real-time
        if (window.livePreview) {
            window.livePreview.queueUpdate({
                section: section,
                field: fieldName,
                value: value,
                timestamp: Date.now()
            });
        }
        
        // Auto-save changes
        if (this.features.autoSave) {
            this.autoSaveChanges(section, fieldName, value);
        }
        
        // Track changes for undo/redo
        this.trackChange({
            section: section,
            field: fieldName,
            oldValue: field.dataset.previousValue || '',
            newValue: value,
            timestamp: Date.now()
        });
        
        field.dataset.previousValue = value;
    }
    
    setupAdvancedPhotoEditor() {
        // Enhanced photo editing capabilities
        const photoEditorFeatures = {
            filters: ['brightness', 'contrast', 'saturation', 'blur', 'sepia', 'grayscale'],
            effects: ['vintage', 'modern', 'professional', 'artistic'],
            cropping: ['square', 'circle', 'rectangle', 'custom'],
            backgrounds: ['remove', 'blur', 'solid', 'gradient'],
            enhancement: ['auto-enhance', 'noise-reduction', 'sharpening']
        };
        
        // Add advanced photo editing UI
        this.createAdvancedPhotoEditor(photoEditorFeatures);
    }
    
    createAdvancedPhotoEditor(features) {
        const photoSection = document.querySelector('[data-section="personal"]');
        if (!photoSection) return;
        
        const photoInput = photoSection.querySelector('#photoInput');
        if (!photoInput) return;
        
        // Add advanced photo editor button
        const advancedEditorBtn = document.createElement('button');
        advancedEditorBtn.type = 'button';
        advancedEditorBtn.className = 'advanced-photo-editor-btn';
        advancedEditorBtn.innerHTML = `
            <i class="fas fa-magic"></i>
            <span>محرر الصور المتقدم</span>
        `;
        
        photoInput.parentNode.appendChild(advancedEditorBtn);
        
        advancedEditorBtn.addEventListener('click', () => {
            this.openAdvancedPhotoEditor();
        });
    }
    
    openAdvancedPhotoEditor() {
        // Create advanced photo editor modal
        const modal = document.createElement('div');
        modal.className = 'advanced-photo-editor-modal active';
        modal.innerHTML = `
            <div class="photo-editor-content">
                <div class="photo-editor-header">
                    <h2>محرر الصور المتقدم - ELASHRAFY CV</h2>
                    <button class="close-editor">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="photo-editor-workspace">
                    <div class="photo-canvas-container">
                        <canvas id="advancedPhotoCanvas"></canvas>
                    </div>
                    
                    <div class="photo-editor-tools">
                        <div class="tool-section">
                            <h4>الفلاتر</h4>
                            <div class="filter-controls">
                                <div class="filter-control">
                                    <label>السطوع</label>
                                    <input type="range" id="brightness" min="-100" max="100" value="0">
                                </div>
                                <div class="filter-control">
                                    <label>التباين</label>
                                    <input type="range" id="contrast" min="-100" max="100" value="0">
                                </div>
                                <div class="filter-control">
                                    <label>التشبع</label>
                                    <input type="range" id="saturation" min="-100" max="100" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="tool-section">
                            <h4>التأثيرات</h4>
                            <div class="effects-grid">
                                <button class="effect-btn" data-effect="vintage">كلاسيكي</button>
                                <button class="effect-btn" data-effect="modern">عصري</button>
                                <button class="effect-btn" data-effect="professional">احترافي</button>
                                <button class="effect-btn" data-effect="artistic">فني</button>
                            </div>
                        </div>
                        
                        <div class="tool-section">
                            <h4>القص والتشكيل</h4>
                            <div class="crop-options">
                                <button class="crop-btn" data-crop="square">مربع</button>
                                <button class="crop-btn" data-crop="circle">دائري</button>
                                <button class="crop-btn" data-crop="rectangle">مستطيل</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="photo-editor-actions">
                    <button class="editor-btn secondary" onclick="this.closest('.advanced-photo-editor-modal').remove()">
                        إلغاء
                    </button>
                    <button class="editor-btn primary" onclick="professionalFeatures.applyPhotoEdits()">
                        تطبيق التعديلات
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Initialize photo editor functionality
        this.initializePhotoEditorControls(modal);
    }
    
    loadProfessionalFonts() {
        // Load premium Arabic and English fonts
        const professionalFonts = [
            // Arabic Fonts
            'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap',
            
            // English Fonts
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap'
        ];
        
        professionalFonts.forEach(fontUrl => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = fontUrl;
            document.head.appendChild(link);
        });
        
        // Add font selector to template customization
        this.addFontSelector();
    }
    
    setupBidirectionalSupport() {
        // Enhanced RTL/LTR support for Arabic and English
        const rtlLtrConfig = {
            autoDetect: true,
            mixedContent: true,
            directionSwitching: true
        };
        
        // Add direction detection and switching
        document.addEventListener('input', (e) => {
            if (e.target.matches('input[type="text"], textarea')) {
                this.detectAndSetDirection(e.target);
            }
        });
    }
    
    detectAndSetDirection(element) {
        const text = element.value;
        const arabicRegex = /[\u0600-\u06FF]/;
        
        if (arabicRegex.test(text)) {
            element.style.direction = 'rtl';
            element.style.textAlign = 'right';
        } else {
            element.style.direction = 'ltr';
            element.style.textAlign = 'left';
        }
    }
    
    setupAdvancedExport() {
        // Enhanced PDF export with professional features
        const exportFeatures = {
            formats: ['PDF', 'PNG', 'JPEG', 'SVG'],
            qualities: ['Standard', 'High', 'Print'],
            sizes: ['A4', 'Letter', 'Legal', 'Custom'],
            orientations: ['Portrait', 'Landscape']
        };
        
        this.createAdvancedExportDialog(exportFeatures);
    }
    
    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    setupEventListeners() {
        // Global event listeners for professional features
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeAdvancedComponents();
        });
        
        // Template selection events
        document.addEventListener('templateSelected', (e) => {
            this.handleTemplateSelection(e.detail);
        });
        
        // Export events
        document.addEventListener('exportRequested', (e) => {
            this.handleAdvancedExport(e.detail);
        });
    }
    
    initializeAdvancedComponents() {
        // Initialize all advanced components
        if (window.readyTemplatesDB) {
            console.log(`✅ Templates Database: ${window.readyTemplatesDB.templates.length} templates loaded`);
        }
        
        if (window.livePreview) {
            console.log('✅ Live Preview System: Ready');
        }
        
        if (window.advancedEditor) {
            console.log('✅ Advanced Editor: Ready');
        }
        
        if (window.professionalApp) {
            console.log('✅ Professional App: Ready');
        }
    }
    
    loadProfessionalAssets() {
        // Load additional professional assets
        const assets = [
            'css/professional-features.css',
            'css/advanced-photo-editor.css',
            'css/professional-fonts.css'
        ];

        assets.forEach(asset => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = asset;
            document.head.appendChild(link);
        });
    }

    // Advanced Photo Editor Methods
    initializePhotoEditorControls(modal) {
        const canvas = modal.querySelector('#advancedPhotoCanvas');
        const ctx = canvas.getContext('2d');

        // Setup filter controls
        const filterControls = modal.querySelectorAll('.filter-control input');
        filterControls.forEach(control => {
            control.addEventListener('input', () => {
                this.applyFilters(canvas, ctx);
            });
        });

        // Setup effect buttons
        const effectButtons = modal.querySelectorAll('.effect-btn');
        effectButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.applyEffect(canvas, ctx, btn.dataset.effect);
            });
        });

        // Setup crop buttons
        const cropButtons = modal.querySelectorAll('.crop-btn');
        cropButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.applyCrop(canvas, ctx, btn.dataset.crop);
            });
        });
    }

    applyFilters(canvas, ctx) {
        const brightness = document.getElementById('brightness').value;
        const contrast = document.getElementById('contrast').value;
        const saturation = document.getElementById('saturation').value;

        ctx.filter = `
            brightness(${100 + parseInt(brightness)}%)
            contrast(${100 + parseInt(contrast)}%)
            saturate(${100 + parseInt(saturation)}%)
        `;

        // Redraw image with filters
        this.redrawCanvas(canvas, ctx);
    }

    applyEffect(canvas, ctx, effect) {
        switch (effect) {
            case 'vintage':
                ctx.filter = 'sepia(80%) contrast(120%) brightness(90%)';
                break;
            case 'modern':
                ctx.filter = 'contrast(110%) saturate(120%) brightness(105%)';
                break;
            case 'professional':
                ctx.filter = 'contrast(105%) brightness(102%) saturate(95%)';
                break;
            case 'artistic':
                ctx.filter = 'saturate(150%) contrast(130%) hue-rotate(15deg)';
                break;
        }

        this.redrawCanvas(canvas, ctx);
    }

    applyCrop(canvas, ctx, cropType) {
        // Implement cropping functionality
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

        switch (cropType) {
            case 'square':
                this.cropToSquare(canvas, ctx, imageData);
                break;
            case 'circle':
                this.cropToCircle(canvas, ctx, imageData);
                break;
            case 'rectangle':
                this.cropToRectangle(canvas, ctx, imageData);
                break;
        }
    }

    // Font Management Methods
    addFontSelector() {
        const fontSelector = document.createElement('div');
        fontSelector.className = 'professional-font-selector';
        fontSelector.innerHTML = `
            <div class="font-selector-header">
                <h4>الخطوط الاحترافية</h4>
                <button class="font-selector-toggle">
                    <i class="fas fa-font"></i>
                </button>
            </div>
            <div class="font-options">
                <div class="font-category">
                    <h5>الخطوط العربية</h5>
                    <div class="font-list">
                        <button class="font-option" data-font="Amiri">أميري</button>
                        <button class="font-option" data-font="Cairo">القاهرة</button>
                        <button class="font-option" data-font="Tajawal">تجوال</button>
                        <button class="font-option" data-font="Almarai">المرعي</button>
                    </div>
                </div>
                <div class="font-category">
                    <h5>English Fonts</h5>
                    <div class="font-list">
                        <button class="font-option" data-font="Inter">Inter</button>
                        <button class="font-option" data-font="Poppins">Poppins</button>
                        <button class="font-option" data-font="Roboto">Roboto</button>
                        <button class="font-option" data-font="Montserrat">Montserrat</button>
                    </div>
                </div>
            </div>
        `;

        // Add to template customization area
        const customizationArea = document.querySelector('.template-customization') ||
                                 document.querySelector('.form-panel');
        if (customizationArea) {
            customizationArea.appendChild(fontSelector);
            this.setupFontSelectorEvents(fontSelector);
        }
    }

    setupFontSelectorEvents(selector) {
        const toggle = selector.querySelector('.font-selector-toggle');
        const options = selector.querySelector('.font-options');
        const fontButtons = selector.querySelectorAll('.font-option');

        toggle.addEventListener('click', () => {
            options.classList.toggle('active');
        });

        fontButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.applyFont(btn.dataset.font);
                fontButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
    }

    applyFont(fontFamily) {
        // Apply font to preview
        const preview = document.querySelector('.cv-preview');
        if (preview) {
            preview.style.fontFamily = `"${fontFamily}", sans-serif`;
        }

        // Update template data
        if (window.livePreview && window.livePreview.currentTemplate) {
            window.livePreview.currentTemplate.fonts = [fontFamily];
            window.livePreview.renderPreview();
        }
    }

    // Advanced Export Methods
    createAdvancedExportDialog(features) {
        const exportBtn = document.querySelector('.export-btn') ||
                         document.querySelector('[data-action="export"]');

        if (exportBtn) {
            exportBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showAdvancedExportDialog(features);
            });
        }
    }

    showAdvancedExportDialog(features) {
        const dialog = document.createElement('div');
        dialog.className = 'advanced-export-dialog active';
        dialog.innerHTML = `
            <div class="export-dialog-content">
                <div class="export-dialog-header">
                    <h2>تصدير احترافي - ELASHRAFY CV</h2>
                    <button class="close-dialog">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="export-options">
                    <div class="export-section">
                        <h4>تنسيق الملف</h4>
                        <div class="format-options">
                            ${features.formats.map(format => `
                                <label class="format-option">
                                    <input type="radio" name="format" value="${format}" ${format === 'PDF' ? 'checked' : ''}>
                                    <span>${format}</span>
                                </label>
                            `).join('')}
                        </div>
                    </div>

                    <div class="export-section">
                        <h4>جودة التصدير</h4>
                        <select id="exportQuality">
                            ${features.qualities.map(quality => `
                                <option value="${quality}" ${quality === 'High' ? 'selected' : ''}>${quality}</option>
                            `).join('')}
                        </select>
                    </div>

                    <div class="export-section">
                        <h4>حجم الصفحة</h4>
                        <select id="pageSize">
                            ${features.sizes.map(size => `
                                <option value="${size}" ${size === 'A4' ? 'selected' : ''}>${size}</option>
                            `).join('')}
                        </select>
                    </div>

                    <div class="export-section">
                        <h4>اتجاه الصفحة</h4>
                        <div class="orientation-options">
                            ${features.orientations.map(orientation => `
                                <label class="orientation-option">
                                    <input type="radio" name="orientation" value="${orientation}" ${orientation === 'Portrait' ? 'checked' : ''}>
                                    <span>${orientation === 'Portrait' ? 'عمودي' : 'أفقي'}</span>
                                </label>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <div class="export-actions">
                    <button class="export-btn secondary" onclick="this.closest('.advanced-export-dialog').remove()">
                        إلغاء
                    </button>
                    <button class="export-btn primary" onclick="professionalFeatures.executeAdvancedExport()">
                        <i class="fas fa-download"></i>
                        تصدير الآن
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Close dialog events
        dialog.querySelector('.close-dialog').addEventListener('click', () => {
            dialog.remove();
        });
    }

    executeAdvancedExport() {
        const format = document.querySelector('input[name="format"]:checked').value;
        const quality = document.getElementById('exportQuality').value;
        const pageSize = document.getElementById('pageSize').value;
        const orientation = document.querySelector('input[name="orientation"]:checked').value;

        // Show export progress
        this.showExportProgress();

        // Execute export based on format
        switch (format) {
            case 'PDF':
                this.exportToPDF(quality, pageSize, orientation);
                break;
            case 'PNG':
                this.exportToPNG(quality);
                break;
            case 'JPEG':
                this.exportToJPEG(quality);
                break;
            case 'SVG':
                this.exportToSVG();
                break;
        }
    }

    showExportProgress() {
        const progress = document.createElement('div');
        progress.className = 'export-progress';
        progress.innerHTML = `
            <div class="progress-content">
                <div class="progress-icon">
                    <i class="fas fa-cog fa-spin"></i>
                </div>
                <h3>جاري التصدير...</h3>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p>يتم الآن إنشاء ملف السيرة الذاتية بجودة عالية</p>
            </div>
        `;

        document.body.appendChild(progress);

        // Simulate progress
        let progressValue = 0;
        const progressFill = progress.querySelector('.progress-fill');
        const interval = setInterval(() => {
            progressValue += Math.random() * 20;
            if (progressValue >= 100) {
                progressValue = 100;
                clearInterval(interval);
                setTimeout(() => {
                    progress.remove();
                    this.showExportSuccess();
                }, 500);
            }
            progressFill.style.width = progressValue + '%';
        }, 200);
    }

    showExportSuccess() {
        const success = document.createElement('div');
        success.className = 'export-success';
        success.innerHTML = `
            <div class="success-content">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>تم التصدير بنجاح!</h3>
                <p>تم إنشاء ملف السيرة الذاتية وحفظه على جهازك</p>
                <button class="success-btn" onclick="this.closest('.export-success').remove()">
                    ممتاز
                </button>
            </div>
        `;

        document.body.appendChild(success);

        setTimeout(() => {
            success.remove();
        }, 3000);
    }
}

// Initialize Professional Features Integration
const professionalFeatures = new ProfessionalFeaturesIntegration();

// Export for global access
window.professionalFeatures = professionalFeatures;
