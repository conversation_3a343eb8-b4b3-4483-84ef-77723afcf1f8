// ELASHRAFY CV - Templates Display Manager
// مدير عرض القوالب - الأشرافي للسيرة الذاتية

class TemplatesDisplayManager {
    constructor() {
        this.currentView = 'grid';
        this.isLoading = false;
        this.animationDelay = 100; // milliseconds between template card animations
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeDisplay();
    }
    
    setupEventListeners() {
        // View toggle buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.changeView(e.target.dataset.view);
            });
        });
        
        // Template card interactions
        document.addEventListener('click', (e) => {
            if (e.target.closest('.template-card')) {
                this.handleTemplateCardClick(e);
            }
        });
        
        // Intersection Observer for lazy loading
        this.setupIntersectionObserver();
    }
    
    initializeDisplay() {
        // Wait for templates to be ready
        this.waitForTemplates().then(() => {
            this.displayTemplates();
        });
    }
    
    waitForTemplates() {
        return new Promise((resolve) => {
            const checkTemplates = () => {
                if (window.readyTemplatesDB && window.readyTemplatesDB.templates.length > 0) {
                    resolve();
                } else {
                    setTimeout(checkTemplates, 100);
                }
            };
            checkTemplates();
        });
    }
    
    displayTemplates(templates = null) {
        const grid = document.getElementById('templatesGrid');
        if (!grid) return;
        
        this.isLoading = true;
        this.showLoadingState();
        
        // Use provided templates or get all templates
        const templatesToShow = templates || window.readyTemplatesDB.templates.slice(0, 20);
        
        setTimeout(() => {
            this.renderTemplateCards(templatesToShow);
            this.isLoading = false;
        }, 500);
    }
    
    showLoadingState() {
        const grid = document.getElementById('templatesGrid');
        if (!grid) return;
        
        grid.innerHTML = `
            <div class="templates-loading">
                <div class="loading-animation">
                    <div class="loading-dots">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                    <h3>جاري تحميل القوالب الجميلة...</h3>
                    <p>يتم الآن تحضير أكثر من 400 قالب احترافي لك</p>
                </div>
            </div>
        `;
        
        // Add loading animation styles
        this.addLoadingStyles();
    }
    
    addLoadingStyles() {
        if (document.getElementById('loadingStyles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'loadingStyles';
        styles.textContent = `
            .templates-loading {
                grid-column: 1 / -1;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 400px;
                text-align: center;
            }
            
            .loading-animation {
                max-width: 400px;
            }
            
            .loading-dots {
                display: flex;
                justify-content: center;
                gap: 8px;
                margin-bottom: 24px;
            }
            
            .dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                animation: dotPulse 1.4s ease-in-out infinite both;
            }
            
            .dot:nth-child(1) { animation-delay: -0.32s; }
            .dot:nth-child(2) { animation-delay: -0.16s; }
            .dot:nth-child(3) { animation-delay: 0s; }
            
            @keyframes dotPulse {
                0%, 80%, 100% {
                    transform: scale(0.8);
                    opacity: 0.5;
                }
                40% {
                    transform: scale(1.2);
                    opacity: 1;
                }
            }
            
            .loading-animation h3 {
                font-size: var(--font-size-xl);
                font-weight: 600;
                color: var(--gray-800);
                margin-bottom: var(--spacing-2);
            }
            
            .loading-animation p {
                color: var(--gray-600);
                font-size: var(--font-size-base);
            }
        `;
        
        document.head.appendChild(styles);
    }
    
    renderTemplateCards(templates) {
        const grid = document.getElementById('templatesGrid');
        if (!grid) return;
        
        // Clear loading state
        grid.innerHTML = '';
        
        // Create template cards with staggered animation
        templates.forEach((template, index) => {
            setTimeout(() => {
                const card = this.createTemplateCard(template);
                grid.appendChild(card);
                
                // Trigger entrance animation
                requestAnimationFrame(() => {
                    card.classList.add('template-card-visible');
                });
            }, index * this.animationDelay);
        });
    }
    
    createTemplateCard(template) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.dataset.templateId = template.id;
        
        if (template.isPremium) {
            card.classList.add('premium');
        }
        
        card.innerHTML = `
            ${template.isPremium ? '<div class="premium-badge"><i class="fas fa-crown"></i> بريميوم</div>' : ''}
            
            <div class="template-preview">
                <img src="${template.preview}" alt="${template.name}" loading="lazy" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjMwMCIgaGVpZ2h0PSI0MDAiIGZpbGw9IiNmM2Y0ZjYiLz48dGV4dCB4PSIxNTAiIHk9IjIwMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzZiNzI4MCIgZm9udC1zaXplPSIxNiI+UHJldmlldyBOb3QgQXZhaWxhYmxlPC90ZXh0Pjwvc3ZnPg=='">
                <div class="template-overlay">
                    <div class="template-actions">
                        <button class="template-btn preview-btn" title="معاينة سريعة" data-action="preview">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="template-btn select-btn" title="اختيار هذا القالب" data-action="select">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="template-btn favorite-btn" title="إضافة للمفضلة" data-action="favorite">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="template-btn info-btn" title="معلومات القالب" data-action="info">
                            <i class="fas fa-info"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="template-info">
                <div class="template-header">
                    <h3 class="template-name">${template.name}</h3>
                    <div class="template-rating">
                        <div class="stars">
                            ${this.generateStars(template.rating)}
                        </div>
                        <span class="rating-value">${template.rating}</span>
                    </div>
                </div>
                
                <p class="template-description">${template.description}</p>
                
                <div class="template-meta">
                    <div class="template-tags">
                        <span class="tag category-tag">${window.readyTemplatesDB.categories[template.category] || template.category}</span>
                        <span class="tag style-tag">${window.readyTemplatesDB.styles[template.style] || template.style}</span>
                    </div>
                    
                    <div class="template-stats">
                        <span class="stat">
                            <i class="fas fa-download"></i>
                            ${this.formatNumber(template.downloads)}
                        </span>
                        <span class="stat">
                            <i class="fas fa-heart"></i>
                            ${Math.floor(template.downloads * 0.1)}
                        </span>
                    </div>
                </div>
                
                <div class="template-features">
                    ${template.features.slice(0, 3).map(feature => 
                        `<span class="feature-tag"><i class="fas fa-check"></i> ${feature}</span>`
                    ).join('')}
                </div>
                
                <div class="template-actions-bottom">
                    <button class="template-action-btn preview-full" data-action="preview">
                        <i class="fas fa-expand"></i>
                        معاينة كاملة
                    </button>
                    <button class="template-action-btn select-template" data-action="select">
                        <i class="fas fa-plus"></i>
                        اختيار القالب
                    </button>
                </div>
            </div>
        `;
        
        // Add entrance animation class
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        
        return card;
    }
    
    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        
        let stars = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="fas fa-star"></i>';
        }
        
        // Half star
        if (hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="far fa-star"></i>';
        }
        
        return stars;
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    handleTemplateCardClick(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const action = e.target.closest('[data-action]')?.dataset.action;
        const card = e.target.closest('.template-card');
        const templateId = card?.dataset.templateId;
        
        if (!templateId) return;
        
        switch (action) {
            case 'preview':
                this.previewTemplate(templateId);
                break;
            case 'select':
                this.selectTemplate(templateId);
                break;
            case 'favorite':
                this.toggleFavorite(templateId, e.target.closest('.favorite-btn'));
                break;
            case 'info':
                this.showTemplateInfo(templateId);
                break;
            default:
                // Default click action - preview
                this.previewTemplate(templateId);
        }
    }
    
    previewTemplate(templateId) {
        const template = window.readyTemplatesDB.getTemplateById(templateId);
        if (!template) return;
        
        // Create and show preview modal
        this.showPreviewModal(template);
    }
    
    selectTemplate(templateId) {
        const template = window.readyTemplatesDB.getTemplateById(templateId);
        if (!template) return;
        
        // Notify the main app
        if (window.professionalApp) {
            window.professionalApp.selectTemplate(templateId);
        }
        
        // Show success animation
        this.showSelectionSuccess(template);
    }
    
    toggleFavorite(templateId, button) {
        const icon = button.querySelector('i');
        const isFavorited = icon.classList.contains('fas');
        
        if (isFavorited) {
            icon.classList.remove('fas');
            icon.classList.add('far');
            this.showNotification('تم إزالة القالب من المفضلة', 'info');
        } else {
            icon.classList.remove('far');
            icon.classList.add('fas');
            this.showNotification('تم إضافة القالب للمفضلة', 'success');
        }
        
        // Save to localStorage
        this.saveFavorite(templateId, !isFavorited);
    }
    
    showTemplateInfo(templateId) {
        const template = window.readyTemplatesDB.getTemplateById(templateId);
        if (!template) return;
        
        // Show detailed info modal
        this.showInfoModal(template);
    }
    
    showPreviewModal(template) {
        // Create modal HTML
        const modal = document.createElement('div');
        modal.className = 'template-preview-modal active';
        modal.innerHTML = `
            <div class="preview-modal-content">
                <button class="preview-modal-close">
                    <i class="fas fa-times"></i>
                </button>
                
                <div class="preview-modal-image">
                    <img src="${template.preview}" alt="${template.name}">
                </div>
                
                <div class="preview-modal-info">
                    <div class="preview-modal-header">
                        <h2 class="preview-modal-title">${template.name}</h2>
                        <div class="preview-modal-rating">
                            <div class="stars">${this.generateStars(template.rating)}</div>
                            <span class="rating-value">${template.rating}</span>
                            <span class="downloads-count">(${this.formatNumber(template.downloads)} تحميل)</span>
                        </div>
                        ${template.isPremium ? '<div class="premium-indicator"><i class="fas fa-crown"></i> قالب بريميوم</div>' : ''}
                    </div>
                    
                    <p class="preview-modal-description">${template.description}</p>
                    
                    <div class="preview-modal-features">
                        <h4>الميزات المتاحة:</h4>
                        <ul>
                            ${template.features.map(feature => `<li><i class="fas fa-check"></i> ${feature}</li>`).join('')}
                        </ul>
                    </div>
                    
                    <div class="preview-modal-tags">
                        <h4>التصنيفات:</h4>
                        <div class="tags-list">
                            ${template.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                    
                    <div class="preview-modal-actions">
                        <button class="preview-modal-btn secondary" onclick="this.closest('.template-preview-modal').remove()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                        <button class="preview-modal-btn primary" onclick="window.templatesDisplay.selectTemplate('${template.id}'); this.closest('.template-preview-modal').remove();">
                            <i class="fas fa-check"></i>
                            اختيار هذا القالب
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Add to page
        document.body.appendChild(modal);
        
        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        // Close button
        modal.querySelector('.preview-modal-close').addEventListener('click', () => {
            modal.remove();
        });
        
        // ESC key to close
        const handleEsc = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEsc);
            }
        };
        document.addEventListener('keydown', handleEsc);
    }
    
    showSelectionSuccess(template) {
        // Create success animation
        const success = document.createElement('div');
        success.className = 'selection-success';
        success.innerHTML = `
            <div class="success-content">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>تم اختيار القالب بنجاح!</h3>
                <p>قالب "${template.name}" جاهز للتحرير</p>
            </div>
        `;
        
        success.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            text-align: center;
            animation: successPop 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        `;
        
        document.body.appendChild(success);
        
        // Remove after animation
        setTimeout(() => {
            success.style.animation = 'successFadeOut 0.4s ease-in forwards';
            setTimeout(() => {
                if (success.parentNode) {
                    success.parentNode.removeChild(success);
                }
            }, 400);
        }, 2000);
    }
    
    changeView(view) {
        if (this.currentView === view) return;
        
        this.currentView = view;
        
        // Update view buttons
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');
        
        // Update grid class
        const grid = document.getElementById('templatesGrid');
        if (grid) {
            grid.className = `templates-grid view-${view}`;
        }
    }
    
    setupIntersectionObserver() {
        // Lazy loading for template images
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });
        
        // Observe template card entrance
        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('template-card-visible');
                    cardObserver.unobserve(entry.target);
                }
            });
        });
        
        // Store observers for later use
        this.imageObserver = imageObserver;
        this.cardObserver = cardObserver;
    }
    
    saveFavorite(templateId, isFavorite) {
        let favorites = JSON.parse(localStorage.getItem('elashrafy-favorites') || '[]');
        
        if (isFavorite) {
            if (!favorites.includes(templateId)) {
                favorites.push(templateId);
            }
        } else {
            favorites = favorites.filter(id => id !== templateId);
        }
        
        localStorage.setItem('elashrafy-favorites', JSON.stringify(favorites));
    }
    
    showNotification(message, type = 'info') {
        if (window.professionalApp && window.professionalApp.showNotification) {
            window.professionalApp.showNotification(message, type);
        }
    }
}

// Initialize templates display manager
const templatesDisplay = new TemplatesDisplayManager();

// Export for global access
window.templatesDisplay = templatesDisplay;

// Add success animation styles
const successStyles = document.createElement('style');
successStyles.textContent = `
    @keyframes successPop {
        0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }
        100% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }
    
    @keyframes successFadeOut {
        to {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
        }
    }
    
    .selection-success .success-icon {
        font-size: 48px;
        color: var(--success-color);
        margin-bottom: 16px;
    }
    
    .selection-success h3 {
        font-size: 24px;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 8px;
    }
    
    .selection-success p {
        color: var(--gray-600);
        font-size: 16px;
    }
    
    .template-card-visible {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
`;

document.head.appendChild(successStyles);
