// ELASHRAFY CV - Advanced Editor
// المحرر المتقدم - الأشرافي للسيرة الذاتية

class AdvancedEditor {
    constructor() {
        this.currentTemplate = null;
        this.cvData = {
            personalInfo: {
                name: '',
                title: '',
                email: '',
                phone: '',
                location: '',
                website: '',
                linkedin: '',
                photo: null
            },
            summary: '',
            experience: [],
            education: [],
            skills: [],
            languages: [],
            projects: [],
            certifications: [],
            references: []
        };
        this.isEditing = false;
        this.autoSaveInterval = null;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeEditor();
    }
    
    setupEventListeners() {
        // Auto-save functionality
        document.addEventListener('input', (e) => {
            if (e.target.closest('.editor-container')) {
                this.handleDataChange(e);
                this.scheduleAutoSave();
            }
        });
        
        // Section management
        document.addEventListener('click', (e) => {
            if (e.target.closest('.add-section-btn')) {
                this.addSection(e.target.closest('.add-section-btn').dataset.section);
            }
            
            if (e.target.closest('.remove-section-btn')) {
                this.removeSection(e.target.closest('.remove-section-btn'));
            }
            
            if (e.target.closest('.move-up-btn')) {
                this.moveSectionUp(e.target.closest('.move-up-btn'));
            }
            
            if (e.target.closest('.move-down-btn')) {
                this.moveSectionDown(e.target.closest('.move-down-btn'));
            }
        });
        
        // Photo upload
        document.addEventListener('change', (e) => {
            if (e.target.id === 'photoUpload') {
                this.handlePhotoUpload(e);
            }
        });
    }
    
    initializeEditor() {
        console.log('🎨 Advanced Editor initialized');
    }
    
    loadTemplate(template) {
        if (!template) return;
        
        this.currentTemplate = template;
        this.renderEditor();
        this.showNotification(`تم تحميل قالب "${template.name}" في المحرر`, 'success');
    }
    
    renderEditor() {
        const editorContainer = document.querySelector('#editorSection .editor-container');
        if (!editorContainer) return;
        
        editorContainer.innerHTML = `
            <div class="editor-layout">
                <div class="editor-sidebar">
                    <div class="editor-header">
                        <h3>
                            <i class="fas fa-edit"></i>
                            محرر السيرة الذاتية
                        </h3>
                        <div class="template-info">
                            <span class="template-name">${this.currentTemplate.name}</span>
                            <span class="template-category">${window.readyTemplatesDB.categories[this.currentTemplate.category]}</span>
                        </div>
                    </div>
                    
                    <div class="editor-sections">
                        ${this.renderEditorSections()}
                    </div>
                    
                    <div class="editor-actions">
                        <button class="editor-btn primary" onclick="advancedEditor.saveCV()">
                            <i class="fas fa-save"></i>
                            حفظ التغييرات
                        </button>
                        <button class="editor-btn secondary" onclick="advancedEditor.previewCV()">
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </button>
                        <button class="editor-btn success" onclick="advancedEditor.exportCV()">
                            <i class="fas fa-download"></i>
                            تصدير PDF
                        </button>
                    </div>
                </div>
                
                <div class="editor-main">
                    <div class="editor-toolbar">
                        <div class="toolbar-group">
                            <button class="toolbar-btn" title="تراجع" onclick="advancedEditor.undo()">
                                <i class="fas fa-undo"></i>
                            </button>
                            <button class="toolbar-btn" title="إعادة" onclick="advancedEditor.redo()">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                        
                        <div class="toolbar-group">
                            <select class="toolbar-select" id="fontFamily" onchange="advancedEditor.changeFontFamily(this.value)">
                                <option value="Cairo">Cairo</option>
                                <option value="Inter">Inter</option>
                                <option value="Roboto">Roboto</option>
                                <option value="Amiri">Amiri</option>
                                <option value="Playfair Display">Playfair Display</option>
                            </select>
                            
                            <select class="toolbar-select" id="fontSize" onchange="advancedEditor.changeFontSize(this.value)">
                                <option value="12">12px</option>
                                <option value="14" selected>14px</option>
                                <option value="16">16px</option>
                                <option value="18">18px</option>
                                <option value="20">20px</option>
                            </select>
                        </div>
                        
                        <div class="toolbar-group">
                            <button class="toolbar-btn" title="عريض" onclick="advancedEditor.toggleBold()">
                                <i class="fas fa-bold"></i>
                            </button>
                            <button class="toolbar-btn" title="مائل" onclick="advancedEditor.toggleItalic()">
                                <i class="fas fa-italic"></i>
                            </button>
                            <button class="toolbar-btn" title="تسطير" onclick="advancedEditor.toggleUnderline()">
                                <i class="fas fa-underline"></i>
                            </button>
                        </div>
                        
                        <div class="toolbar-group">
                            <input type="color" class="color-picker" id="textColor" title="لون النص" onchange="advancedEditor.changeTextColor(this.value)">
                            <input type="color" class="color-picker" id="backgroundColor" title="لون الخلفية" onchange="advancedEditor.changeBackgroundColor(this.value)">
                        </div>
                        
                        <div class="toolbar-group">
                            <button class="toolbar-btn" title="إضافة صورة" onclick="advancedEditor.addImage()">
                                <i class="fas fa-image"></i>
                            </button>
                            <button class="toolbar-btn" title="إضافة رابط" onclick="advancedEditor.addLink()">
                                <i class="fas fa-link"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="editor-content">
                        ${this.renderEditorContent()}
                    </div>
                </div>
                
                <div class="editor-preview">
                    <div class="preview-header">
                        <h4>
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </h4>
                        <div class="preview-controls">
                            <button class="preview-btn" onclick="advancedEditor.togglePreviewMode('desktop')" data-mode="desktop">
                                <i class="fas fa-desktop"></i>
                            </button>
                            <button class="preview-btn" onclick="advancedEditor.togglePreviewMode('tablet')" data-mode="tablet">
                                <i class="fas fa-tablet-alt"></i>
                            </button>
                            <button class="preview-btn" onclick="advancedEditor.togglePreviewMode('mobile')" data-mode="mobile">
                                <i class="fas fa-mobile-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="preview-content" id="livePreview">
                        ${this.renderLivePreview()}
                    </div>
                </div>
            </div>
        `;
        
        this.initializeEditorFeatures();
    }
    
    renderEditorSections() {
        return `
            <div class="editor-section active" data-section="personal">
                <div class="section-header">
                    <i class="fas fa-user"></i>
                    <span>المعلومات الشخصية</span>
                </div>
            </div>
            
            <div class="editor-section" data-section="summary">
                <div class="section-header">
                    <i class="fas fa-align-left"></i>
                    <span>الملخص المهني</span>
                </div>
            </div>
            
            <div class="editor-section" data-section="experience">
                <div class="section-header">
                    <i class="fas fa-briefcase"></i>
                    <span>الخبرة المهنية</span>
                </div>
            </div>
            
            <div class="editor-section" data-section="education">
                <div class="section-header">
                    <i class="fas fa-graduation-cap"></i>
                    <span>التعليم</span>
                </div>
            </div>
            
            <div class="editor-section" data-section="skills">
                <div class="section-header">
                    <i class="fas fa-cogs"></i>
                    <span>المهارات</span>
                </div>
            </div>
            
            <div class="editor-section" data-section="languages">
                <div class="section-header">
                    <i class="fas fa-language"></i>
                    <span>اللغات</span>
                </div>
            </div>
            
            <div class="editor-section" data-section="projects">
                <div class="section-header">
                    <i class="fas fa-project-diagram"></i>
                    <span>المشاريع</span>
                </div>
            </div>
            
            <div class="editor-section" data-section="certifications">
                <div class="section-header">
                    <i class="fas fa-certificate"></i>
                    <span>الشهادات</span>
                </div>
            </div>
        `;
    }
    
    renderEditorContent() {
        return `
            <div class="content-section active" id="personalContent">
                ${this.renderPersonalInfoForm()}
            </div>
            
            <div class="content-section" id="summaryContent">
                ${this.renderSummaryForm()}
            </div>
            
            <div class="content-section" id="experienceContent">
                ${this.renderExperienceForm()}
            </div>
            
            <div class="content-section" id="educationContent">
                ${this.renderEducationForm()}
            </div>
            
            <div class="content-section" id="skillsContent">
                ${this.renderSkillsForm()}
            </div>
            
            <div class="content-section" id="languagesContent">
                ${this.renderLanguagesForm()}
            </div>
            
            <div class="content-section" id="projectsContent">
                ${this.renderProjectsForm()}
            </div>
            
            <div class="content-section" id="certificationsContent">
                ${this.renderCertificationsForm()}
            </div>
        `;
    }
    
    renderPersonalInfoForm() {
        return `
            <div class="form-group">
                <label for="fullName">الاسم الكامل *</label>
                <input type="text" id="fullName" class="form-control" value="${this.cvData.personalInfo.name}" placeholder="أدخل اسمك الكامل">
            </div>
            
            <div class="form-group">
                <label for="jobTitle">المسمى الوظيفي *</label>
                <input type="text" id="jobTitle" class="form-control" value="${this.cvData.personalInfo.title}" placeholder="مثال: مطور ويب، مصمم جرافيك">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني *</label>
                    <input type="email" id="email" class="form-control" value="${this.cvData.personalInfo.email}" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="phone">رقم الهاتف *</label>
                    <input type="tel" id="phone" class="form-control" value="${this.cvData.personalInfo.phone}" placeholder="+966 50 123 4567">
                </div>
            </div>
            
            <div class="form-group">
                <label for="location">الموقع</label>
                <input type="text" id="location" class="form-control" value="${this.cvData.personalInfo.location}" placeholder="المدينة، البلد">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="website">الموقع الشخصي</label>
                    <input type="url" id="website" class="form-control" value="${this.cvData.personalInfo.website}" placeholder="https://yourwebsite.com">
                </div>
                
                <div class="form-group">
                    <label for="linkedin">LinkedIn</label>
                    <input type="url" id="linkedin" class="form-control" value="${this.cvData.personalInfo.linkedin}" placeholder="https://linkedin.com/in/yourprofile">
                </div>
            </div>
            
            <div class="form-group">
                <label for="photoUpload">الصورة الشخصية</label>
                <div class="photo-upload-area">
                    <input type="file" id="photoUpload" accept="image/*" style="display: none;">
                    <div class="photo-preview" onclick="document.getElementById('photoUpload').click()">
                        ${this.cvData.personalInfo.photo ? 
                            `<img src="${this.cvData.personalInfo.photo}" alt="الصورة الشخصية">` :
                            `<div class="photo-placeholder">
                                <i class="fas fa-camera"></i>
                                <span>اضغط لإضافة صورة</span>
                            </div>`
                        }
                    </div>
                    <div class="photo-controls">
                        <button type="button" class="btn-small" onclick="document.getElementById('photoUpload').click()">
                            <i class="fas fa-upload"></i>
                            رفع صورة
                        </button>
                        ${this.cvData.personalInfo.photo ? 
                            `<button type="button" class="btn-small danger" onclick="advancedEditor.removePhoto()">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>` : ''
                        }
                    </div>
                </div>
            </div>
        `;
    }
    
    renderSummaryForm() {
        return `
            <div class="form-group">
                <label for="professionalSummary">الملخص المهني</label>
                <textarea id="professionalSummary" class="form-control" rows="6" placeholder="اكتب ملخصاً مهنياً يبرز خبراتك ومهاراتك الرئيسية...">${this.cvData.summary}</textarea>
                <div class="form-help">
                    <i class="fas fa-lightbulb"></i>
                    نصيحة: اكتب 3-4 جمل تلخص خبرتك المهنية وأهدافك المهنية
                </div>
            </div>
            
            <div class="ai-assistant">
                <h4><i class="fas fa-robot"></i> مساعد الذكي لكتابة الملخص</h4>
                <p>دع الذكي الاصطناعي يساعدك في كتابة ملخص مهني مميز</p>
                <button class="btn secondary" onclick="advancedEditor.generateSummary()">
                    <i class="fas fa-magic"></i>
                    إنشاء ملخص بالذكاء الاصطناعي
                </button>
            </div>
        `;
    }
    
    renderExperienceForm() {
        let html = `
            <div class="section-intro">
                <h4>الخبرة المهنية</h4>
                <p>أضف خبراتك المهنية مرتبة من الأحدث إلى الأقدم</p>
            </div>
        `;
        
        this.cvData.experience.forEach((exp, index) => {
            html += this.renderExperienceItem(exp, index);
        });
        
        html += `
            <button class="add-section-btn" data-section="experience">
                <i class="fas fa-plus"></i>
                إضافة خبرة مهنية
            </button>
        `;
        
        return html;
    }
    
    renderExperienceItem(exp, index) {
        return `
            <div class="experience-item" data-index="${index}">
                <div class="item-header">
                    <h5>خبرة مهنية ${index + 1}</h5>
                    <div class="item-controls">
                        <button class="move-up-btn" title="تحريك لأعلى">
                            <i class="fas fa-arrow-up"></i>
                        </button>
                        <button class="move-down-btn" title="تحريك لأسفل">
                            <i class="fas fa-arrow-down"></i>
                        </button>
                        <button class="remove-section-btn" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>المسمى الوظيفي *</label>
                        <input type="text" class="form-control" data-field="title" value="${exp.title || ''}" placeholder="مثال: مطور ويب أول">
                    </div>
                    
                    <div class="form-group">
                        <label>اسم الشركة *</label>
                        <input type="text" class="form-control" data-field="company" value="${exp.company || ''}" placeholder="اسم الشركة أو المؤسسة">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>تاريخ البداية *</label>
                        <input type="month" class="form-control" data-field="startDate" value="${exp.startDate || ''}">
                    </div>
                    
                    <div class="form-group">
                        <label>تاريخ النهاية</label>
                        <input type="month" class="form-control" data-field="endDate" value="${exp.endDate || ''}" ${exp.current ? 'disabled' : ''}>
                        <label class="checkbox-label">
                            <input type="checkbox" data-field="current" ${exp.current ? 'checked' : ''}>
                            أعمل حالياً في هذا المنصب
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label>الموقع</label>
                    <input type="text" class="form-control" data-field="location" value="${exp.location || ''}" placeholder="المدينة، البلد">
                </div>
                
                <div class="form-group">
                    <label>وصف المهام والإنجازات</label>
                    <textarea class="form-control" data-field="description" rows="4" placeholder="اكتب وصفاً مفصلاً لمهامك وإنجازاتك في هذا المنصب...">${exp.description || ''}</textarea>
                </div>
            </div>
        `;
    }
    
    renderEducationForm() {
        // Similar structure to experience
        return `<div class="education-section">Education form will be here</div>`;
    }
    
    renderSkillsForm() {
        return `
            <div class="skills-section">
                <div class="section-intro">
                    <h4>المهارات</h4>
                    <p>أضف مهاراتك التقنية والشخصية</p>
                </div>
                
                <div class="skills-categories">
                    <div class="skill-category">
                        <h5>المهارات التقنية</h5>
                        <div class="skills-list" id="technicalSkills">
                            <!-- Skills will be rendered here -->
                        </div>
                        <button class="add-skill-btn" data-category="technical">
                            <i class="fas fa-plus"></i>
                            إضافة مهارة تقنية
                        </button>
                    </div>
                    
                    <div class="skill-category">
                        <h5>المهارات الشخصية</h5>
                        <div class="skills-list" id="softSkills">
                            <!-- Skills will be rendered here -->
                        </div>
                        <button class="add-skill-btn" data-category="soft">
                            <i class="fas fa-plus"></i>
                            إضافة مهارة شخصية
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    renderLanguagesForm() {
        return `<div class="languages-section">Languages form will be here</div>`;
    }
    
    renderProjectsForm() {
        return `<div class="projects-section">Projects form will be here</div>`;
    }
    
    renderCertificationsForm() {
        return `<div class="certifications-section">Certifications form will be here</div>`;
    }
    
    renderLivePreview() {
        if (!this.currentTemplate) {
            return `
                <div class="preview-placeholder">
                    <i class="fas fa-eye-slash"></i>
                    <p>اختر قالباً لرؤية المعاينة المباشرة</p>
                </div>
            `;
        }
        
        return `
            <div class="cv-preview" style="font-family: ${this.currentTemplate.fonts?.[0] || 'Cairo'}">
                <!-- Live preview will be rendered here based on template and data -->
                <div class="preview-loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>جاري تحديث المعاينة...</p>
                </div>
            </div>
        `;
    }
    
    initializeEditorFeatures() {
        // Initialize section navigation
        this.setupSectionNavigation();
        
        // Initialize auto-save
        this.setupAutoSave();
        
        // Initialize live preview updates
        this.setupLivePreview();
    }
    
    setupSectionNavigation() {
        document.querySelectorAll('.editor-section').forEach(section => {
            section.addEventListener('click', () => {
                const sectionName = section.dataset.section;
                this.switchToSection(sectionName);
            });
        });
    }
    
    switchToSection(sectionName) {
        // Update sidebar
        document.querySelectorAll('.editor-section').forEach(s => s.classList.remove('active'));
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
        
        // Update content
        document.querySelectorAll('.content-section').forEach(s => s.classList.remove('active'));
        document.getElementById(`${sectionName}Content`).classList.add('active');
    }
    
    setupAutoSave() {
        this.autoSaveInterval = setInterval(() => {
            this.autoSave();
        }, 30000); // Auto-save every 30 seconds
    }
    
    setupLivePreview() {
        // Update preview when data changes
        document.addEventListener('input', (e) => {
            if (e.target.closest('.editor-content')) {
                this.updateLivePreview();
            }
        });
    }
    
    handleDataChange(e) {
        const field = e.target.dataset.field;
        const section = e.target.closest('.content-section').id.replace('Content', '');
        const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
        
        // Update data model
        this.updateCVData(section, field, value, e.target);
    }
    
    updateCVData(section, field, value, element) {
        // Implementation for updating CV data structure
        console.log('Updating CV data:', { section, field, value });
    }
    
    scheduleAutoSave() {
        clearTimeout(this.autoSaveTimeout);
        this.autoSaveTimeout = setTimeout(() => {
            this.autoSave();
        }, 2000);
    }
    
    autoSave() {
        // Save to localStorage
        localStorage.setItem('elashrafy-cv-draft', JSON.stringify({
            template: this.currentTemplate?.id,
            data: this.cvData,
            timestamp: new Date().toISOString()
        }));
        
        console.log('✅ Auto-saved CV draft');
    }
    
    updateLivePreview() {
        // Update the live preview based on current data
        const previewContent = document.getElementById('livePreview');
        if (previewContent) {
            // Implementation for live preview updates
            console.log('🔄 Updating live preview...');
        }
    }
    
    // Toolbar functions
    changeFontFamily(fontFamily) {
        document.getElementById('livePreview').style.fontFamily = fontFamily;
        this.updateLivePreview();
    }
    
    changeFontSize(fontSize) {
        document.getElementById('livePreview').style.fontSize = fontSize + 'px';
        this.updateLivePreview();
    }
    
    toggleBold() {
        document.execCommand('bold');
        this.updateLivePreview();
    }
    
    toggleItalic() {
        document.execCommand('italic');
        this.updateLivePreview();
    }
    
    toggleUnderline() {
        document.execCommand('underline');
        this.updateLivePreview();
    }
    
    changeTextColor(color) {
        document.execCommand('foreColor', false, color);
        this.updateLivePreview();
    }
    
    changeBackgroundColor(color) {
        document.execCommand('backColor', false, color);
        this.updateLivePreview();
    }
    
    // Action functions
    saveCV() {
        this.autoSave();
        this.showNotification('تم حفظ السيرة الذاتية بنجاح!', 'success');
    }
    
    previewCV() {
        // Switch to preview section
        if (window.professionalApp) {
            window.professionalApp.showSection('preview');
        }
    }
    
    exportCV() {
        this.showNotification('جاري تصدير السيرة الذاتية...', 'info');
        
        // Implementation for PDF export
        setTimeout(() => {
            this.showNotification('تم تصدير السيرة الذاتية بنجاح!', 'success');
        }, 2000);
    }
    
    showNotification(message, type) {
        if (window.professionalApp && window.professionalApp.showNotification) {
            window.professionalApp.showNotification(message, type);
        }
    }
}

// Initialize advanced editor
const advancedEditor = new AdvancedEditor();

// Export for global access
window.advancedEditor = advancedEditor;
