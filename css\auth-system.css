/* ELASHRAFY CV - Authentication System Styles */
/* أنماط نظام المصادقة - الأشرافي للسيرة الذاتية */

/* Auth Trigger Button */
.auth-trigger-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.auth-trigger-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.auth-trigger-btn:hover::before {
    left: 100%;
}

.auth-trigger-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
}

.auth-trigger-btn.logged-in {
    background: linear-gradient(135deg, #10b981, #059669);
    padding: var(--spacing-1) var(--spacing-3);
}

.auth-trigger-btn .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.auth-trigger-btn .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Auth Modal */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.auth-modal.active {
    opacity: 1;
    visibility: visible;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
}

.auth-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    max-width: 450px;
    width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Auth Header */
.auth-header {
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    color: white;
    padding: var(--spacing-8);
    text-align: center;
    position: relative;
}

.auth-logo {
    margin-bottom: var(--spacing-4);
    display: flex;
    justify-content: center;
}

.auth-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    font-weight: 400;
}

.auth-close {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.auth-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Auth Tabs */
.auth-tabs {
    display: flex;
    background: var(--gray-100);
    margin: 0;
}

.auth-tab {
    flex: 1;
    padding: var(--spacing-4);
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.auth-tab.active {
    background: white;
    color: var(--primary-color);
}

.auth-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-color);
}

/* Auth Forms */
.auth-form {
    padding: var(--spacing-8);
    display: none;
    max-height: 60vh;
    overflow-y: auto;
}

.auth-form.active {
    display: block;
    animation: formSlideIn 0.3s ease-out;
}

@keyframes formSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-group {
    margin-bottom: var(--spacing-6);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
}

.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-icon i {
    position: absolute;
    left: var(--spacing-3);
    color: var(--gray-400);
    z-index: 2;
}

.input-with-icon input {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-3) var(--spacing-10);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    background: white;
}

.input-with-icon input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.input-with-icon input:focus + .password-toggle,
.input-with-icon input:focus ~ i {
    color: var(--primary-color);
}

.password-toggle {
    position: absolute;
    right: var(--spacing-3);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    z-index: 2;
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(59, 130, 246, 0.1);
}

/* Password Strength Indicator */
.password-strength {
    margin-top: var(--spacing-2);
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: var(--spacing-1);
}

.strength-fill {
    height: 100%;
    width: 0%;
    background: var(--gray-400);
    transition: all var(--transition-normal);
    border-radius: 2px;
}

.strength-text {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    font-weight: 500;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
    flex-wrap: wrap;
    gap: var(--spacing-2);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.terms-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.terms-link:hover {
    text-decoration: underline;
}

/* Auth Buttons */
.auth-btn {
    width: 100%;
    padding: var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    position: relative;
    overflow: hidden;
}

.auth-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.auth-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
}

.auth-btn.biometric {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    margin-top: var(--spacing-4);
}

.auth-btn.biometric:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(139, 92, 246, 0.4);
}

.auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.auth-btn.loading {
    pointer-events: none;
}

/* Divider */
.divider {
    text-align: center;
    margin: var(--spacing-6) 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gray-300);
}

.divider span {
    background: white;
    padding: 0 var(--spacing-4);
    color: var(--gray-500);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Social Login */
.social-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-3);
    margin-top: var(--spacing-4);
}

.social-btn {
    padding: var(--spacing-3);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: white;
    color: var(--gray-700);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.social-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.social-btn.google:hover {
    border-color: #ea4335;
    color: #ea4335;
}

.social-btn.microsoft:hover {
    border-color: #00a1f1;
    color: #00a1f1;
}

.social-btn.apple:hover {
    border-color: #000000;
    color: #000000;
}

.social-btn i {
    font-size: var(--font-size-lg);
}

/* User Dropdown */
.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.user-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-header {
    padding: var(--spacing-4);
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-bottom: 1px solid var(--gray-200);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.user-info img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: var(--shadow-sm);
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: var(--font-size-base);
}

.user-email {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.dropdown-menu {
    padding: var(--spacing-2);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--gray-100);
    color: var(--primary-color);
    transform: translateX(4px);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--gray-500);
}

.dropdown-item:hover i {
    color: var(--primary-color);
}

.dropdown-divider {
    height: 1px;
    background: var(--gray-200);
    margin: var(--spacing-2) 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        width: 95vw;
        max-height: 95vh;
    }
    
    .auth-header {
        padding: var(--spacing-6);
    }
    
    .auth-form {
        padding: var(--spacing-6);
    }
    
    .social-buttons {
        grid-template-columns: 1fr;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
    }
    
    .user-dropdown {
        right: -20px;
        min-width: 260px;
    }
}

/* RTL Support */
[dir="rtl"] .input-with-icon i {
    left: auto;
    right: var(--spacing-3);
}

[dir="rtl"] .input-with-icon input {
    padding: var(--spacing-3) var(--spacing-10) var(--spacing-3) var(--spacing-3);
}

[dir="rtl"] .password-toggle {
    right: auto;
    left: var(--spacing-3);
}

[dir="rtl"] .user-dropdown {
    right: auto;
    left: 0;
}

[dir="rtl"] .dropdown-item:hover {
    transform: translateX(-4px);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .auth-container {
        background: var(--gray-800);
        color: white;
    }
    
    .auth-form {
        background: var(--gray-800);
    }
    
    .input-with-icon input {
        background: var(--gray-700);
        border-color: var(--gray-600);
        color: white;
    }
    
    .user-dropdown {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }
}
