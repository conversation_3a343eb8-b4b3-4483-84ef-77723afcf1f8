// Photo Management System
class PhotoManager {
    constructor() {
        this.currentPhoto = null;
        this.photoData = null;
        this.photoSettings = {
            shape: 'circle',
            position: 'center',
            brightness: 0,
            contrast: 0,
            rotation: 0
        };
        
        this.init();
    }
    
    init() {
        this.setupPhotoUpload();
        this.setupPhotoEditor();
        this.setupPhotoOptions();
    }
    
    setupPhotoUpload() {
        const uploadBtn = document.getElementById('uploadPhotoBtn');
        const photoInput = document.getElementById('photoInput');
        const removeBtn = document.getElementById('removePhotoBtn');
        const photoPreview = document.getElementById('photoPreview');
        
        uploadBtn.addEventListener('click', () => {
            photoInput.click();
        });
        
        photoInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handlePhotoUpload(file);
            }
        });
        
        removeBtn.addEventListener('click', () => {
            this.removePhoto();
        });
        
        // Drag and drop functionality
        photoPreview.addEventListener('dragover', (e) => {
            e.preventDefault();
            photoPreview.classList.add('drag-over');
        });
        
        photoPreview.addEventListener('dragleave', () => {
            photoPreview.classList.remove('drag-over');
        });
        
        photoPreview.addEventListener('drop', (e) => {
            e.preventDefault();
            photoPreview.classList.remove('drag-over');
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                this.handlePhotoUpload(files[0]);
            }
        });
    }
    
    setupPhotoEditor() {
        const photoPreview = document.getElementById('photoPreview');
        const photoEditorModal = document.getElementById('photoEditorModal');
        const closeEditor = document.getElementById('closePhotoEditor');
        const applyChanges = document.getElementById('applyPhotoChanges');
        const resetPhoto = document.getElementById('resetPhoto');
        
        // Open editor on double click
        photoPreview.addEventListener('dblclick', () => {
            if (this.currentPhoto) {
                this.openPhotoEditor();
            }
        });
        
        closeEditor.addEventListener('click', () => {
            this.closePhotoEditor();
        });
        
        applyChanges.addEventListener('click', () => {
            this.applyPhotoChanges();
        });
        
        resetPhoto.addEventListener('click', () => {
            this.resetPhotoSettings();
        });
        
        // Setup sliders
        const brightnessSlider = document.getElementById('brightnessSlider');
        const contrastSlider = document.getElementById('contrastSlider');
        const rotationSlider = document.getElementById('rotationSlider');
        
        brightnessSlider.addEventListener('input', (e) => {
            this.photoSettings.brightness = parseInt(e.target.value);
            this.updatePhotoPreview();
        });
        
        contrastSlider.addEventListener('input', (e) => {
            this.photoSettings.contrast = parseInt(e.target.value);
            this.updatePhotoPreview();
        });
        
        rotationSlider.addEventListener('input', (e) => {
            this.photoSettings.rotation = parseInt(e.target.value);
            this.updatePhotoPreview();
        });
    }
    
    setupPhotoOptions() {
        const photoShape = document.getElementById('photoShape');
        const photoPosition = document.getElementById('photoPosition');
        
        photoShape.addEventListener('change', (e) => {
            this.photoSettings.shape = e.target.value;
            this.updatePhotoDisplay();
            this.updateCVPreview();
        });
        
        photoPosition.addEventListener('change', (e) => {
            this.photoSettings.position = e.target.value;
            this.updateCVPreview();
        });
    }
    
    handlePhotoUpload(file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            this.showNotification('Please select a valid image file', 'error');
            return;
        }
        
        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            this.showNotification('Image size should be less than 5MB', 'error');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            this.currentPhoto = e.target.result;
            this.photoData = {
                name: file.name,
                size: file.size,
                type: file.type,
                data: e.target.result
            };
            
            this.displayPhoto();
            this.showPhotoOptions();
            this.updateCVPreview();
            
            // Save to form data
            if (window.cvBuilder) {
                cvBuilder.formData.personal.photo = this.photoData;
                cvBuilder.formData.personal.photoSettings = this.photoSettings;
                cvBuilder.saveToLocalStorage();
            }
        };
        
        reader.readAsDataURL(file);
    }
    
    displayPhoto() {
        const photoPreview = document.getElementById('photoPreview');
        const removeBtn = document.getElementById('removePhotoBtn');
        
        photoPreview.innerHTML = `<img src="${this.currentPhoto}" alt="Profile Photo">`;
        this.updatePhotoDisplay();
        
        removeBtn.style.display = 'flex';
    }
    
    updatePhotoDisplay() {
        const photoPreview = document.getElementById('photoPreview');
        const img = photoPreview.querySelector('img');
        
        if (img) {
            // Apply shape
            photoPreview.className = `photo-preview ${this.photoSettings.shape}`;
            
            // Apply filters
            const filters = [];
            if (this.photoSettings.brightness !== 0) {
                filters.push(`brightness(${100 + this.photoSettings.brightness}%)`);
            }
            if (this.photoSettings.contrast !== 0) {
                filters.push(`contrast(${100 + this.photoSettings.contrast}%)`);
            }
            if (this.photoSettings.rotation !== 0) {
                filters.push(`rotate(${this.photoSettings.rotation}deg)`);
            }
            
            img.style.filter = filters.join(' ');
            img.style.transform = this.photoSettings.rotation !== 0 ? 
                `rotate(${this.photoSettings.rotation}deg)` : '';
        }
    }
    
    showPhotoOptions() {
        const photoOptions = document.getElementById('photoOptions');
        photoOptions.style.display = 'block';
    }
    
    removePhoto() {
        const photoPreview = document.getElementById('photoPreview');
        const removeBtn = document.getElementById('removePhotoBtn');
        const photoOptions = document.getElementById('photoOptions');
        const photoInput = document.getElementById('photoInput');
        
        photoPreview.innerHTML = `
            <div class="photo-placeholder">
                <i class="fas fa-camera"></i>
                <span data-translate="add_photo">Add Photo</span>
            </div>
        `;
        
        removeBtn.style.display = 'none';
        photoOptions.style.display = 'none';
        photoInput.value = '';
        
        this.currentPhoto = null;
        this.photoData = null;
        this.resetPhotoSettings();
        
        // Remove from form data
        if (window.cvBuilder) {
            delete cvBuilder.formData.personal.photo;
            delete cvBuilder.formData.personal.photoSettings;
            cvBuilder.saveToLocalStorage();
        }
        
        this.updateCVPreview();
    }
    
    openPhotoEditor() {
        const modal = document.getElementById('photoEditorModal');
        const canvas = document.getElementById('photoCanvas');
        const ctx = canvas.getContext('2d');
        
        modal.classList.add('active');
        
        // Load image to canvas
        const img = new Image();
        img.onload = () => {
            canvas.width = 400;
            canvas.height = 400;
            
            // Calculate dimensions to fit image in canvas
            const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
            const x = (canvas.width - img.width * scale) / 2;
            const y = (canvas.height - img.height * scale) / 2;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, x, y, img.width * scale, img.height * scale);
        };
        img.src = this.currentPhoto;
        
        // Set slider values
        document.getElementById('brightnessSlider').value = this.photoSettings.brightness;
        document.getElementById('contrastSlider').value = this.photoSettings.contrast;
        document.getElementById('rotationSlider').value = this.photoSettings.rotation;
    }
    
    closePhotoEditor() {
        const modal = document.getElementById('photoEditorModal');
        modal.classList.remove('active');
    }
    
    updatePhotoPreview() {
        const canvas = document.getElementById('photoCanvas');
        const ctx = canvas.getContext('2d');
        
        if (!this.currentPhoto) return;
        
        const img = new Image();
        img.onload = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Apply transformations
            ctx.save();
            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.rotate((this.photoSettings.rotation * Math.PI) / 180);
            
            // Apply filters
            ctx.filter = `brightness(${100 + this.photoSettings.brightness}%) contrast(${100 + this.photoSettings.contrast}%)`;
            
            const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
            const x = -img.width * scale / 2;
            const y = -img.height * scale / 2;
            
            ctx.drawImage(img, x, y, img.width * scale, img.height * scale);
            ctx.restore();
        };
        img.src = this.currentPhoto;
    }
    
    applyPhotoChanges() {
        this.updatePhotoDisplay();
        this.closePhotoEditor();
        this.updateCVPreview();
        
        // Save settings
        if (window.cvBuilder) {
            cvBuilder.formData.personal.photoSettings = this.photoSettings;
            cvBuilder.saveToLocalStorage();
        }
    }
    
    resetPhotoSettings() {
        this.photoSettings = {
            shape: 'circle',
            position: 'center',
            brightness: 0,
            contrast: 0,
            rotation: 0
        };
        
        // Reset UI
        document.getElementById('photoShape').value = 'circle';
        document.getElementById('photoPosition').value = 'center';
        document.getElementById('brightnessSlider').value = 0;
        document.getElementById('contrastSlider').value = 0;
        document.getElementById('rotationSlider').value = 0;
        
        this.updatePhotoDisplay();
    }
    
    updateCVPreview() {
        if (window.cvApp) {
            cvApp.updatePreview();
        }
    }
    
    showNotification(message, type = 'info') {
        if (window.cvApp) {
            cvApp.showNotification(message, type);
        }
    }
    
    // Get photo data for CV generation
    getPhotoData() {
        if (!this.currentPhoto) return null;
        
        return {
            data: this.currentPhoto,
            settings: this.photoSettings
        };
    }
    
    // Load photo from saved data
    loadPhoto(photoData, photoSettings) {
        if (photoData) {
            this.currentPhoto = photoData.data;
            this.photoData = photoData;
            this.photoSettings = photoSettings || this.photoSettings;
            
            this.displayPhoto();
            this.showPhotoOptions();
            
            // Update UI controls
            document.getElementById('photoShape').value = this.photoSettings.shape;
            document.getElementById('photoPosition').value = this.photoSettings.position;
        }
    }
}

// Initialize photo manager
const photoManager = new PhotoManager();

// Export for global access
window.photoManager = photoManager;
