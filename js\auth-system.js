// ELASHRAFY CV - Advanced Authentication System
// نظام المصادقة المتقدم - الأشرافي للسيرة الذاتية

class AuthenticationSystem {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.biometricSupported = false;
        this.sessionTimeout = 30 * 60 * 1000; // 30 minutes
        this.sessionTimer = null;
        
        this.init();
    }
    
    async init() {
        await this.checkBiometricSupport();
        this.setupAuthUI();
        this.checkExistingSession();
        this.setupSessionManagement();
        this.loadUserPreferences();
    }
    
    async checkBiometricSupport() {
        try {
            if (window.PublicKeyCredential && 
                await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()) {
                this.biometricSupported = true;
                console.log('✅ Biometric authentication supported');
            } else {
                console.log('❌ Biometric authentication not supported');
            }
        } catch (error) {
            console.log('❌ Error checking biometric support:', error);
        }
    }
    
    setupAuthUI() {
        this.createAuthModal();
        this.setupAuthButtons();
        this.setupFormValidation();
    }
    
    createAuthModal() {
        const authModalHTML = `
            <div class="auth-modal" id="authModal">
                <div class="auth-overlay" id="authOverlay"></div>
                <div class="auth-container">
                    <div class="auth-header">
                        <div class="auth-logo">
                            <svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
                                <rect width="50" height="50" rx="10" fill="url(#authGradient)"/>
                                <path d="M15 12h20a2 2 0 0 1 2 2v20a2 2 0 0 1-2 2H15a2 2 0 0 1-2-2V14a2 2 0 0 1 2-2z" fill="white" opacity="0.9"/>
                                <path d="M20 20h10M20 25h10M20 30h8" stroke="url(#authGradient2)" stroke-width="2" stroke-linecap="round"/>
                                <circle cx="25" cy="17" r="2" fill="url(#authGradient2)"/>
                                <defs>
                                    <linearGradient id="authGradient" x1="0" y1="0" x2="50" y2="50" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#1e40af"/>
                                        <stop offset="1" stop-color="#3b82f6"/>
                                    </linearGradient>
                                    <linearGradient id="authGradient2" x1="0" y1="0" x2="50" y2="50" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#1e40af"/>
                                        <stop offset="1" stop-color="#8b5cf6"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <h2 class="auth-title" data-translate="welcome_elashrafy">مرحباً بك في الأشرافي CV</h2>
                        <p class="auth-subtitle" data-translate="secure_login">تسجيل دخول آمن ومتقدم</p>
                        <button class="auth-close" id="authClose">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="auth-tabs">
                        <button class="auth-tab active" data-tab="login" data-translate="login">تسجيل الدخول</button>
                        <button class="auth-tab" data-tab="register" data-translate="register">إنشاء حساب</button>
                    </div>
                    
                    <!-- Login Form -->
                    <div class="auth-form active" id="loginForm">
                        <div class="form-group">
                            <label data-translate="email_username">البريد الإلكتروني أو اسم المستخدم</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user"></i>
                                <input type="text" id="loginEmail" placeholder="أدخل بريدك الإلكتروني" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label data-translate="password">كلمة المرور</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="loginPassword" placeholder="أدخل كلمة المرور" required>
                                <button type="button" class="password-toggle" id="toggleLoginPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="rememberMe">
                                <span class="checkmark"></span>
                                <span data-translate="remember_me">تذكرني</span>
                            </label>
                            <a href="#" class="forgot-password" data-translate="forgot_password">نسيت كلمة المرور؟</a>
                        </div>
                        
                        <button type="submit" class="auth-btn primary" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i>
                            <span data-translate="login">تسجيل الدخول</span>
                        </button>
                        
                        <div class="biometric-section" id="biometricLogin" style="display: none;">
                            <div class="divider">
                                <span data-translate="or">أو</span>
                            </div>
                            <button type="button" class="auth-btn biometric" id="biometricLoginBtn">
                                <i class="fas fa-fingerprint"></i>
                                <span data-translate="login_fingerprint">تسجيل الدخول بالبصمة</span>
                            </button>
                        </div>
                        
                        <div class="social-login">
                            <div class="divider">
                                <span data-translate="or_continue_with">أو تابع مع</span>
                            </div>
                            <div class="social-buttons">
                                <button class="social-btn google" id="googleLogin">
                                    <i class="fab fa-google"></i>
                                    <span>Google</span>
                                </button>
                                <button class="social-btn microsoft" id="microsoftLogin">
                                    <i class="fab fa-microsoft"></i>
                                    <span>Microsoft</span>
                                </button>
                                <button class="social-btn apple" id="appleLogin">
                                    <i class="fab fa-apple"></i>
                                    <span>Apple</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Register Form -->
                    <div class="auth-form" id="registerForm">
                        <div class="form-group">
                            <label data-translate="full_name">الاسم الكامل</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user"></i>
                                <input type="text" id="registerName" placeholder="أدخل اسمك الكامل" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label data-translate="email">البريد الإلكتروني</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="registerEmail" placeholder="أدخل بريدك الإلكتروني" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label data-translate="password">كلمة المرور</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="registerPassword" placeholder="أدخل كلمة مرور قوية" required>
                                <button type="button" class="password-toggle" id="toggleRegisterPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="passwordStrength">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <span class="strength-text">قوة كلمة المرور</span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label data-translate="confirm_password">تأكيد كلمة المرور</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور" required>
                            </div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="agreeTerms" required>
                                <span class="checkmark"></span>
                                <span data-translate="agree_terms">أوافق على <a href="#" class="terms-link">الشروط والأحكام</a></span>
                            </label>
                        </div>
                        
                        <button type="submit" class="auth-btn primary" id="registerBtn">
                            <i class="fas fa-user-plus"></i>
                            <span data-translate="create_account">إنشاء حساب</span>
                        </button>
                        
                        <div class="biometric-section" id="biometricRegister" style="display: none;">
                            <div class="divider">
                                <span data-translate="secure_with">تأمين إضافي</span>
                            </div>
                            <button type="button" class="auth-btn biometric" id="setupBiometricBtn">
                                <i class="fas fa-fingerprint"></i>
                                <span data-translate="setup_fingerprint">إعداد البصمة</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', authModalHTML);
    }
    
    setupAuthButtons() {
        // Show auth modal button
        const authButton = document.createElement('button');
        authButton.className = 'auth-trigger-btn';
        authButton.innerHTML = `
            <i class="fas fa-user"></i>
            <span data-translate="login">تسجيل الدخول</span>
        `;
        authButton.addEventListener('click', () => this.showAuthModal());
        
        // Add to header
        const header = document.querySelector('.app-header .header-controls');
        if (header) {
            header.insertBefore(authButton, header.firstChild);
        }
        
        // Setup modal events
        this.setupModalEvents();
        this.setupTabSwitching();
        this.setupFormSubmissions();
        this.setupBiometricButtons();
        this.setupSocialLogin();
    }
    
    setupModalEvents() {
        const modal = document.getElementById('authModal');
        const overlay = document.getElementById('authOverlay');
        const closeBtn = document.getElementById('authClose');
        
        [overlay, closeBtn].forEach(element => {
            element.addEventListener('click', () => this.hideAuthModal());
        });
        
        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                this.hideAuthModal();
            }
        });
    }
    
    setupTabSwitching() {
        const tabs = document.querySelectorAll('.auth-tab');
        const forms = document.querySelectorAll('.auth-form');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;
                
                // Update active tab
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Update active form
                forms.forEach(form => {
                    form.classList.remove('active');
                    if (form.id === `${targetTab}Form`) {
                        form.classList.add('active');
                    }
                });
                
                // Show/hide biometric options
                this.updateBiometricVisibility();
            });
        });
    }
    
    setupFormSubmissions() {
        // Login form
        document.getElementById('loginBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
        
        // Register form
        document.getElementById('registerBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.handleRegister();
        });
        
        // Password toggles
        this.setupPasswordToggles();
        
        // Password strength checker
        this.setupPasswordStrength();
    }
    
    setupPasswordToggles() {
        const toggles = document.querySelectorAll('.password-toggle');
        
        toggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                const input = toggle.parentNode.querySelector('input');
                const icon = toggle.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    input.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            });
        });
    }
    
    setupPasswordStrength() {
        const passwordInput = document.getElementById('registerPassword');
        const strengthIndicator = document.getElementById('passwordStrength');
        
        passwordInput.addEventListener('input', () => {
            const strength = this.calculatePasswordStrength(passwordInput.value);
            this.updatePasswordStrengthUI(strength);
        });
    }
    
    calculatePasswordStrength(password) {
        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password),
            arabic: /[\u0600-\u06FF]/.test(password)
        };
        
        Object.values(checks).forEach(check => {
            if (check) score++;
        });
        
        return {
            score,
            level: score < 3 ? 'weak' : score < 5 ? 'medium' : 'strong',
            checks
        };
    }
    
    updatePasswordStrengthUI(strength) {
        const strengthIndicator = document.getElementById('passwordStrength');
        const fill = strengthIndicator.querySelector('.strength-fill');
        const text = strengthIndicator.querySelector('.strength-text');
        
        const colors = {
            weak: '#ef4444',
            medium: '#f59e0b',
            strong: '#10b981'
        };
        
        const labels = {
            weak: 'ضعيفة',
            medium: 'متوسطة',
            strong: 'قوية'
        };
        
        fill.style.width = `${(strength.score / 6) * 100}%`;
        fill.style.backgroundColor = colors[strength.level];
        text.textContent = `كلمة مرور ${labels[strength.level]}`;
        text.style.color = colors[strength.level];
    }
    
    async setupBiometricButtons() {
        if (!this.biometricSupported) return;
        
        // Show biometric sections
        document.getElementById('biometricLogin').style.display = 'block';
        document.getElementById('biometricRegister').style.display = 'block';
        
        // Setup biometric login
        document.getElementById('biometricLoginBtn').addEventListener('click', () => {
            this.handleBiometricLogin();
        });
        
        // Setup biometric registration
        document.getElementById('setupBiometricBtn').addEventListener('click', () => {
            this.setupBiometricAuth();
        });
    }
    
    updateBiometricVisibility() {
        if (!this.biometricSupported) return;
        
        const activeTab = document.querySelector('.auth-tab.active').dataset.tab;
        const biometricSections = document.querySelectorAll('.biometric-section');
        
        biometricSections.forEach(section => {
            section.style.display = 'block';
        });
    }
    
    showAuthModal() {
        const modal = document.getElementById('authModal');
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Focus first input
        setTimeout(() => {
            const firstInput = modal.querySelector('input');
            if (firstInput) firstInput.focus();
        }, 300);
    }
    
    hideAuthModal() {
        const modal = document.getElementById('authModal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    async handleLogin() {
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;
        const rememberMe = document.getElementById('rememberMe').checked;
        
        if (!this.validateLoginForm(email, password)) return;
        
        try {
            this.showLoadingState('loginBtn');
            
            // Simulate API call
            await this.delay(1500);
            
            const user = await this.authenticateUser(email, password);
            
            if (user) {
                await this.loginSuccess(user, rememberMe);
            } else {
                this.showError('بيانات تسجيل الدخول غير صحيحة');
            }
        } catch (error) {
            this.showError('حدث خطأ أثناء تسجيل الدخول');
        } finally {
            this.hideLoadingState('loginBtn');
        }
    }
    
    async handleRegister() {
        const name = document.getElementById('registerName').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const agreeTerms = document.getElementById('agreeTerms').checked;
        
        if (!this.validateRegisterForm(name, email, password, confirmPassword, agreeTerms)) return;
        
        try {
            this.showLoadingState('registerBtn');
            
            // Simulate API call
            await this.delay(2000);
            
            const user = await this.createUser(name, email, password);
            
            if (user) {
                await this.registrationSuccess(user);
            } else {
                this.showError('فشل في إنشاء الحساب');
            }
        } catch (error) {
            this.showError('حدث خطأ أثناء إنشاء الحساب');
        } finally {
            this.hideLoadingState('registerBtn');
        }
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    validateLoginForm(email, password) {
        if (!email || !password) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        if (!this.isValidEmail(email) && !this.isValidUsername(email)) {
            this.showError('يرجى إدخال بريد إلكتروني أو اسم مستخدم صحيح');
            return false;
        }

        return true;
    }

    validateRegisterForm(name, email, password, confirmPassword, agreeTerms) {
        if (!name || !email || !password || !confirmPassword) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }

        if (!this.isValidEmail(email)) {
            this.showError('يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }

        if (password !== confirmPassword) {
            this.showError('كلمات المرور غير متطابقة');
            return false;
        }

        const strength = this.calculatePasswordStrength(password);
        if (strength.score < 3) {
            this.showError('كلمة المرور ضعيفة جداً. يرجى اختيار كلمة مرور أقوى');
            return false;
        }

        if (!agreeTerms) {
            this.showError('يجب الموافقة على الشروط والأحكام');
            return false;
        }

        return true;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isValidUsername(username) {
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        return usernameRegex.test(username);
    }

    async authenticateUser(email, password) {
        // Simulate user authentication
        const users = this.getStoredUsers();
        const user = users.find(u =>
            (u.email === email || u.username === email) &&
            u.password === this.hashPassword(password)
        );

        return user || null;
    }

    async createUser(name, email, password) {
        const users = this.getStoredUsers();

        // Check if user already exists
        if (users.find(u => u.email === email)) {
            throw new Error('المستخدم موجود بالفعل');
        }

        const newUser = {
            id: this.generateUserId(),
            name,
            email,
            username: this.generateUsername(email),
            password: this.hashPassword(password),
            createdAt: new Date().toISOString(),
            isVerified: false,
            preferences: {
                language: languageManager.getCurrentLanguage(),
                theme: 'modern',
                notifications: true
            },
            subscription: {
                type: 'free',
                templatesUsed: 0,
                exportsThisMonth: 0
            }
        };

        users.push(newUser);
        this.saveUsers(users);

        return newUser;
    }

    async loginSuccess(user, rememberMe) {
        this.currentUser = user;
        this.isLoggedIn = true;

        // Save session
        const sessionData = {
            userId: user.id,
            loginTime: Date.now(),
            rememberMe
        };

        if (rememberMe) {
            localStorage.setItem('elashrafy-session', JSON.stringify(sessionData));
        } else {
            sessionStorage.setItem('elashrafy-session', JSON.stringify(sessionData));
        }

        // Update UI
        this.updateUIForLoggedInUser();
        this.hideAuthModal();
        this.startSessionTimer();

        // Show welcome message
        this.showSuccess(`مرحباً بك ${user.name}! تم تسجيل الدخول بنجاح`);

        // Load user's CV data
        await this.loadUserCVData();
    }

    async registrationSuccess(user) {
        // Auto-login after registration
        await this.loginSuccess(user, false);

        // Show welcome message for new user
        this.showSuccess(`مرحباً ${user.name}! تم إنشاء حسابك بنجاح`);

        // Show onboarding
        setTimeout(() => {
            this.showOnboarding();
        }, 2000);
    }

    async handleBiometricLogin() {
        if (!this.biometricSupported) {
            this.showError('المصادقة البيومترية غير مدعومة على هذا الجهاز');
            return;
        }

        try {
            this.showLoadingState('biometricLoginBtn');

            const credential = await navigator.credentials.get({
                publicKey: {
                    challenge: new Uint8Array(32),
                    allowCredentials: this.getStoredCredentials(),
                    userVerification: 'required',
                    timeout: 60000
                }
            });

            if (credential) {
                const user = await this.verifyBiometricCredential(credential);
                if (user) {
                    await this.loginSuccess(user, true);
                } else {
                    this.showError('فشل في التحقق من البصمة');
                }
            }
        } catch (error) {
            console.error('Biometric login error:', error);
            this.showError('فشل في تسجيل الدخول بالبصمة');
        } finally {
            this.hideLoadingState('biometricLoginBtn');
        }
    }

    async setupBiometricAuth() {
        if (!this.biometricSupported) {
            this.showError('المصادقة البيومترية غير مدعومة على هذا الجهاز');
            return;
        }

        try {
            this.showLoadingState('setupBiometricBtn');

            const credential = await navigator.credentials.create({
                publicKey: {
                    challenge: new Uint8Array(32),
                    rp: {
                        name: "ELASHRAFY CV",
                        id: window.location.hostname
                    },
                    user: {
                        id: new TextEncoder().encode(this.currentUser?.id || 'temp-user'),
                        name: this.currentUser?.email || '<EMAIL>',
                        displayName: this.currentUser?.name || 'User'
                    },
                    pubKeyCredParams: [{
                        type: "public-key",
                        alg: -7
                    }],
                    authenticatorSelection: {
                        authenticatorAttachment: "platform",
                        userVerification: "required"
                    },
                    timeout: 60000
                }
            });

            if (credential) {
                await this.storeBiometricCredential(credential);
                this.showSuccess('تم إعداد المصادقة بالبصمة بنجاح!');
            }
        } catch (error) {
            console.error('Biometric setup error:', error);
            this.showError('فشل في إعداد المصادقة بالبصمة');
        } finally {
            this.hideLoadingState('setupBiometricBtn');
        }
    }

    updateUIForLoggedInUser() {
        const authButton = document.querySelector('.auth-trigger-btn');
        if (authButton && this.currentUser) {
            authButton.innerHTML = `
                <div class="user-avatar">
                    <img src="${this.currentUser.avatar || this.generateAvatar(this.currentUser.name)}" alt="User Avatar">
                </div>
                <span>${this.currentUser.name}</span>
                <i class="fas fa-chevron-down"></i>
            `;
            authButton.classList.add('logged-in');

            // Add dropdown menu
            this.createUserDropdown(authButton);
        }
    }

    createUserDropdown(button) {
        const dropdown = document.createElement('div');
        dropdown.className = 'user-dropdown';
        dropdown.innerHTML = `
            <div class="dropdown-header">
                <div class="user-info">
                    <img src="${this.currentUser.avatar || this.generateAvatar(this.currentUser.name)}" alt="Avatar">
                    <div>
                        <div class="user-name">${this.currentUser.name}</div>
                        <div class="user-email">${this.currentUser.email}</div>
                    </div>
                </div>
            </div>
            <div class="dropdown-menu">
                <a href="#" class="dropdown-item" id="userProfile">
                    <i class="fas fa-user"></i>
                    <span data-translate="profile">الملف الشخصي</span>
                </a>
                <a href="#" class="dropdown-item" id="userSettings">
                    <i class="fas fa-cog"></i>
                    <span data-translate="settings">الإعدادات</span>
                </a>
                <a href="#" class="dropdown-item" id="userSubscription">
                    <i class="fas fa-crown"></i>
                    <span data-translate="subscription">الاشتراك</span>
                </a>
                <div class="dropdown-divider"></div>
                <a href="#" class="dropdown-item" id="userLogout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span data-translate="logout">تسجيل الخروج</span>
                </a>
            </div>
        `;

        button.appendChild(dropdown);

        // Setup dropdown events
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdown.classList.toggle('active');
        });

        document.addEventListener('click', () => {
            dropdown.classList.remove('active');
        });

        // Setup menu items
        document.getElementById('userLogout').addEventListener('click', (e) => {
            e.preventDefault();
            this.logout();
        });
    }

    generateAvatar(name) {
        const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
        const colors = ['#3b82f6', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444'];
        const color = colors[name.length % colors.length];

        return `data:image/svg+xml;base64,${btoa(`
            <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="20" r="20" fill="${color}"/>
                <text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial" font-size="14" font-weight="bold">${initials}</text>
            </svg>
        `)}`;
    }

    logout() {
        this.currentUser = null;
        this.isLoggedIn = false;

        // Clear session
        localStorage.removeItem('elashrafy-session');
        sessionStorage.removeItem('elashrafy-session');

        // Clear session timer
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
        }

        // Reset UI
        this.resetUIForLoggedOutUser();

        // Show message
        this.showSuccess('تم تسجيل الخروج بنجاح');

        // Clear user data
        this.clearUserCVData();
    }

    resetUIForLoggedOutUser() {
        const authButton = document.querySelector('.auth-trigger-btn');
        if (authButton) {
            authButton.innerHTML = `
                <i class="fas fa-user"></i>
                <span data-translate="login">تسجيل الدخول</span>
            `;
            authButton.classList.remove('logged-in');

            // Remove dropdown
            const dropdown = authButton.querySelector('.user-dropdown');
            if (dropdown) {
                dropdown.remove();
            }
        }
    }

    // Utility methods
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateUsername(email) {
        return email.split('@')[0] + '_' + Math.random().toString(36).substr(2, 4);
    }

    hashPassword(password) {
        // Simple hash for demo - use proper hashing in production
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    getStoredUsers() {
        const users = localStorage.getItem('elashrafy-users');
        return users ? JSON.parse(users) : [];
    }

    saveUsers(users) {
        localStorage.setItem('elashrafy-users', JSON.stringify(users));
    }

    showLoadingState(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = true;
            button.classList.add('loading');
            const originalText = button.innerHTML;
            button.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                <span>جاري المعالجة...</span>
            `;
            button.dataset.originalText = originalText;
        }
    }

    hideLoadingState(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = false;
            button.classList.remove('loading');
            button.innerHTML = button.dataset.originalText || button.innerHTML;
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        if (window.cvApp) {
            cvApp.showNotification(message, type);
        }
    }

    checkExistingSession() {
        const sessionData = localStorage.getItem('elashrafy-session') ||
                           sessionStorage.getItem('elashrafy-session');

        if (sessionData) {
            try {
                const session = JSON.parse(sessionData);
                const users = this.getStoredUsers();
                const user = users.find(u => u.id === session.userId);

                if (user) {
                    this.currentUser = user;
                    this.isLoggedIn = true;
                    this.updateUIForLoggedInUser();
                    this.startSessionTimer();
                    this.loadUserCVData();
                }
            } catch (error) {
                console.error('Error loading session:', error);
            }
        }
    }

    startSessionTimer() {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
        }

        this.sessionTimer = setTimeout(() => {
            this.showSessionExpiredWarning();
        }, this.sessionTimeout);
    }

    showSessionExpiredWarning() {
        if (confirm('انتهت صلاحية جلستك. هل تريد تجديدها؟')) {
            this.startSessionTimer();
        } else {
            this.logout();
        }
    }

    async loadUserCVData() {
        if (!this.currentUser) return;

        const userCVData = localStorage.getItem(`elashrafy-cv-${this.currentUser.id}`);
        if (userCVData && window.cvBuilder) {
            try {
                const cvData = JSON.parse(userCVData);
                cvBuilder.formData = cvData;
                cvBuilder.populateForm();

                if (window.cvApp) {
                    cvApp.updatePreview();
                }
            } catch (error) {
                console.error('Error loading user CV data:', error);
            }
        }
    }

    clearUserCVData() {
        if (window.cvBuilder) {
            cvBuilder.formData = cvBuilder.getDefaultFormData();
            cvBuilder.populateForm();

            if (window.cvApp) {
                cvApp.updatePreview();
            }
        }
    }

    saveUserCVData() {
        if (!this.currentUser || !window.cvBuilder) return;

        const cvData = cvBuilder.getFormData();
        localStorage.setItem(`elashrafy-cv-${this.currentUser.id}`, JSON.stringify(cvData));
    }

    loadUserPreferences() {
        if (!this.currentUser) return;

        const preferences = this.currentUser.preferences;
        if (preferences) {
            // Apply language preference
            if (preferences.language && window.languageManager) {
                languageManager.setLanguage(preferences.language);
            }

            // Apply theme preference
            if (preferences.theme && window.cvApp) {
                cvApp.changeTheme(preferences.theme);
            }
        }
    }

    showOnboarding() {
        // Show welcome tour for new users
        const onboardingSteps = [
            {
                target: '.template-gallery-btn',
                title: 'اختر قالبك المفضل',
                content: 'تصفح أكثر من 400 قالب احترافي'
            },
            {
                target: '.photo-upload-section',
                title: 'أضف صورتك الشخصية',
                content: 'ارفع صورة احترافية وعدلها بأدوات متقدمة'
            },
            {
                target: '.export-btn',
                title: 'صدر سيرتك الذاتية',
                content: 'احصل على ملف PDF عالي الجودة'
            }
        ];

        // Implementation of onboarding tour would go here
        console.log('Onboarding steps:', onboardingSteps);
    }
}

// Initialize authentication system
const authSystem = new AuthenticationSystem();

// Export for global access
window.authSystem = authSystem;
