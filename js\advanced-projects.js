// ELASHRAFY CV - Advanced Projects System
// نظام المشاريع المتقدم - الأشرافي للسيرة الذاتية

class AdvancedProjectsSystem {
    constructor() {
        this.projects = [];
        this.projectTemplates = this.getProjectTemplates();
        this.skillsDatabase = this.getSkillsDatabase();
        this.currentProject = null;
        
        this.init();
    }
    
    init() {
        this.setupProjectsUI();
        this.setupProjectModal();
        this.setupProjectTemplates();
        this.loadSavedProjects();
    }
    
    setupProjectsUI() {
        // Find projects section in CV form
        const projectsSection = document.querySelector('[data-section="projects"]');
        if (!projectsSection) return;
        
        // Enhanced projects header
        const projectsHeader = projectsSection.querySelector('h3');
        if (projectsHeader) {
            projectsHeader.innerHTML = `
                <i class="fas fa-project-diagram"></i>
                <span data-translate="projects">المشاريع</span>
                <div class="projects-actions">
                    <button class="action-btn" id="addProjectBtn" title="إضافة مشروع">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="action-btn" id="projectTemplatesBtn" title="قوالب المشاريع">
                        <i class="fas fa-layer-group"></i>
                    </button>
                    <button class="action-btn" id="importProjectBtn" title="استيراد مشروع">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            `;
        }
        
        // Enhanced projects container
        const projectsContainer = projectsSection.querySelector('.projects-container') || 
                                 projectsSection.querySelector('.form-section');
        
        if (projectsContainer) {
            projectsContainer.innerHTML = `
                <div class="projects-grid" id="projectsGrid">
                    <!-- Projects will be added here -->
                </div>
                
                <div class="projects-stats" id="projectsStats">
                    <div class="stat-item">
                        <div class="stat-number" id="totalProjects">0</div>
                        <div class="stat-label">إجمالي المشاريع</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="completedProjects">0</div>
                        <div class="stat-label">مشاريع مكتملة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="uniqueSkills">0</div>
                        <div class="stat-label">مهارات مستخدمة</div>
                    </div>
                </div>
                
                <div class="projects-insights" id="projectsInsights">
                    <h4>رؤى المشاريع</h4>
                    <div class="insights-grid" id="insightsGrid">
                        <!-- Insights will be generated here -->
                    </div>
                </div>
            `;
        }
        
        this.setupProjectsEvents();
    }
    
    setupProjectsEvents() {
        // Add project button
        document.getElementById('addProjectBtn')?.addEventListener('click', () => {
            this.openProjectModal();
        });
        
        // Project templates button
        document.getElementById('projectTemplatesBtn')?.addEventListener('click', () => {
            this.openProjectTemplatesModal();
        });
        
        // Import project button
        document.getElementById('importProjectBtn')?.addEventListener('click', () => {
            this.importProject();
        });
    }
    
    setupProjectModal() {
        const modalHTML = `
            <div class="project-modal" id="projectModal">
                <div class="modal-overlay" id="projectModalOverlay"></div>
                <div class="modal-container">
                    <div class="modal-header">
                        <h3>
                            <i class="fas fa-project-diagram"></i>
                            <span id="projectModalTitle">إضافة مشروع جديد</span>
                        </h3>
                        <button class="modal-close" id="closeProjectModal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <form id="projectForm" class="project-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم المشروع *</label>
                                    <input type="text" id="projectName" required placeholder="أدخل اسم المشروع">
                                </div>
                                
                                <div class="form-group">
                                    <label>نوع المشروع</label>
                                    <select id="projectType">
                                        <option value="web">تطوير ويب</option>
                                        <option value="mobile">تطبيق جوال</option>
                                        <option value="desktop">تطبيق سطح المكتب</option>
                                        <option value="ai">ذكاء اصطناعي</option>
                                        <option value="data">علوم البيانات</option>
                                        <option value="design">تصميم</option>
                                        <option value="research">بحث</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>وصف المشروع *</label>
                                <textarea id="projectDescription" required placeholder="اكتب وصفاً مفصلاً للمشروع وأهدافه"></textarea>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label>تاريخ البداية</label>
                                    <input type="date" id="projectStartDate">
                                </div>
                                
                                <div class="form-group">
                                    <label>تاريخ الانتهاء</label>
                                    <input type="date" id="projectEndDate">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label>حالة المشروع</label>
                                    <select id="projectStatus">
                                        <option value="completed">مكتمل</option>
                                        <option value="in-progress">قيد التطوير</option>
                                        <option value="planned">مخطط</option>
                                        <option value="on-hold">متوقف مؤقتاً</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label>نسبة الإنجاز (%)</label>
                                    <input type="range" id="projectProgress" min="0" max="100" value="100">
                                    <span class="progress-value" id="progressValue">100%</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>التقنيات المستخدمة</label>
                                <div class="skills-input-container">
                                    <input type="text" id="projectSkills" placeholder="ابدأ بكتابة اسم التقنية...">
                                    <div class="skills-suggestions" id="skillsSuggestions"></div>
                                </div>
                                <div class="selected-skills" id="selectedSkills"></div>
                            </div>
                            
                            <div class="form-group">
                                <label>روابط المشروع</label>
                                <div class="links-container" id="projectLinks">
                                    <div class="link-input">
                                        <select class="link-type">
                                            <option value="github">GitHub</option>
                                            <option value="demo">عرض تجريبي</option>
                                            <option value="website">موقع ويب</option>
                                            <option value="documentation">توثيق</option>
                                            <option value="video">فيديو</option>
                                            <option value="other">أخرى</option>
                                        </select>
                                        <input type="url" placeholder="رابط المشروع" class="link-url">
                                        <button type="button" class="remove-link">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="add-link-btn" id="addLinkBtn">
                                    <i class="fas fa-plus"></i>
                                    إضافة رابط
                                </button>
                            </div>
                            
                            <div class="form-group">
                                <label>الإنجازات والنتائج</label>
                                <div class="achievements-container" id="projectAchievements">
                                    <div class="achievement-input">
                                        <input type="text" placeholder="إنجاز أو نتيجة مهمة" class="achievement-text">
                                        <button type="button" class="remove-achievement">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <button type="button" class="add-achievement-btn" id="addAchievementBtn">
                                    <i class="fas fa-plus"></i>
                                    إضافة إنجاز
                                </button>
                            </div>
                            
                            <div class="form-group">
                                <label>صور المشروع</label>
                                <div class="project-images" id="projectImages">
                                    <div class="image-upload-area" id="imageUploadArea">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>اسحب الصور هنا أو انقر للاختيار</p>
                                        <input type="file" id="projectImageInput" multiple accept="image/*" hidden>
                                    </div>
                                    <div class="uploaded-images" id="uploadedImages"></div>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="button" class="btn secondary" id="cancelProject">إلغاء</button>
                                <button type="submit" class="btn primary" id="saveProject">
                                    <i class="fas fa-save"></i>
                                    حفظ المشروع
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.setupProjectModalEvents();
    }
    
    setupProjectModalEvents() {
        // Modal close events
        document.getElementById('closeProjectModal').addEventListener('click', () => {
            this.closeProjectModal();
        });
        
        document.getElementById('projectModalOverlay').addEventListener('click', () => {
            this.closeProjectModal();
        });
        
        document.getElementById('cancelProject').addEventListener('click', () => {
            this.closeProjectModal();
        });
        
        // Form submission
        document.getElementById('projectForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProject();
        });
        
        // Progress slider
        document.getElementById('projectProgress').addEventListener('input', (e) => {
            document.getElementById('progressValue').textContent = `${e.target.value}%`;
        });
        
        // Skills input with autocomplete
        this.setupSkillsInput();
        
        // Links management
        this.setupLinksManagement();
        
        // Achievements management
        this.setupAchievementsManagement();
        
        // Image upload
        this.setupImageUpload();
    }
    
    setupSkillsInput() {
        const skillsInput = document.getElementById('projectSkills');
        const suggestions = document.getElementById('skillsSuggestions');
        const selectedSkills = document.getElementById('selectedSkills');
        let currentSkills = [];
        
        skillsInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            if (query.length < 2) {
                suggestions.style.display = 'none';
                return;
            }
            
            const matches = this.skillsDatabase.filter(skill => 
                skill.toLowerCase().includes(query) && 
                !currentSkills.includes(skill)
            ).slice(0, 8);
            
            if (matches.length > 0) {
                suggestions.innerHTML = matches.map(skill => 
                    `<div class="skill-suggestion" data-skill="${skill}">${skill}</div>`
                ).join('');
                suggestions.style.display = 'block';
            } else {
                suggestions.style.display = 'none';
            }
        });
        
        suggestions.addEventListener('click', (e) => {
            if (e.target.classList.contains('skill-suggestion')) {
                const skill = e.target.dataset.skill;
                this.addSkillToProject(skill, currentSkills, selectedSkills);
                skillsInput.value = '';
                suggestions.style.display = 'none';
            }
        });
        
        skillsInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && skillsInput.value.trim()) {
                e.preventDefault();
                const skill = skillsInput.value.trim();
                if (!currentSkills.includes(skill)) {
                    this.addSkillToProject(skill, currentSkills, selectedSkills);
                    skillsInput.value = '';
                    suggestions.style.display = 'none';
                }
            }
        });
    }
    
    addSkillToProject(skill, currentSkills, container) {
        currentSkills.push(skill);
        
        const skillTag = document.createElement('div');
        skillTag.className = 'skill-tag';
        skillTag.innerHTML = `
            <span>${skill}</span>
            <button type="button" class="remove-skill" data-skill="${skill}">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        skillTag.querySelector('.remove-skill').addEventListener('click', () => {
            const index = currentSkills.indexOf(skill);
            if (index > -1) {
                currentSkills.splice(index, 1);
                skillTag.remove();
            }
        });
        
        container.appendChild(skillTag);
    }
    
    setupLinksManagement() {
        document.getElementById('addLinkBtn').addEventListener('click', () => {
            this.addLinkInput();
        });
        
        // Setup remove link events for existing inputs
        this.setupLinkRemoveEvents();
    }
    
    addLinkInput() {
        const container = document.getElementById('projectLinks');
        const linkInput = document.createElement('div');
        linkInput.className = 'link-input';
        linkInput.innerHTML = `
            <select class="link-type">
                <option value="github">GitHub</option>
                <option value="demo">عرض تجريبي</option>
                <option value="website">موقع ويب</option>
                <option value="documentation">توثيق</option>
                <option value="video">فيديو</option>
                <option value="other">أخرى</option>
            </select>
            <input type="url" placeholder="رابط المشروع" class="link-url">
            <button type="button" class="remove-link">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(linkInput);
        this.setupLinkRemoveEvents();
    }
    
    setupLinkRemoveEvents() {
        document.querySelectorAll('.remove-link').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.target.closest('.link-input').remove();
            });
        });
    }
    
    setupAchievementsManagement() {
        document.getElementById('addAchievementBtn').addEventListener('click', () => {
            this.addAchievementInput();
        });
        
        this.setupAchievementRemoveEvents();
    }
    
    addAchievementInput() {
        const container = document.getElementById('projectAchievements');
        const achievementInput = document.createElement('div');
        achievementInput.className = 'achievement-input';
        achievementInput.innerHTML = `
            <input type="text" placeholder="إنجاز أو نتيجة مهمة" class="achievement-text">
            <button type="button" class="remove-achievement">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(achievementInput);
        this.setupAchievementRemoveEvents();
    }
    
    setupAchievementRemoveEvents() {
        document.querySelectorAll('.remove-achievement').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.target.closest('.achievement-input').remove();
            });
        });
    }
    
    setupImageUpload() {
        const uploadArea = document.getElementById('imageUploadArea');
        const fileInput = document.getElementById('projectImageInput');
        const uploadedImages = document.getElementById('uploadedImages');
        
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
            this.handleImageFiles(files, uploadedImages);
        });
        
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleImageFiles(files, uploadedImages);
        });
    }
    
    handleImageFiles(files, container) {
        files.forEach(file => {
            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                alert(`الملف ${file.name} كبير جداً. الحد الأقصى 5 ميجابايت.`);
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                const imagePreview = document.createElement('div');
                imagePreview.className = 'image-preview';
                imagePreview.innerHTML = `
                    <img src="${e.target.result}" alt="Project Image">
                    <button type="button" class="remove-image">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                imagePreview.querySelector('.remove-image').addEventListener('click', () => {
                    imagePreview.remove();
                });
                
                container.appendChild(imagePreview);
            };
            reader.readAsDataURL(file);
        });
    }
    
    openProjectModal(project = null) {
        this.currentProject = project;
        const modal = document.getElementById('projectModal');
        const title = document.getElementById('projectModalTitle');
        
        if (project) {
            title.textContent = 'تعديل المشروع';
            this.populateProjectForm(project);
        } else {
            title.textContent = 'إضافة مشروع جديد';
            this.resetProjectForm();
        }
        
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    closeProjectModal() {
        const modal = document.getElementById('projectModal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
        this.currentProject = null;
    }
    
    saveProject() {
        const formData = this.getProjectFormData();

        if (!this.validateProjectForm(formData)) {
            return;
        }

        if (this.currentProject) {
            // Update existing project
            const index = this.projects.findIndex(p => p.id === this.currentProject.id);
            if (index !== -1) {
                this.projects[index] = { ...formData, id: this.currentProject.id };
            }
        } else {
            // Add new project
            formData.id = this.generateProjectId();
            formData.createdAt = new Date().toISOString();
            this.projects.push(formData);
        }

        this.saveProjectsToStorage();
        this.renderProjects();
        this.updateProjectsStats();
        this.generateProjectsInsights();
        this.closeProjectModal();

        // Show success message
        this.showNotification(
            this.currentProject ? 'تم تحديث المشروع بنجاح!' : 'تم إضافة المشروع بنجاح!',
            'success'
        );
    }

    getProjectFormData() {
        const links = Array.from(document.querySelectorAll('.link-input')).map(input => ({
            type: input.querySelector('.link-type').value,
            url: input.querySelector('.link-url').value
        })).filter(link => link.url);

        const achievements = Array.from(document.querySelectorAll('.achievement-text'))
            .map(input => input.value.trim())
            .filter(achievement => achievement);

        const skills = Array.from(document.querySelectorAll('.skill-tag span'))
            .map(span => span.textContent);

        const images = Array.from(document.querySelectorAll('.image-preview img'))
            .map(img => img.src);

        return {
            name: document.getElementById('projectName').value.trim(),
            type: document.getElementById('projectType').value,
            description: document.getElementById('projectDescription').value.trim(),
            startDate: document.getElementById('projectStartDate').value,
            endDate: document.getElementById('projectEndDate').value,
            status: document.getElementById('projectStatus').value,
            progress: parseInt(document.getElementById('projectProgress').value),
            skills,
            links,
            achievements,
            images,
            updatedAt: new Date().toISOString()
        };
    }

    validateProjectForm(data) {
        if (!data.name) {
            this.showNotification('يرجى إدخال اسم المشروع', 'error');
            return false;
        }

        if (!data.description) {
            this.showNotification('يرجى إدخال وصف المشروع', 'error');
            return false;
        }

        if (data.startDate && data.endDate && new Date(data.startDate) > new Date(data.endDate)) {
            this.showNotification('تاريخ البداية يجب أن يكون قبل تاريخ الانتهاء', 'error');
            return false;
        }

        return true;
    }

    populateProjectForm(project) {
        document.getElementById('projectName').value = project.name || '';
        document.getElementById('projectType').value = project.type || 'web';
        document.getElementById('projectDescription').value = project.description || '';
        document.getElementById('projectStartDate').value = project.startDate || '';
        document.getElementById('projectEndDate').value = project.endDate || '';
        document.getElementById('projectStatus').value = project.status || 'completed';
        document.getElementById('projectProgress').value = project.progress || 100;
        document.getElementById('progressValue').textContent = `${project.progress || 100}%`;

        // Populate skills
        const selectedSkills = document.getElementById('selectedSkills');
        selectedSkills.innerHTML = '';
        if (project.skills) {
            project.skills.forEach(skill => {
                this.addSkillToProject(skill, project.skills, selectedSkills);
            });
        }

        // Populate links
        const linksContainer = document.getElementById('projectLinks');
        linksContainer.innerHTML = '';
        if (project.links && project.links.length > 0) {
            project.links.forEach(link => {
                this.addLinkInput();
                const lastLink = linksContainer.lastElementChild;
                lastLink.querySelector('.link-type').value = link.type;
                lastLink.querySelector('.link-url').value = link.url;
            });
        } else {
            this.addLinkInput();
        }

        // Populate achievements
        const achievementsContainer = document.getElementById('projectAchievements');
        achievementsContainer.innerHTML = '';
        if (project.achievements && project.achievements.length > 0) {
            project.achievements.forEach(achievement => {
                this.addAchievementInput();
                const lastAchievement = achievementsContainer.lastElementChild;
                lastAchievement.querySelector('.achievement-text').value = achievement;
            });
        } else {
            this.addAchievementInput();
        }

        // Populate images
        const uploadedImages = document.getElementById('uploadedImages');
        uploadedImages.innerHTML = '';
        if (project.images) {
            project.images.forEach(imageSrc => {
                const imagePreview = document.createElement('div');
                imagePreview.className = 'image-preview';
                imagePreview.innerHTML = `
                    <img src="${imageSrc}" alt="Project Image">
                    <button type="button" class="remove-image">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                imagePreview.querySelector('.remove-image').addEventListener('click', () => {
                    imagePreview.remove();
                });

                uploadedImages.appendChild(imagePreview);
            });
        }
    }

    resetProjectForm() {
        document.getElementById('projectForm').reset();
        document.getElementById('progressValue').textContent = '100%';
        document.getElementById('selectedSkills').innerHTML = '';
        document.getElementById('uploadedImages').innerHTML = '';

        // Reset links to one empty input
        const linksContainer = document.getElementById('projectLinks');
        linksContainer.innerHTML = '';
        this.addLinkInput();

        // Reset achievements to one empty input
        const achievementsContainer = document.getElementById('projectAchievements');
        achievementsContainer.innerHTML = '';
        this.addAchievementInput();
    }

    renderProjects() {
        const grid = document.getElementById('projectsGrid');
        if (!grid) return;

        if (this.projects.length === 0) {
            grid.innerHTML = `
                <div class="no-projects">
                    <i class="fas fa-project-diagram"></i>
                    <h4>لا توجد مشاريع بعد</h4>
                    <p>ابدأ بإضافة مشروعك الأول لإظهار خبراتك وإنجازاتك</p>
                    <button class="btn primary" onclick="window.advancedProjects.openProjectModal()">
                        <i class="fas fa-plus"></i>
                        إضافة مشروع
                    </button>
                </div>
            `;
            return;
        }

        grid.innerHTML = this.projects.map(project => this.createProjectCard(project)).join('');
    }

    createProjectCard(project) {
        const statusColors = {
            completed: '#10b981',
            'in-progress': '#f59e0b',
            planned: '#6b7280',
            'on-hold': '#ef4444'
        };

        const statusLabels = {
            completed: 'مكتمل',
            'in-progress': 'قيد التطوير',
            planned: 'مخطط',
            'on-hold': 'متوقف مؤقتاً'
        };

        const typeIcons = {
            web: 'fas fa-globe',
            mobile: 'fas fa-mobile-alt',
            desktop: 'fas fa-desktop',
            ai: 'fas fa-brain',
            data: 'fas fa-chart-bar',
            design: 'fas fa-palette',
            research: 'fas fa-microscope',
            other: 'fas fa-cog'
        };

        return `
            <div class="project-card" data-project-id="${project.id}">
                <div class="project-header">
                    <div class="project-type">
                        <i class="${typeIcons[project.type] || typeIcons.other}"></i>
                    </div>
                    <div class="project-status" style="background: ${statusColors[project.status]}">
                        ${statusLabels[project.status]}
                    </div>
                </div>

                <div class="project-content">
                    <h4 class="project-name">${project.name}</h4>
                    <p class="project-description">${this.truncateText(project.description, 100)}</p>

                    ${project.progress < 100 ? `
                        <div class="project-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${project.progress}%"></div>
                            </div>
                            <span class="progress-text">${project.progress}%</span>
                        </div>
                    ` : ''}

                    ${project.skills && project.skills.length > 0 ? `
                        <div class="project-skills">
                            ${project.skills.slice(0, 3).map(skill =>
                                `<span class="skill-tag">${skill}</span>`
                            ).join('')}
                            ${project.skills.length > 3 ? `<span class="more-skills">+${project.skills.length - 3}</span>` : ''}
                        </div>
                    ` : ''}

                    ${project.links && project.links.length > 0 ? `
                        <div class="project-links">
                            ${project.links.slice(0, 3).map(link =>
                                `<a href="${link.url}" target="_blank" class="project-link" title="${link.type}">
                                    <i class="${this.getLinkIcon(link.type)}"></i>
                                </a>`
                            ).join('')}
                        </div>
                    ` : ''}
                </div>

                <div class="project-actions">
                    <button class="action-btn" onclick="window.advancedProjects.viewProject('${project.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn" onclick="window.advancedProjects.editProject('${project.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn" onclick="window.advancedProjects.duplicateProject('${project.id}')" title="نسخ">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="action-btn danger" onclick="window.advancedProjects.deleteProject('${project.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    getLinkIcon(type) {
        const icons = {
            github: 'fab fa-github',
            demo: 'fas fa-external-link-alt',
            website: 'fas fa-globe',
            documentation: 'fas fa-book',
            video: 'fas fa-video',
            other: 'fas fa-link'
        };
        return icons[type] || icons.other;
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    }

    updateProjectsStats() {
        const totalProjects = this.projects.length;
        const completedProjects = this.projects.filter(p => p.status === 'completed').length;
        const uniqueSkills = [...new Set(this.projects.flatMap(p => p.skills || []))].length;

        document.getElementById('totalProjects').textContent = totalProjects;
        document.getElementById('completedProjects').textContent = completedProjects;
        document.getElementById('uniqueSkills').textContent = uniqueSkills;
    }

    generateProjectsInsights() {
        const insights = this.calculateInsights();
        const grid = document.getElementById('insightsGrid');

        if (!grid || insights.length === 0) return;

        grid.innerHTML = insights.map(insight => `
            <div class="insight-card">
                <div class="insight-icon">
                    <i class="${insight.icon}"></i>
                </div>
                <div class="insight-content">
                    <h5>${insight.title}</h5>
                    <p>${insight.description}</p>
                </div>
            </div>
        `).join('');
    }

    calculateInsights() {
        if (this.projects.length === 0) return [];

        const insights = [];

        // Most used technology
        const skillCounts = {};
        this.projects.forEach(project => {
            if (project.skills) {
                project.skills.forEach(skill => {
                    skillCounts[skill] = (skillCounts[skill] || 0) + 1;
                });
            }
        });

        const mostUsedSkill = Object.keys(skillCounts).reduce((a, b) =>
            skillCounts[a] > skillCounts[b] ? a : b, ''
        );

        if (mostUsedSkill) {
            insights.push({
                icon: 'fas fa-star',
                title: 'التقنية الأكثر استخداماً',
                description: `${mostUsedSkill} - استخدمت في ${skillCounts[mostUsedSkill]} مشروع`
            });
        }

        // Project completion rate
        const completionRate = Math.round((this.projects.filter(p => p.status === 'completed').length / this.projects.length) * 100);
        insights.push({
            icon: 'fas fa-chart-line',
            title: 'معدل إنجاز المشاريع',
            description: `${completionRate}% من مشاريعك مكتملة`
        });

        // Average project duration
        const projectsWithDates = this.projects.filter(p => p.startDate && p.endDate);
        if (projectsWithDates.length > 0) {
            const avgDuration = projectsWithDates.reduce((sum, project) => {
                const start = new Date(project.startDate);
                const end = new Date(project.endDate);
                return sum + (end - start);
            }, 0) / projectsWithDates.length;

            const avgDays = Math.round(avgDuration / (1000 * 60 * 60 * 24));
            insights.push({
                icon: 'fas fa-clock',
                title: 'متوسط مدة المشروع',
                description: `${avgDays} يوم في المتوسط`
            });
        }

        return insights;
    }

    viewProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (!project) return;

        // Create detailed view modal
        this.showProjectDetailsModal(project);
    }

    editProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (project) {
            this.openProjectModal(project);
        }
    }

    duplicateProject(projectId) {
        const project = this.projects.find(p => p.id === projectId);
        if (!project) return;

        const duplicatedProject = {
            ...project,
            id: this.generateProjectId(),
            name: `${project.name} (نسخة)`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.projects.push(duplicatedProject);
        this.saveProjectsToStorage();
        this.renderProjects();
        this.updateProjectsStats();

        this.showNotification('تم نسخ المشروع بنجاح!', 'success');
    }

    deleteProject(projectId) {
        if (!confirm('هل أنت متأكد من حذف هذا المشروع؟')) return;

        const index = this.projects.findIndex(p => p.id === projectId);
        if (index !== -1) {
            this.projects.splice(index, 1);
            this.saveProjectsToStorage();
            this.renderProjects();
            this.updateProjectsStats();
            this.generateProjectsInsights();

            this.showNotification('تم حذف المشروع بنجاح!', 'success');
        }
    }

    generateProjectId() {
        return 'project_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    saveProjectsToStorage() {
        try {
            localStorage.setItem('elashrafy-projects', JSON.stringify(this.projects));
        } catch (error) {
            console.error('Error saving projects:', error);
        }
    }

    loadSavedProjects() {
        try {
            const saved = localStorage.getItem('elashrafy-projects');
            if (saved) {
                this.projects = JSON.parse(saved);
                this.renderProjects();
                this.updateProjectsStats();
                this.generateProjectsInsights();
            }
        } catch (error) {
            console.error('Error loading projects:', error);
        }
    }

    getProjectTemplates() {
        return [
            {
                name: 'مشروع تطوير ويب',
                type: 'web',
                description: 'موقع ويب تفاعلي مع واجهة مستخدم حديثة',
                skills: ['HTML', 'CSS', 'JavaScript', 'React', 'Node.js'],
                achievements: ['تحسين الأداء بنسبة 40%', 'زيادة المستخدمين بنسبة 60%']
            },
            {
                name: 'تطبيق جوال',
                type: 'mobile',
                description: 'تطبيق جوال متعدد المنصات',
                skills: ['React Native', 'Firebase', 'Redux'],
                achievements: ['أكثر من 10,000 تحميل', 'تقييم 4.8 نجوم']
            }
        ];
    }

    getSkillsDatabase() {
        return [
            // Programming Languages
            'JavaScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift',
            'Kotlin', 'TypeScript', 'Dart', 'Scala', 'R', 'MATLAB', 'Perl', 'Lua', 'Haskell',

            // Web Technologies
            'HTML', 'CSS', 'React', 'Vue.js', 'Angular', 'Node.js', 'Express.js', 'Next.js',
            'Nuxt.js', 'Svelte', 'jQuery', 'Bootstrap', 'Tailwind CSS', 'Sass', 'Less',

            // Mobile Development
            'React Native', 'Flutter', 'Ionic', 'Xamarin', 'Cordova', 'Android', 'iOS',

            // Databases
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server',
            'Firebase', 'DynamoDB', 'Cassandra', 'Neo4j',

            // Cloud & DevOps
            'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'Jenkins', 'GitLab CI',
            'GitHub Actions', 'Terraform', 'Ansible',

            // AI & ML
            'TensorFlow', 'PyTorch', 'Scikit-learn', 'Pandas', 'NumPy', 'OpenCV', 'Keras',

            // Design
            'Figma', 'Adobe XD', 'Sketch', 'Photoshop', 'Illustrator', 'InDesign'
        ];
    }

    showNotification(message, type = 'info') {
        if (window.cvApp) {
            cvApp.showNotification(message, type);
        }
    }
}

// Initialize advanced projects system
const advancedProjects = new AdvancedProjectsSystem();

// Export for global access
window.advancedProjects = advancedProjects;
