/* ELASHRAFY CV - Advanced Features Styles */

/* Premium Brand Enhancements */
:root {
    /* ELASHRAFY Brand Colors */
    --elashrafy-primary: #1e40af;
    --elashrafy-secondary: #3b82f6;
    --elashrafy-accent: #8b5cf6;
    --elashrafy-gold: #f59e0b;
    --elashrafy-success: #10b981;
    --elashrafy-error: #ef4444;
    
    /* Premium Gradients */
    --gradient-primary: linear-gradient(135deg, #1e40af, #3b82f6);
    --gradient-accent: linear-gradient(135deg, #8b5cf6, #3b82f6);
    --gradient-gold: linear-gradient(135deg, #f59e0b, #d97706);
    --gradient-success: linear-gradient(135deg, #10b981, #059669);
    
    /* Advanced Shadows */
    --shadow-premium: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-floating: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    
    /* Premium Typography */
    --font-display: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-body: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
}

/* Enhanced Logo Animations */
.logo-icon svg {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo:hover .logo-icon svg {
    transform: scale(1.05) rotate(2deg);
    filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3));
}

.logo-main {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Premium Button Enhancements */
.template-gallery-btn {
    background: var(--gradient-accent);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.template-gallery-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.template-gallery-btn:hover::before {
    left: 100%;
}

.template-gallery-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-floating);
}

/* Advanced Template Card Styles */
.template-card {
    background: var(--white);
    border: 2px solid transparent;
    border-radius: var(--radius-xl);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    transform-style: preserve-3d;
}

.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    border-radius: var(--radius-xl);
}

.template-card:hover {
    transform: translateY(-8px) rotateX(5deg);
    box-shadow: var(--shadow-premium);
    border-color: var(--elashrafy-primary);
}

.template-card:hover::before {
    opacity: 0.05;
}

.template-preview-img {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.template-preview-img::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.template-card:hover .template-preview-img::after {
    transform: translateX(100%);
}

/* Premium Badge Enhancements */
.premium-badge {
    background: var(--gradient-gold);
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: 0 0 0 var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: var(--shadow-md);
    position: relative;
}

.premium-badge::before {
    content: '✨';
    margin-right: var(--spacing-1);
}

.premium-badge::after {
    content: '';
    position: absolute;
    bottom: -4px;
    right: -4px;
    width: 8px;
    height: 8px;
    background: var(--gradient-gold);
    transform: rotate(45deg);
}

/* Enhanced Photo Upload Area */
.photo-upload-section {
    background: linear-gradient(135deg, #f8fafc, #ffffff);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.photo-upload-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-upload-section:hover {
    border-color: var(--elashrafy-primary);
    background: linear-gradient(135deg, #eff6ff, #ffffff);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.photo-upload-section:hover::before {
    opacity: 1;
}

.photo-preview {
    border: 3px solid var(--gray-200);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.photo-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.photo-preview:hover {
    border-color: var(--elashrafy-primary);
    box-shadow: var(--shadow-glow);
    transform: scale(1.02);
}

.photo-preview:hover::before {
    transform: translateX(100%);
}

/* Advanced Modal Enhancements */
.template-gallery-modal,
.photo-editor-modal {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.modal-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-premium);
    animation: modalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #f8fafc, #ffffff);
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
    backdrop-filter: blur(10px);
}

/* Enhanced Category Buttons */
.category-btn {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.5);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.category-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left 0.3s ease;
    z-index: -1;
}

.category-btn:hover::before,
.category-btn.active::before {
    left: 0;
}

.category-btn:hover,
.category-btn.active {
    color: white;
    border-color: var(--elashrafy-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Advanced Loading States */
.loading-overlay.active {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.loading-spinner {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.loading-spinner i {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enhanced Success/Error States */
.notification {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: notificationSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes notificationSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* Premium Form Enhancements */
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--elashrafy-primary);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1), var(--shadow-glow);
    transform: translateY(-1px);
}

.form-section.active {
    animation: sectionSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes sectionSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Advanced Progress Indicator */
.progress-fill {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .template-card:hover {
        transform: translateY(-4px);
    }
    
    .photo-preview:hover {
        transform: scale(1.01);
    }
    
    .modal-content {
        margin: 5vh auto;
        width: 95vw;
        height: 90vh;
    }
}

/* Dark Mode Support (Future) */
@media (prefers-color-scheme: dark) {
    :root {
        --elashrafy-primary: #3b82f6;
        --elashrafy-secondary: #60a5fa;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .template-card {
        border-width: 3px;
    }
    
    .category-btn {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
