// PDF Export Functionality
class PDFExporter {
    constructor() {
        this.isExporting = false;
        this.init();
    }
    
    init() {
        // Setup export button
        document.getElementById('exportPDF').addEventListener('click', () => {
            this.exportToPDF();
        });
    }
    
    async exportToPDF() {
        if (this.isExporting) return;

        try {
            this.isExporting = true;
            this.showLoadingOverlay();

            // Get CV preview element with multiple fallbacks
            let cvPreview = this.findCVPreviewElement();

            if (!cvPreview) {
                // If no preview exists, generate one first
                await this.generatePreviewForExport();
                cvPreview = this.findCVPreviewElement();
            }

            if (!cvPreview) {
                throw new Error('لا يمكن العثور على معاينة السيرة الذاتية. يرجى إنشاء معاينة أولاً.');
            }

            // Ensure the preview has content
            if (!this.hasValidContent(cvPreview)) {
                throw new Error('المعاينة فارغة. يرجى ملء البيانات الأساسية أولاً.');
            }

            // Create a clone for PDF generation to avoid affecting the original
            const clonedCV = cvPreview.cloneNode(true);
            clonedCV.id = 'pdf-clone';

            // Prepare clone for PDF generation
            this.prepareCVForPDF(clonedCV);

            // Add clone to document temporarily
            document.body.appendChild(clonedCV);

            // Wait for fonts and images to load
            await this.waitForContent(clonedCV);

            // Generate high-quality canvas
            const canvas = await html2canvas(clonedCV, {
                scale: 3, // Higher resolution for better quality
                useCORS: true,
                allowTaint: false,
                backgroundColor: '#ffffff',
                width: 794, // A4 width in pixels at 96 DPI
                height: null, // Let it calculate height automatically
                scrollX: 0,
                scrollY: 0,
                windowWidth: 794,
                logging: false,
                imageTimeout: 15000,
                removeContainer: true
            });

            // Remove clone from document
            document.body.removeChild(clonedCV);

            // Create PDF with proper page handling
            await this.createPDFFromCanvas(canvas);

            // Show success message
            this.showSuccessMessage();

        } catch (error) {
            console.error('Error generating PDF:', error);
            this.showErrorMessage();
        } finally {
            this.isExporting = false;
            this.hideLoadingOverlay();
        }
    }

    findCVPreviewElement() {
        // Try multiple selectors to find the CV preview
        const selectors = [
            '#cvPreview',
            '.cv-preview',
            '#livePreview .cv-preview',
            '.cv-page',
            '#preview .cv-content',
            '.preview-container .cv-preview',
            '.live-preview-container .cv-preview'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && element.offsetHeight > 0) {
                return element;
            }
        }

        return null;
    }

    hasValidContent(element) {
        // Check if the preview has meaningful content
        const textContent = element.textContent.trim();
        const hasImages = element.querySelectorAll('img').length > 0;
        const hasStructure = element.querySelectorAll('div, section, article').length > 3;

        return textContent.length > 50 || hasImages || hasStructure;
    }

    async generatePreviewForExport() {
        // Force generate a preview if none exists
        if (window.livePreview) {
            // Get current template or use default
            const template = window.livePreview.currentTemplate ||
                           (window.readyTemplatesDB && window.readyTemplatesDB.templates[0]);

            if (template) {
                window.livePreview.loadTemplate(template);
                // Wait for preview to render
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        } else if (window.cvApp && window.cvApp.generatePreview) {
            // Fallback to main app preview generation
            window.cvApp.generatePreview();
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    prepareCVForPDF(clonedCV) {
        // Reset all transforms and set optimal size for PDF
        clonedCV.style.cssText = `
            position: absolute;
            top: -9999px;
            left: -9999px;
            width: 794px !important;
            max-width: none !important;
            min-width: 794px !important;
            transform: none !important;
            zoom: 1 !important;
            background: white;
            box-shadow: none !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: visible !important;
        `;

        // Ensure content fits properly
        const content = clonedCV.querySelector('.cv-content');
        if (content) {
            content.style.cssText = `
                padding: 40px !important;
                margin: 0 !important;
                width: 100% !important;
                box-sizing: border-box !important;
                font-size: 14px !important;
                line-height: 1.5 !important;
            `;
        }

        // Fix photo display in PDF
        const photo = clonedCV.querySelector('.cv-photo');
        if (photo) {
            photo.style.cssText += `
                width: 100px !important;
                height: 100px !important;
                object-fit: cover !important;
                display: block !important;
            `;
        }

        // Ensure all text is visible
        const textElements = clonedCV.querySelectorAll('*');
        textElements.forEach(el => {
            const computedStyle = window.getComputedStyle(el);
            if (computedStyle.color === 'transparent' || computedStyle.opacity === '0') {
                el.style.color = '#000000';
                el.style.opacity = '1';
            }
        });

        // Fix any gradient text issues
        const gradientTexts = clonedCV.querySelectorAll('[style*="background-clip"]');
        gradientTexts.forEach(el => {
            el.style.webkitBackgroundClip = 'unset';
            el.style.webkitTextFillColor = 'unset';
            el.style.backgroundClip = 'unset';
            el.style.color = '#2563eb';
        });
    }

    async waitForContent(element) {
        // Wait for images to load
        const images = element.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve;
                    // Timeout after 5 seconds
                    setTimeout(resolve, 5000);
                }
            });
        });

        await Promise.all(imagePromises);

        // Wait for fonts to load
        if (document.fonts) {
            await document.fonts.ready;
        }

        // Additional delay to ensure everything is rendered
        await this.delay(500);
    }

    async createPDFFromCanvas(canvas) {
        const { jsPDF } = window.jspdf;

        // Create PDF with high quality settings
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4',
            compress: true,
            precision: 2
        });

        // Calculate optimal dimensions
        const pageWidth = 210; // A4 width in mm
        const pageHeight = 297; // A4 height in mm
        const imgWidth = pageWidth;
        const imgHeight = (canvas.height * pageWidth) / canvas.width;

        // Convert canvas to high-quality image
        const imgData = canvas.toDataURL('image/jpeg', 0.95);

        if (imgHeight <= pageHeight) {
            // Single page - center vertically if needed
            const yOffset = Math.max(0, (pageHeight - imgHeight) / 2);
            pdf.addImage(imgData, 'JPEG', 0, yOffset, imgWidth, imgHeight);
        } else {
            // Multiple pages - intelligent page breaks
            await this.addMultiPageContent(pdf, imgData, imgWidth, imgHeight, pageHeight);
        }

        // Generate filename and save
        const formData = cvBuilder.getFormData();
        const fileName = this.generateFileName(formData);
        pdf.save(fileName);
    }

    async addMultiPageContent(pdf, imgData, imgWidth, imgHeight, pageHeight) {
        let currentY = 0;
        let pageNumber = 1;

        while (currentY < imgHeight) {
            if (pageNumber > 1) {
                pdf.addPage();
            }

            // Calculate how much content fits on this page
            const remainingHeight = imgHeight - currentY;
            const pageContentHeight = Math.min(pageHeight, remainingHeight);

            // Add image section to current page
            pdf.addImage(
                imgData,
                'JPEG',
                0,
                0,
                imgWidth,
                imgHeight,
                undefined,
                'FAST',
                0,
                -currentY
            );

            currentY += pageHeight;
            pageNumber++;

            // Prevent infinite loop
            if (pageNumber > 10) break;
        }
    }
    
    generateFileName(formData) {
        const name = formData.personal?.fullName || 'CV';
        const date = new Date().toISOString().split('T')[0];
        const cleanName = name.replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g, '').replace(/\s+/g, '_');
        return `${cleanName}_CV_${date}.pdf`;
    }
    
    showLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.add('active');
    }
    
    hideLoadingOverlay() {
        const overlay = document.getElementById('loadingOverlay');
        overlay.classList.remove('active');
    }
    
    showSuccessMessage() {
        this.showNotification(
            languageManager.getTranslation('pdf_generated'),
            'success'
        );
    }
    
    showErrorMessage() {
        this.showNotification(
            languageManager.getTranslation('pdf_error'),
            'error'
        );
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: this.getNotificationColor(type),
            color: 'white',
            padding: '16px 20px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            zIndex: '10000',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            fontSize: '14px',
            fontWeight: '500',
            maxWidth: '400px',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease-in-out'
        });
        
        // Add to document
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
    }
    
    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    getNotificationColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Alternative export method using browser's print functionality
    async printToPDF() {
        try {
            this.showLoadingOverlay();
            
            // Create a new window with the CV content
            const printWindow = window.open('', '_blank');
            const cvContent = document.getElementById('cvPreview').outerHTML;
            
            // Get current theme
            const currentTheme = document.querySelector('.cv-preview').className.match(/theme-\w+/)?.[0] || 'theme-modern';
            
            printWindow.document.write(`
                <!DOCTYPE html>
                <html lang="${languageManager.getCurrentLanguage()}" dir="${languageManager.isRTL() ? 'rtl' : 'ltr'}">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>CV - ${cvBuilder.getFormData().personal?.fullName || 'Professional CV'}</title>
                    <link rel="preconnect" href="https://fonts.googleapis.com">
                    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
                    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
                    <style>
                        ${this.getPrintStyles()}
                    </style>
                </head>
                <body>
                    <div class="cv-preview ${currentTheme}">
                        ${document.querySelector('.cv-content').outerHTML}
                    </div>
                </body>
                </html>
            `);
            
            printWindow.document.close();
            
            // Wait for content to load
            await this.delay(1000);
            
            // Trigger print dialog
            printWindow.print();
            
            // Close the window after printing
            setTimeout(() => {
                printWindow.close();
            }, 1000);
            
        } catch (error) {
            console.error('Error printing PDF:', error);
            this.showErrorMessage();
        } finally {
            this.hideLoadingOverlay();
        }
    }
    
    getPrintStyles() {
        // Return CSS styles optimized for printing
        return `
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: ${languageManager.isRTL() ? "'Noto Sans Arabic', 'Tahoma', sans-serif" : "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif"};
                line-height: 1.6;
                color: #1e293b;
                background: white;
            }
            
            .cv-preview {
                width: 100%;
                max-width: none;
                margin: 0;
                padding: 0;
                background: white;
                box-shadow: none;
                border-radius: 0;
            }
            
            .cv-content {
                padding: 40px;
            }
            
            .cv-header {
                text-align: center;
                margin-bottom: 40px;
                padding-bottom: 30px;
                border-bottom: 2px solid #2563eb;
            }
            
            .cv-name {
                font-size: 36px;
                font-weight: 700;
                color: #1e293b;
                margin-bottom: 8px;
            }
            
            .cv-title {
                font-size: 20px;
                color: #2563eb;
                font-weight: 500;
                margin-bottom: 16px;
            }
            
            .cv-contact {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                gap: 16px;
                font-size: 14px;
                color: #64748b;
            }
            
            .cv-contact-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .cv-contact-item i {
                color: #2563eb;
                width: 16px;
                text-align: center;
            }
            
            .cv-section {
                margin-bottom: 32px;
            }
            
            .cv-section-title {
                font-size: 20px;
                font-weight: 600;
                color: #1e293b;
                margin-bottom: 16px;
                padding-bottom: 8px;
                border-bottom: 1px solid #cbd5e1;
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .cv-section-title i {
                color: #2563eb;
            }
            
            .cv-summary {
                font-size: 16px;
                line-height: 1.7;
                color: #475569;
                text-align: justify;
            }
            
            .cv-experience-item,
            .cv-education-item {
                margin-bottom: 24px;
                padding-bottom: 16px;
                border-bottom: 1px solid #e2e8f0;
            }
            
            .cv-experience-item:last-child,
            .cv-education-item:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }
            
            .cv-item-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 8px;
            }
            
            .cv-item-title {
                font-size: 18px;
                font-weight: 600;
                color: #1e293b;
            }
            
            .cv-item-company,
            .cv-item-institution {
                font-size: 16px;
                color: #2563eb;
                font-weight: 500;
            }
            
            .cv-item-date {
                font-size: 14px;
                color: #64748b;
                font-weight: 500;
                white-space: nowrap;
            }
            
            .cv-item-description {
                font-size: 14px;
                line-height: 1.6;
                color: #475569;
                margin-top: 8px;
            }
            
            .cv-skills-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 16px;
            }
            
            .cv-skill-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
            }
            
            .cv-skill-name {
                font-weight: 500;
                color: #1e293b;
            }
            
            .cv-skill-level {
                font-size: 12px;
                color: #64748b;
                background: #e2e8f0;
                padding: 4px 8px;
                border-radius: 4px;
                text-transform: uppercase;
                font-weight: 500;
            }
            
            .cv-languages-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 12px;
            }
            
            .cv-language-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px;
                background: #f8fafc;
                border-radius: 6px;
            }
            
            .cv-language-name {
                font-weight: 500;
                color: #1e293b;
            }
            
            .cv-language-level {
                font-size: 12px;
                color: #2563eb;
                font-weight: 600;
            }
            
            /* Theme-specific print styles */
            .cv-preview.theme-modern .cv-header {
                background: #2563eb !important;
                color: white !important;
                padding: 40px;
                margin: -40px -40px 40px;
                border-radius: 0;
                border-bottom: none;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .cv-preview.theme-modern .cv-name {
                color: white !important;
            }
            
            .cv-preview.theme-modern .cv-title {
                color: rgba(255, 255, 255, 0.9) !important;
            }
            
            .cv-preview.theme-modern .cv-contact {
                color: rgba(255, 255, 255, 0.8) !important;
            }
            
            .cv-preview.theme-modern .cv-contact-item i {
                color: white !important;
            }
            
            @page {
                margin: 0;
                size: A4;
            }
            
            @media print {
                body {
                    -webkit-print-color-adjust: exact;
                    color-adjust: exact;
                }
            }
        `;
    }
}

// Initialize PDF Exporter
const pdfExporter = new PDFExporter();

// Export for global access
window.pdfExporter = pdfExporter;
