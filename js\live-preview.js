// ELASHRAFY CV - Live Preview
// المعاينة المباشرة - الأشرافي للسيرة الذاتية

class LivePreview {
    constructor() {
        this.currentTemplate = null;
        this.cvData = null;
        this.previewMode = 'desktop';
        this.isUpdating = false;
        this.updateQueue = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializePreview();
    }
    
    setupEventListeners() {
        // Listen for data changes from editor
        document.addEventListener('cvDataChanged', (e) => {
            this.queueUpdate(e.detail);
        });
        
        // Preview mode changes
        document.addEventListener('click', (e) => {
            if (e.target.closest('.preview-btn')) {
                const mode = e.target.closest('.preview-btn').dataset.mode;
                this.changePreviewMode(mode);
            }
        });
        
        // Window resize
        window.addEventListener('resize', () => {
            this.adjustPreviewScale();
        });
    }
    
    initializePreview() {
        console.log('🔍 Live Preview initialized');
        this.renderPreviewPlaceholder();
    }
    
    loadTemplate(template) {
        if (!template) return;
        
        this.currentTemplate = template;
        this.renderPreview();
    }
    
    updatePreview(template, cvData = null) {
        this.currentTemplate = template;
        if (cvData) {
            this.cvData = cvData;
        }
        this.renderPreview();
    }
    
    queueUpdate(data) {
        this.updateQueue.push(data);
        
        if (!this.isUpdating) {
            this.processUpdateQueue();
        }
    }
    
    async processUpdateQueue() {
        this.isUpdating = true;
        
        while (this.updateQueue.length > 0) {
            const update = this.updateQueue.shift();
            await this.applyUpdate(update);
            
            // Small delay to prevent overwhelming the browser
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        this.isUpdating = false;
    }
    
    async applyUpdate(update) {
        if (!this.currentTemplate) return;
        
        // Update the specific section that changed
        const { section, field, value } = update;
        
        switch (section) {
            case 'personal':
                this.updatePersonalSection(field, value);
                break;
            case 'summary':
                this.updateSummarySection(value);
                break;
            case 'experience':
                this.updateExperienceSection(update);
                break;
            case 'education':
                this.updateEducationSection(update);
                break;
            case 'skills':
                this.updateSkillsSection(update);
                break;
            default:
                this.renderPreview();
        }
    }
    
    renderPreview() {
        const previewContainer = document.getElementById('livePreview');
        if (!previewContainer) return;
        
        if (!this.currentTemplate) {
            this.renderPreviewPlaceholder();
            return;
        }
        
        // Show loading state
        this.showPreviewLoading();
        
        // Render the CV based on template
        setTimeout(() => {
            this.renderCVPreview();
        }, 300);
    }
    
    renderPreviewPlaceholder() {
        const previewContainer = document.getElementById('livePreview');
        if (!previewContainer) return;
        
        previewContainer.innerHTML = `
            <div class="preview-placeholder">
                <div class="placeholder-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h3>معاينة السيرة الذاتية</h3>
                <p>اختر قالباً وابدأ بإدخال بياناتك لرؤية المعاينة المباشرة</p>
                <div class="placeholder-features">
                    <div class="feature">
                        <i class="fas fa-eye"></i>
                        <span>معاينة مباشرة</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-mobile-alt"></i>
                        <span>متجاوب مع جميع الأجهزة</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-palette"></i>
                        <span>تخصيص الألوان والخطوط</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    showPreviewLoading() {
        const previewContainer = document.getElementById('livePreview');
        if (!previewContainer) return;
        
        previewContainer.innerHTML = `
            <div class="preview-loading">
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                </div>
                <h4>جاري تحديث المعاينة...</h4>
                <p>يتم الآن تطبيق التغييرات على قالب "${this.currentTemplate.name}"</p>
            </div>
        `;
    }
    
    renderCVPreview() {
        const previewContainer = document.getElementById('livePreview');
        if (!previewContainer) return;
        
        // Get CV data from editor or use default
        const cvData = this.getCVData();
        
        // Generate preview HTML based on template style
        const previewHTML = this.generatePreviewHTML(cvData);
        
        previewContainer.innerHTML = `
            <div class="cv-preview ${this.previewMode}" data-template="${this.currentTemplate.id}">
                <div class="cv-page">
                    ${previewHTML}
                </div>
                
                <div class="preview-overlay">
                    <div class="preview-info">
                        <span class="template-name">${this.currentTemplate.name}</span>
                        <span class="preview-mode">${this.getPreviewModeLabel()}</span>
                    </div>
                    
                    <div class="preview-actions">
                        <button class="preview-action-btn" onclick="livePreview.zoomIn()" title="تكبير">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="preview-action-btn" onclick="livePreview.zoomOut()" title="تصغير">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button class="preview-action-btn" onclick="livePreview.resetZoom()" title="إعادة تعيين التكبير">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="preview-action-btn" onclick="livePreview.fullscreen()" title="ملء الشاشة">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Apply template-specific styles
        this.applyTemplateStyles();
        
        // Adjust preview scale
        this.adjustPreviewScale();
    }
    
    generatePreviewHTML(cvData) {
        // Generate HTML based on template style
        switch (this.currentTemplate.style) {
            case 'glass-morphism':
                return this.generateGlassMorphismHTML(cvData);
            case 'neo-brutalism':
                return this.generateNeoBrutalismHTML(cvData);
            case 'gradient-mesh':
                return this.generateGradientMeshHTML(cvData);
            case 'organic-shapes':
                return this.generateOrganicShapesHTML(cvData);
            case 'geometric-art':
                return this.generateGeometricArtHTML(cvData);
            case 'watercolor':
                return this.generateWatercolorHTML(cvData);
            case 'neon-cyber':
                return this.generateNeonCyberHTML(cvData);
            case 'crystal-clear':
                return this.generateCrystalClearHTML(cvData);
            case 'luxury':
                return this.generateLuxuryHTML(cvData);
            case 'holographic':
                return this.generateHolographicHTML(cvData);
            default:
                return this.generateDefaultHTML(cvData);
        }
    }
    
    generateDefaultHTML(cvData) {
        return `
            <div class="cv-header">
                <div class="cv-photo">
                    ${cvData.personalInfo.photo ? 
                        `<img src="${cvData.personalInfo.photo}" alt="${cvData.personalInfo.name}">` :
                        `<div class="photo-placeholder"><i class="fas fa-user"></i></div>`
                    }
                </div>
                <div class="cv-info">
                    <h1 class="cv-name">${cvData.personalInfo.name || 'اسمك الكامل'}</h1>
                    <h2 class="cv-title">${cvData.personalInfo.title || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact">
                        ${cvData.personalInfo.email ? `<div class="contact-item"><i class="fas fa-envelope"></i> ${cvData.personalInfo.email}</div>` : ''}
                        ${cvData.personalInfo.phone ? `<div class="contact-item"><i class="fas fa-phone"></i> ${cvData.personalInfo.phone}</div>` : ''}
                        ${cvData.personalInfo.location ? `<div class="contact-item"><i class="fas fa-map-marker-alt"></i> ${cvData.personalInfo.location}</div>` : ''}
                    </div>
                </div>
            </div>
            
            ${cvData.summary ? `
                <div class="cv-section">
                    <h3 class="section-title">الملخص المهني</h3>
                    <p class="summary-text">${cvData.summary}</p>
                </div>
            ` : ''}
            
            ${cvData.experience && cvData.experience.length > 0 ? `
                <div class="cv-section">
                    <h3 class="section-title">الخبرة المهنية</h3>
                    <div class="experience-list">
                        ${cvData.experience.map(exp => `
                            <div class="experience-item">
                                <div class="exp-header">
                                    <h4 class="exp-title">${exp.title || 'المسمى الوظيفي'}</h4>
                                    <span class="exp-date">${exp.startDate || 'تاريخ البداية'} - ${exp.current ? 'حتى الآن' : (exp.endDate || 'تاريخ النهاية')}</span>
                                </div>
                                <div class="exp-company">${exp.company || 'اسم الشركة'}</div>
                                ${exp.location ? `<div class="exp-location">${exp.location}</div>` : ''}
                                ${exp.description ? `<div class="exp-description">${exp.description}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${cvData.skills && cvData.skills.length > 0 ? `
                <div class="cv-section">
                    <h3 class="section-title">المهارات</h3>
                    <div class="skills-list">
                        ${cvData.skills.map(skill => `
                            <div class="skill-item">
                                <span class="skill-name">${skill.name || 'اسم المهارة'}</span>
                                ${skill.level ? `<div class="skill-level level-${skill.level}"></div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
        `;
    }
    
    generateGlassMorphismHTML(cvData) {
        return `
            <div class="glass-container">
                <div class="glass-header">
                    <div class="glass-photo">
                        ${cvData.personalInfo.photo ? 
                            `<img src="${cvData.personalInfo.photo}" alt="${cvData.personalInfo.name}">` :
                            `<div class="photo-placeholder glass"><i class="fas fa-user"></i></div>`
                        }
                    </div>
                    <div class="glass-info">
                        <h1 class="glass-name">${cvData.personalInfo.name || 'اسمك الكامل'}</h1>
                        <h2 class="glass-title">${cvData.personalInfo.title || 'المسمى الوظيفي'}</h2>
                    </div>
                </div>
                
                <div class="glass-sections">
                    ${cvData.summary ? `
                        <div class="glass-section">
                            <h3 class="glass-section-title">الملخص المهني</h3>
                            <div class="glass-content">
                                <p>${cvData.summary}</p>
                            </div>
                        </div>
                    ` : ''}
                    
                    <!-- Add more sections with glass morphism styling -->
                </div>
            </div>
        `;
    }
    
    generateNeoBrutalismHTML(cvData) {
        return `
            <div class="brutal-container">
                <div class="brutal-header">
                    <div class="brutal-photo">
                        ${cvData.personalInfo.photo ? 
                            `<img src="${cvData.personalInfo.photo}" alt="${cvData.personalInfo.name}">` :
                            `<div class="photo-placeholder brutal"><i class="fas fa-user"></i></div>`
                        }
                    </div>
                    <div class="brutal-info">
                        <h1 class="brutal-name">${cvData.personalInfo.name || 'اسمك الكامل'}</h1>
                        <h2 class="brutal-title">${cvData.personalInfo.title || 'المسمى الوظيفي'}</h2>
                    </div>
                </div>
                
                <!-- Add more sections with neo-brutalism styling -->
            </div>
        `;
    }
    
    getCVData() {
        // Get data from form inputs or advanced editor
        if (window.advancedEditor && window.advancedEditor.cvData) {
            return window.advancedEditor.cvData;
        }

        // Extract data from form inputs
        const formData = this.extractFormData();
        return formData;
    }

    extractFormData() {
        const data = {
            personalInfo: {
                name: this.getInputValue('fullName') || 'اسمك الكامل',
                title: this.getInputValue('jobTitle') || 'المسمى الوظيفي',
                email: this.getInputValue('email') || 'البريد الإلكتروني',
                phone: this.getInputValue('phone') || 'رقم الهاتف',
                location: this.getInputValue('location') || 'الموقع',
                website: this.getInputValue('website') || '',
                linkedin: this.getInputValue('linkedin') || '',
                photo: this.getPhotoData()
            },
            summary: this.getInputValue('summary') || 'أضف ملخصاً مهنياً يبرز خبراتك ومهاراتك الأساسية',
            experience: this.getExperienceData(),
            education: this.getEducationData(),
            skills: this.getSkillsData(),
            languages: this.getLanguagesData(),
            projects: this.getProjectsData(),
            certifications: this.getCertificationsData()
        };

        return data;
    }

    getInputValue(name) {
        const input = document.querySelector(`[name="${name}"], #${name}`);
        return input ? input.value.trim() : '';
    }

    getPhotoData() {
        const photoPreview = document.querySelector('.photo-preview img');
        return photoPreview ? photoPreview.src : null;
    }

    getExperienceData() {
        const experiences = [];
        const expItems = document.querySelectorAll('.experience-item');

        expItems.forEach(item => {
            const title = item.querySelector('[name*="title"]')?.value || '';
            const company = item.querySelector('[name*="company"]')?.value || '';
            const location = item.querySelector('[name*="location"]')?.value || '';
            const startDate = item.querySelector('[name*="startDate"]')?.value || '';
            const endDate = item.querySelector('[name*="endDate"]')?.value || '';
            const current = item.querySelector('[name*="current"]')?.checked || false;
            const description = item.querySelector('[name*="description"]')?.value || '';

            if (title || company) {
                experiences.push({
                    title,
                    company,
                    location,
                    startDate,
                    endDate,
                    current,
                    description
                });
            }
        });

        return experiences;
    }

    getEducationData() {
        const education = [];
        const eduItems = document.querySelectorAll('.education-item');

        eduItems.forEach(item => {
            const degree = item.querySelector('[name*="degree"]')?.value || '';
            const institution = item.querySelector('[name*="institution"]')?.value || '';
            const year = item.querySelector('[name*="year"]')?.value || '';
            const gpa = item.querySelector('[name*="gpa"]')?.value || '';

            if (degree || institution) {
                education.push({
                    degree,
                    institution,
                    year,
                    gpa
                });
            }
        });

        return education;
    }

    getSkillsData() {
        const skills = [];
        const skillItems = document.querySelectorAll('.skill-tag, .skill-item');

        skillItems.forEach(item => {
            const name = item.querySelector('.skill-name')?.textContent ||
                        item.dataset.skill || '';
            const level = item.querySelector('.skill-level')?.textContent ||
                         item.dataset.level || 'متوسط';

            if (name) {
                skills.push({ name, level });
            }
        });

        return skills;
    }

    getLanguagesData() {
        const languages = [];
        const langItems = document.querySelectorAll('.language-item');

        langItems.forEach(item => {
            const name = item.querySelector('[name*="language"]')?.value || '';
            const level = item.querySelector('[name*="level"]')?.value || '';

            if (name) {
                languages.push({ name, level });
            }
        });

        return languages;
    }

    getProjectsData() {
        const projects = [];
        const projectItems = document.querySelectorAll('.project-item');

        projectItems.forEach(item => {
            const name = item.querySelector('[name*="name"]')?.value || '';
            const description = item.querySelector('[name*="description"]')?.value || '';
            const technologies = item.querySelector('[name*="technologies"]')?.value || '';
            const url = item.querySelector('[name*="url"]')?.value || '';

            if (name) {
                projects.push({
                    name,
                    description,
                    technologies: technologies.split(',').map(t => t.trim()),
                    url
                });
            }
        });

        return projects;
    }

    getCertificationsData() {
        const certifications = [];
        const certItems = document.querySelectorAll('.certification-item');

        certItems.forEach(item => {
            const name = item.querySelector('[name*="name"]')?.value || '';
            const issuer = item.querySelector('[name*="issuer"]')?.value || '';
            const date = item.querySelector('[name*="date"]')?.value || '';

            if (name) {
                certifications.push({ name, issuer, date });
            }
        });

        return certifications;
    }
    
    applyTemplateStyles() {
        // Apply template-specific CSS variables and styles
        const cvPreview = document.querySelector('.cv-preview');
        if (!cvPreview) return;
        
        const colors = this.currentTemplate.colors;
        if (colors) {
            cvPreview.style.setProperty('--template-primary', colors.primary);
            cvPreview.style.setProperty('--template-secondary', colors.secondary);
            cvPreview.style.setProperty('--template-accent', colors.accent);
            cvPreview.style.setProperty('--template-background', colors.background);
            cvPreview.style.setProperty('--template-text', colors.text);
        }
        
        const fonts = this.currentTemplate.fonts;
        if (fonts && fonts.length > 0) {
            cvPreview.style.fontFamily = fonts.join(', ');
        }
    }
    
    changePreviewMode(mode) {
        this.previewMode = mode;
        
        // Update active button
        document.querySelectorAll('.preview-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-mode="${mode}"]`).classList.add('active');
        
        // Update preview container
        const cvPreview = document.querySelector('.cv-preview');
        if (cvPreview) {
            cvPreview.className = `cv-preview ${mode}`;
            this.adjustPreviewScale();
        }
    }
    
    adjustPreviewScale() {
        const cvPreview = document.querySelector('.cv-preview');
        const previewContainer = document.getElementById('livePreview');
        
        if (!cvPreview || !previewContainer) return;
        
        const containerWidth = previewContainer.clientWidth;
        const containerHeight = previewContainer.clientHeight;
        
        let scale = 1;
        
        switch (this.previewMode) {
            case 'desktop':
                scale = Math.min(containerWidth / 800, containerHeight / 1000);
                break;
            case 'tablet':
                scale = Math.min(containerWidth / 600, containerHeight / 800);
                break;
            case 'mobile':
                scale = Math.min(containerWidth / 400, containerHeight / 600);
                break;
        }
        
        scale = Math.min(scale, 1); // Don't scale up beyond 100%
        
        cvPreview.style.transform = `scale(${scale})`;
        cvPreview.style.transformOrigin = 'top center';
    }
    
    getPreviewModeLabel() {
        const labels = {
            desktop: 'سطح المكتب',
            tablet: 'الجهاز اللوحي',
            mobile: 'الهاتف المحمول'
        };
        return labels[this.previewMode] || 'سطح المكتب';
    }
    
    // Update specific sections
    updatePersonalSection(field, value) {
        const element = document.querySelector(`.cv-${field}`);
        if (element) {
            if (field === 'photo') {
                const img = element.querySelector('img');
                const placeholder = element.querySelector('.photo-placeholder');
                
                if (value) {
                    if (img) {
                        img.src = value;
                    } else if (placeholder) {
                        element.innerHTML = `<img src="${value}" alt="الصورة الشخصية">`;
                    }
                } else {
                    if (img) {
                        element.innerHTML = `<div class="photo-placeholder"><i class="fas fa-user"></i></div>`;
                    }
                }
            } else {
                element.textContent = value || this.getPlaceholderText(field);
            }
        }
    }
    
    updateSummarySection(value) {
        const element = document.querySelector('.summary-text');
        if (element) {
            element.textContent = value || 'أضف ملخصاً مهنياً يبرز خبراتك ومهاراتك';
        }
    }
    
    updateExperienceSection(update) {
        // Rebuild experience section
        this.renderPreview();
    }
    
    updateEducationSection(update) {
        // Rebuild education section
        this.renderPreview();
    }
    
    updateSkillsSection(update) {
        // Rebuild skills section
        this.renderPreview();
    }
    
    getPlaceholderText(field) {
        const placeholders = {
            name: 'اسمك الكامل',
            title: 'المسمى الوظيفي',
            email: 'البريد الإلكتروني',
            phone: 'رقم الهاتف',
            location: 'الموقع'
        };
        return placeholders[field] || '';
    }
    
    // Preview controls
    zoomIn() {
        const cvPreview = document.querySelector('.cv-preview');
        if (cvPreview) {
            const currentScale = this.getCurrentScale();
            const newScale = Math.min(currentScale * 1.2, 2);
            cvPreview.style.transform = `scale(${newScale})`;
        }
    }
    
    zoomOut() {
        const cvPreview = document.querySelector('.cv-preview');
        if (cvPreview) {
            const currentScale = this.getCurrentScale();
            const newScale = Math.max(currentScale * 0.8, 0.3);
            cvPreview.style.transform = `scale(${newScale})`;
        }
    }
    
    resetZoom() {
        this.adjustPreviewScale();
    }
    
    getCurrentScale() {
        const cvPreview = document.querySelector('.cv-preview');
        if (cvPreview) {
            const transform = cvPreview.style.transform;
            const match = transform.match(/scale\(([^)]+)\)/);
            return match ? parseFloat(match[1]) : 1;
        }
        return 1;
    }
    
    fullscreen() {
        const previewContainer = document.getElementById('livePreview');
        if (previewContainer) {
            if (previewContainer.requestFullscreen) {
                previewContainer.requestFullscreen();
            } else if (previewContainer.webkitRequestFullscreen) {
                previewContainer.webkitRequestFullscreen();
            } else if (previewContainer.msRequestFullscreen) {
                previewContainer.msRequestFullscreen();
            }
        }
    }
}

// Initialize live preview
const livePreview = new LivePreview();

// Export for global access
window.livePreview = livePreview;
