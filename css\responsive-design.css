/* ELASHRAFY CV - Responsive Design */
/* التصميم المتجاوب - الأشرافي للسيرة الذاتية */

/* Base responsive utilities */
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-4);
}

/* Responsive grid system */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-2));
}

.col {
    flex: 1;
    padding: 0 var(--spacing-2);
}

.col-auto {
    flex: 0 0 auto;
    width: auto;
}

/* Responsive breakpoints */
/* Extra Large devices (1400px and up) */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }
    
    .section-header {
        padding: var(--spacing-10) var(--spacing-8);
    }
    
    .brand-title {
        font-size: var(--font-size-3xl);
    }
}

/* Large devices (1200px and up) */
@media (min-width: 1200px) and (max-width: 1399.98px) {
    .container {
        max-width: 1140px;
    }
    
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }
    
    .main-navigation {
        gap: var(--spacing-3);
    }
    
    .nav-btn {
        padding: var(--spacing-3) var(--spacing-5);
    }
}

/* Medium devices (992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .container {
        max-width: 960px;
    }
    
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--spacing-5);
    }
    
    .header-container {
        padding: 0 var(--spacing-5);
    }
    
    .brand-section {
        gap: var(--spacing-3);
    }
    
    .brand-title {
        font-size: var(--font-size-xl);
    }
    
    .brand-subtitle {
        font-size: var(--font-size-xs);
    }
    
    .main-navigation {
        gap: var(--spacing-2);
    }
    
    .nav-btn {
        padding: var(--spacing-2) var(--spacing-4);
        font-size: var(--font-size-sm);
    }
    
    .nav-badge {
        font-size: 10px;
        padding: 2px var(--spacing-1);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .templates-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-3);
    }
}

/* Small devices (768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }
    
    .header-container {
        flex-direction: column;
        height: auto;
        padding: var(--spacing-4);
        gap: var(--spacing-4);
    }
    
    .brand-section {
        order: 1;
        justify-content: center;
    }
    
    .main-navigation {
        order: 2;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
        gap: var(--spacing-2);
    }
    
    .header-actions {
        order: 3;
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--spacing-4);
    }
    
    .template-card {
        margin-bottom: var(--spacing-4);
    }
    
    .section-header {
        padding: var(--spacing-6);
    }
    
    .section-title {
        font-size: var(--font-size-xl);
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .templates-controls {
        gap: var(--spacing-3);
    }
    
    .search-filters {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }
    
    .filter-select {
        width: 100%;
    }
    
    .view-controls {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: center;
    }
    
    .templates-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-3);
        padding: var(--spacing-4);
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-2);
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-4);
    }
    
    .preview-modal-content {
        flex-direction: column;
        max-width: 95vw;
        max-height: 95vh;
    }
    
    .preview-modal-image {
        max-width: none;
        max-height: 50vh;
    }
    
    .preview-modal-info {
        min-width: auto;
        padding: var(--spacing-4);
    }
}

/* Extra small devices (576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .container {
        max-width: 540px;
    }
    
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--spacing-3);
    }
    
    .brand-title {
        font-size: var(--font-size-lg);
    }
    
    .brand-subtitle {
        display: none;
    }
    
    .nav-btn span {
        display: none;
    }
    
    .nav-btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        padding: 0;
        justify-content: center;
    }
    
    .action-btn span {
        display: none;
    }
    
    .action-btn {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        padding: 0;
        justify-content: center;
    }
    
    .search-input-wrapper {
        max-width: 100%;
    }
    
    .template-info {
        padding: var(--spacing-4);
    }
    
    .template-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .template-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .template-actions {
        gap: var(--spacing-2);
    }
    
    .template-btn {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
    
    .templates-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .preview-modal-actions {
        flex-direction: column;
        gap: var(--spacing-2);
    }
}

/* Extra extra small devices (less than 576px) */
@media (max-width: 575.98px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .header-container {
        padding: var(--spacing-3);
        gap: var(--spacing-3);
    }
    
    .brand-logo svg {
        width: 40px;
        height: 40px;
    }
    
    .brand-title {
        font-size: var(--font-size-base);
    }
    
    .brand-subtitle {
        display: none;
    }
    
    .main-navigation {
        padding: var(--spacing-1);
        gap: var(--spacing-1);
    }
    
    .nav-btn {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        padding: 0;
        justify-content: center;
        font-size: var(--font-size-sm);
    }
    
    .nav-btn span {
        display: none;
    }
    
    .nav-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 8px;
        padding: 2px 4px;
        min-width: 16px;
        height: 16px;
        border-radius: 8px;
    }
    
    .header-actions {
        gap: var(--spacing-2);
    }
    
    .action-btn {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        padding: 0;
        justify-content: center;
        font-size: var(--font-size-sm);
    }
    
    .action-btn span {
        display: none;
    }
    
    .language-toggle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        padding: 0;
        justify-content: center;
    }
    
    .app-main {
        padding: var(--spacing-4) var(--spacing-3);
    }
    
    .section-header {
        padding: var(--spacing-4);
    }
    
    .section-title {
        font-size: var(--font-size-lg);
        flex-direction: column;
        gap: var(--spacing-2);
        text-align: center;
    }
    
    .section-subtitle {
        font-size: var(--font-size-sm);
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .template-card {
        margin-bottom: var(--spacing-3);
    }
    
    .template-preview {
        aspect-ratio: 4/5;
    }
    
    .template-info {
        padding: var(--spacing-3);
    }
    
    .template-name {
        font-size: var(--font-size-base);
    }
    
    .template-description {
        font-size: var(--font-size-sm);
        -webkit-line-clamp: 3;
    }
    
    .template-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .template-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .template-tags {
        gap: var(--spacing-1);
    }
    
    .tag {
        font-size: 10px;
        padding: 2px var(--spacing-1);
    }
    
    .template-features {
        gap: var(--spacing-1);
    }
    
    .feature-tag {
        font-size: 10px;
        padding: 2px var(--spacing-1);
    }
    
    .template-actions {
        gap: var(--spacing-1);
    }
    
    .template-btn {
        width: 35px;
        height: 35px;
        font-size: var(--font-size-sm);
    }
    
    .search-input-wrapper input {
        padding: var(--spacing-3);
        font-size: var(--font-size-sm);
    }
    
    .templates-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-2);
        padding: var(--spacing-3);
    }
    
    .stat-card {
        padding: var(--spacing-3);
        flex-direction: row;
        gap: var(--spacing-2);
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
    
    .stat-number {
        font-size: var(--font-size-lg);
    }
    
    .stat-label {
        font-size: var(--font-size-xs);
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .feature-card {
        padding: var(--spacing-4);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .preview-modal-content {
        flex-direction: column;
        max-width: 95vw;
        max-height: 95vh;
        margin: var(--spacing-2);
    }
    
    .preview-modal-image {
        max-width: none;
        max-height: 40vh;
    }
    
    .preview-modal-info {
        min-width: auto;
        padding: var(--spacing-3);
    }
    
    .preview-modal-title {
        font-size: var(--font-size-lg);
    }
    
    .preview-modal-actions {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .load-more-btn {
        padding: var(--spacing-3) var(--spacing-6);
        font-size: var(--font-size-sm);
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 767.98px) and (orientation: landscape) {
    .header-container {
        flex-direction: row;
        height: 60px;
        padding: 0 var(--spacing-3);
    }
    
    .brand-section {
        order: 1;
        flex: 1;
    }
    
    .main-navigation {
        order: 2;
        width: auto;
        flex: 2;
        justify-content: center;
    }
    
    .header-actions {
        order: 3;
        width: auto;
        flex: 1;
        justify-content: flex-end;
    }
    
    .nav-btn {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
    
    .action-btn {
        width: 35px;
        height: 35px;
        font-size: 12px;
    }
    
    .app-main {
        padding: var(--spacing-3);
    }
    
    .section-header {
        padding: var(--spacing-3);
    }
    
    .templates-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: var(--spacing-2);
    }
}

/* Print styles */
@media print {
    .professional-header,
    .professional-footer,
    .template-overlay,
    .template-actions,
    .load-more-container {
        display: none !important;
    }
    
    .app-main {
        padding: 0;
    }
    
    .templates-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-2);
    }
    
    .template-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .template-preview {
        aspect-ratio: 3/4;
    }
    
    .template-info {
        padding: var(--spacing-2);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .template-card {
        border-width: 2px;
        border-color: #000;
    }
    
    .template-btn {
        border: 2px solid currentColor;
    }
    
    .tag,
    .feature-tag {
        border: 1px solid currentColor;
    }
    
    .nav-btn,
    .action-btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .template-card,
    .template-preview img,
    .template-overlay,
    .template-btn,
    .nav-btn,
    .action-btn,
    .load-more-btn {
        transition: none !important;
        animation: none !important;
    }
    
    .template-card:hover {
        transform: none !important;
    }
    
    .template-card:hover .template-preview img {
        transform: none !important;
    }
    
    .templates-grid {
        animation: none !important;
    }
    
    @keyframes fadeInUp,
    @keyframes dotPulse,
    @keyframes successPop,
    @keyframes successFadeOut {
        from, to {
            opacity: 1;
            transform: none;
        }
    }
}

/* Focus management for accessibility */
.template-card:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.template-btn:focus,
.nav-btn:focus,
.action-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip to content link */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

.skip-to-content:focus {
    top: 6px;
}
