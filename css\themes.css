/* CV Themes */

/* Modern Theme (Default) */
.cv-preview.theme-modern {
    --theme-primary: #2563eb;
    --theme-secondary: #64748b;
    --theme-accent: #f59e0b;
    --theme-text: #1e293b;
    --theme-text-light: #64748b;
    --theme-bg: #ffffff;
    --theme-bg-light: #f8fafc;
}

.cv-preview.theme-modern .cv-header {
    background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));
    color: white;
    padding: var(--spacing-8);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    border-radius: 0;
    border-bottom: none;
}

.cv-preview.theme-modern .cv-name {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cv-preview.theme-modern .cv-title {
    color: rgba(255, 255, 255, 0.9);
}

.cv-preview.theme-modern .cv-contact {
    color: rgba(255, 255, 255, 0.8);
}

.cv-preview.theme-modern .cv-contact-item i {
    color: white;
}

.cv-preview.theme-modern .cv-section-title {
    color: var(--theme-primary);
    border-bottom-color: var(--theme-primary);
    font-weight: 700;
}

/* Classic Theme */
.cv-preview.theme-classic {
    --theme-primary: #1f2937;
    --theme-secondary: #6b7280;
    --theme-accent: #dc2626;
    --theme-text: #1f2937;
    --theme-text-light: #6b7280;
    --theme-bg: #ffffff;
    --theme-bg-light: #f9fafb;
}

.cv-preview.theme-classic .cv-header {
    border-bottom: 3px solid var(--theme-accent);
    background: var(--theme-bg-light);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    padding: var(--spacing-8);
}

.cv-preview.theme-classic .cv-name {
    color: var(--theme-primary);
    font-family: 'Times New Roman', serif;
}

.cv-preview.theme-classic .cv-title {
    color: var(--theme-accent);
    font-style: italic;
}

.cv-preview.theme-classic .cv-section-title {
    color: var(--theme-primary);
    border-bottom: 2px solid var(--theme-accent);
    font-family: 'Times New Roman', serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cv-preview.theme-classic .cv-contact-item i {
    color: var(--theme-accent);
}

.cv-preview.theme-classic .cv-item-company,
.cv-preview.theme-classic .cv-item-institution {
    color: var(--theme-accent);
    font-style: italic;
}

/* Creative Theme */
.cv-preview.theme-creative {
    --theme-primary: #7c3aed;
    --theme-secondary: #a78bfa;
    --theme-accent: #06b6d4;
    --theme-text: #1e293b;
    --theme-text-light: #64748b;
    --theme-bg: #ffffff;
    --theme-bg-light: #faf5ff;
}

.cv-preview.theme-creative {
    background: linear-gradient(135deg, #faf5ff 0%, #ffffff 100%);
}

.cv-preview.theme-creative .cv-header {
    background: var(--theme-bg-light);
    border: none;
    border-left: 8px solid var(--theme-primary);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    padding: var(--spacing-8);
    position: relative;
}

.cv-preview.theme-creative .cv-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    clip-path: polygon(100% 0, 0 0, 100% 100%);
}

.cv-preview.theme-creative .cv-name {
    color: var(--theme-primary);
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cv-preview.theme-creative .cv-title {
    color: var(--theme-secondary);
    font-weight: 600;
}

.cv-preview.theme-creative .cv-section-title {
    background: linear-gradient(90deg, var(--theme-primary), var(--theme-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border-bottom: 2px solid transparent;
    border-image: linear-gradient(90deg, var(--theme-primary), var(--theme-accent)) 1;
    position: relative;
}

.cv-preview.theme-creative .cv-section-title::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: var(--theme-accent);
    border-radius: 50%;
}

.cv-preview.theme-creative .cv-contact-item i {
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cv-preview.theme-creative .cv-skill-level {
    background: linear-gradient(45deg, var(--theme-primary), var(--theme-accent));
    color: white;
}

/* Minimal Theme */
.cv-preview.theme-minimal {
    --theme-primary: #000000;
    --theme-secondary: #666666;
    --theme-accent: #000000;
    --theme-text: #000000;
    --theme-text-light: #666666;
    --theme-bg: #ffffff;
    --theme-bg-light: #fafafa;
}

.cv-preview.theme-minimal {
    font-family: 'Helvetica Neue', Arial, sans-serif;
    border: 1px solid #e5e5e5;
}

.cv-preview.theme-minimal .cv-header {
    border-bottom: 1px solid #e5e5e5;
    background: white;
    text-align: left;
}

.cv-preview.theme-minimal .cv-name {
    color: var(--theme-primary);
    font-weight: 300;
    font-size: 2.5rem;
    letter-spacing: -1px;
}

.cv-preview.theme-minimal .cv-title {
    color: var(--theme-secondary);
    font-weight: 400;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.cv-preview.theme-minimal .cv-contact {
    justify-content: flex-start;
    margin-top: var(--spacing-4);
}

.cv-preview.theme-minimal .cv-contact-item {
    font-size: 0.875rem;
    color: var(--theme-secondary);
}

.cv-preview.theme-minimal .cv-contact-item i {
    display: none;
}

.cv-preview.theme-minimal .cv-section-title {
    color: var(--theme-primary);
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: var(--spacing-6);
}

.cv-preview.theme-minimal .cv-section-title i {
    display: none;
}

.cv-preview.theme-minimal .cv-item-title {
    font-weight: 600;
    font-size: 1rem;
}

.cv-preview.theme-minimal .cv-item-company,
.cv-preview.theme-minimal .cv-item-institution {
    color: var(--theme-secondary);
    font-weight: 400;
}

.cv-preview.theme-minimal .cv-item-date {
    color: var(--theme-secondary);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cv-preview.theme-minimal .cv-skill-level {
    background: var(--theme-primary);
    color: white;
    font-size: 0.625rem;
}

.cv-preview.theme-minimal .cv-language-item {
    background: transparent;
    border: 1px solid #e5e5e5;
}

/* RTL Theme Adjustments */
[dir="rtl"] .cv-preview.theme-creative .cv-header {
    border-left: none;
    border-right: 8px solid var(--theme-primary);
}

[dir="rtl"] .cv-preview.theme-creative .cv-section-title::before {
    left: auto;
    right: -20px;
}

[dir="rtl"] .cv-preview.theme-minimal .cv-header {
    text-align: right;
}

[dir="rtl"] .cv-preview.theme-minimal .cv-contact {
    justify-content: flex-end;
}

/* Print Styles for All Themes */
@media print {
    .cv-preview {
        box-shadow: none !important;
        border-radius: 0 !important;
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
    }
    
    .cv-preview.theme-modern .cv-header {
        background: var(--theme-primary) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .cv-preview.theme-creative {
        background: white !important;
    }
    
    .cv-preview.theme-creative .cv-header::before {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* Theme-specific animations */
.cv-preview.theme-creative .cv-section {
    animation: slideInFromLeft 0.6s ease-out;
    animation-fill-mode: both;
}

.cv-preview.theme-creative .cv-section:nth-child(2) { animation-delay: 0.1s; }
.cv-preview.theme-creative .cv-section:nth-child(3) { animation-delay: 0.2s; }
.cv-preview.theme-creative .cv-section:nth-child(4) { animation-delay: 0.3s; }
.cv-preview.theme-creative .cv-section:nth-child(5) { animation-delay: 0.4s; }

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Executive Theme */
.cv-preview.theme-executive {
    --theme-primary: #1e293b;
    --theme-secondary: #475569;
    --theme-accent: #8b5cf6;
    --theme-text: #0f172a;
    --theme-text-light: #475569;
    --theme-bg: #ffffff;
    --theme-bg-light: #f8fafc;
}

.cv-preview.theme-executive {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border: 1px solid #e2e8f0;
}

.cv-preview.theme-executive .cv-header {
    background: linear-gradient(135deg, var(--theme-primary), var(--theme-accent));
    color: white;
    padding: var(--spacing-10);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    position: relative;
}

.cv-preview.theme-executive .cv-header::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid var(--theme-primary);
}

.cv-preview.theme-executive .cv-name {
    color: white;
    font-size: 3rem;
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.cv-preview.theme-executive .cv-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 1px;
}

.cv-preview.theme-executive .cv-section-title {
    color: var(--theme-primary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    border-bottom: 3px solid var(--theme-accent);
    padding-bottom: var(--spacing-2);
}

/* Academic Theme */
.cv-preview.theme-academic {
    --theme-primary: #374151;
    --theme-secondary: #6b7280;
    --theme-accent: #6366f1;
    --theme-text: #111827;
    --theme-text-light: #6b7280;
    --theme-bg: #ffffff;
    --theme-bg-light: #f9fafb;
}

.cv-preview.theme-academic {
    font-family: 'Times New Roman', serif;
    line-height: 1.8;
}

.cv-preview.theme-academic .cv-header {
    text-align: center;
    border-bottom: 2px solid var(--theme-primary);
    padding-bottom: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.cv-preview.theme-academic .cv-name {
    color: var(--theme-primary);
    font-size: 2.5rem;
    font-weight: 400;
    margin-bottom: var(--spacing-2);
}

.cv-preview.theme-academic .cv-title {
    color: var(--theme-secondary);
    font-style: italic;
    font-size: 1.1rem;
}

.cv-preview.theme-academic .cv-section-title {
    color: var(--theme-primary);
    font-weight: 600;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid var(--theme-accent);
    margin-bottom: var(--spacing-4);
}

/* Technical Theme */
.cv-preview.theme-technical {
    --theme-primary: #0f172a;
    --theme-secondary: #475569;
    --theme-accent: #3b82f6;
    --theme-text: #0f172a;
    --theme-text-light: #475569;
    --theme-bg: #ffffff;
    --theme-bg-light: #f1f5f9;
}

.cv-preview.theme-technical {
    font-family: 'Roboto Mono', 'Courier New', monospace;
    background: var(--theme-bg-light);
}

.cv-preview.theme-technical .cv-header {
    background: var(--theme-primary);
    color: var(--theme-accent);
    padding: var(--spacing-6);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    font-family: 'Roboto Mono', monospace;
}

.cv-preview.theme-technical .cv-name {
    color: var(--theme-accent);
    font-weight: 700;
    font-size: 2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.cv-preview.theme-technical .cv-title {
    color: #94a3b8;
    font-weight: 400;
}

.cv-preview.theme-technical .cv-section-title {
    color: var(--theme-primary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-left: 4px solid var(--theme-accent);
    padding-left: var(--spacing-3);
    background: rgba(59, 130, 246, 0.1);
    padding: var(--spacing-2) var(--spacing-3);
}

/* Medical Theme */
.cv-preview.theme-medical {
    --theme-primary: #059669;
    --theme-secondary: #6b7280;
    --theme-accent: #dc2626;
    --theme-text: #111827;
    --theme-text-light: #6b7280;
    --theme-bg: #ffffff;
    --theme-bg-light: #f0fdf4;
}

.cv-preview.theme-medical .cv-header {
    background: linear-gradient(135deg, var(--theme-primary), #10b981);
    color: white;
    padding: var(--spacing-8);
    margin: calc(-1 * var(--spacing-8)) calc(-1 * var(--spacing-8)) var(--spacing-8);
    position: relative;
}

.cv-preview.theme-medical .cv-header::before {
    content: '⚕';
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    font-size: 2rem;
    opacity: 0.3;
}

.cv-preview.theme-medical .cv-name {
    color: white;
    font-weight: 600;
}

.cv-preview.theme-medical .cv-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.cv-preview.theme-medical .cv-section-title {
    color: var(--theme-primary);
    font-weight: 600;
    border-bottom: 2px solid var(--theme-primary);
    padding-bottom: var(--spacing-1);
}

/* Layout Styles */
.cv-content.two-column {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-6);
}

.cv-content.sidebar-left {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-6);
}

.cv-content.sidebar-right {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--spacing-6);
}

.cv-content.header-focus .cv-header {
    padding: var(--spacing-12);
    margin-bottom: var(--spacing-10);
}

/* Photo Integration Styles */
.cv-header.with-photo {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-6);
    align-items: center;
}

.cv-header.with-photo.photo-center {
    grid-template-columns: 1fr;
    text-align: center;
}

.cv-header.with-photo.photo-center .cv-photo {
    margin: 0 auto var(--spacing-4);
}

.cv-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.cv-photo.square {
    border-radius: var(--radius-lg);
}

.cv-photo.rounded {
    border-radius: var(--radius-xl);
}

/* Premium Template Indicators */
.template-card .premium-badge {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 10;
}

.template-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: var(--spacing-2) 0;
    font-size: var(--font-size-xs);
    color: var(--gray-600);
}

.template-rating,
.template-downloads {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
    margin-top: var(--spacing-2);
}

.template-tags .tag {
    background: var(--gray-100);
    color: var(--gray-700);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Theme transition effects */
.cv-preview {
    transition: all 0.3s ease-in-out;
}

.cv-preview * {
    transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
}
