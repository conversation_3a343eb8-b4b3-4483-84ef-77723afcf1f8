// ELASHRAFY CV - Advanced Features Manager
class AdvancedFeaturesManager {
    constructor() {
        this.analytics = {
            templateViews: {},
            templateSelections: {},
            photoUploads: 0,
            pdfExports: 0,
            sessionStart: Date.now()
        };
        
        this.preferences = {
            autoSave: true,
            notifications: true,
            animations: true,
            highContrast: false
        };
        
        this.init();
    }
    
    init() {
        this.setupAdvancedFeatures();
        this.setupAnalytics();
        this.setupPreferences();
        this.setupKeyboardShortcuts();
        this.setupPerformanceOptimizations();
    }
    
    setupAdvancedFeatures() {
        // Enhanced template search
        this.setupTemplateSearch();
        
        // Advanced photo features
        this.setupAdvancedPhotoFeatures();
        
        // Smart auto-save
        this.setupSmartAutoSave();
        
        // Performance monitoring
        this.setupPerformanceMonitoring();
    }
    
    setupTemplateSearch() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = languageManager.getTranslation('search_templates') || 'Search templates...';
        searchInput.className = 'template-search';
        searchInput.style.cssText = `
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        `;
        
        // Add search to template gallery
        const templateCategories = document.querySelector('.template-categories');
        if (templateCategories) {
            templateCategories.parentNode.insertBefore(searchInput, templateCategories);
            
            searchInput.addEventListener('input', (e) => {
                this.searchTemplates(e.target.value);
            });
        }
    }
    
    searchTemplates(query) {
        if (!window.templateManager) return;
        
        const results = templateManager.searchTemplates(query);
        this.displaySearchResults(results);
        
        // Track search analytics
        this.analytics.searches = this.analytics.searches || {};
        this.analytics.searches[query] = (this.analytics.searches[query] || 0) + 1;
    }
    
    displaySearchResults(results) {
        const grid = document.getElementById('templateGrid');
        if (!grid) return;
        
        if (results.length === 0) {
            grid.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search" style="font-size: 48px; color: #9ca3af; margin-bottom: 16px;"></i>
                    <h3 style="color: #6b7280; margin-bottom: 8px;">No templates found</h3>
                    <p style="color: #9ca3af;">Try adjusting your search terms</p>
                </div>
            `;
            return;
        }
        
        grid.innerHTML = results.map(template => 
            templateManager.createTemplateCard(template)
        ).join('');
        
        // Re-attach event listeners
        grid.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', () => {
                const templateId = card.dataset.templateId;
                templateManager.selectTemplate(templateId);
                this.trackTemplateSelection(templateId);
            });
        });
    }
    
    setupAdvancedPhotoFeatures() {
        // Add photo filters
        this.addPhotoFilters();
        
        // Add batch photo processing
        this.setupBatchPhotoProcessing();
        
        // Add photo templates
        this.setupPhotoTemplates();
    }
    
    addPhotoFilters() {
        const photoEditor = document.getElementById('photoEditorModal');
        if (!photoEditor) return;
        
        const controlsContainer = photoEditor.querySelector('.photo-editor-controls');
        if (!controlsContainer) return;
        
        const filtersHTML = `
            <div class="control-group">
                <label>Saturation</label>
                <input type="range" id="saturationSlider" min="-100" max="100" value="0">
            </div>
            <div class="control-group">
                <label>Blur</label>
                <input type="range" id="blurSlider" min="0" max="10" value="0">
            </div>
            <div class="control-group">
                <label>Sepia</label>
                <input type="range" id="sepiaSlider" min="0" max="100" value="0">
            </div>
        `;
        
        controlsContainer.insertAdjacentHTML('beforeend', filtersHTML);
        
        // Add event listeners for new filters
        ['saturation', 'blur', 'sepia'].forEach(filter => {
            const slider = document.getElementById(`${filter}Slider`);
            if (slider) {
                slider.addEventListener('input', () => {
                    this.applyAdvancedFilters();
                });
            }
        });
    }
    
    applyAdvancedFilters() {
        const canvas = document.getElementById('photoCanvas');
        if (!canvas || !window.photoManager) return;
        
        const filters = {
            brightness: document.getElementById('brightnessSlider')?.value || 0,
            contrast: document.getElementById('contrastSlider')?.value || 0,
            saturation: document.getElementById('saturationSlider')?.value || 0,
            blur: document.getElementById('blurSlider')?.value || 0,
            sepia: document.getElementById('sepiaSlider')?.value || 0
        };
        
        // Apply filters to canvas
        const ctx = canvas.getContext('2d');
        ctx.filter = `
            brightness(${100 + parseInt(filters.brightness)}%)
            contrast(${100 + parseInt(filters.contrast)}%)
            saturate(${100 + parseInt(filters.saturation)}%)
            blur(${filters.blur}px)
            sepia(${filters.sepia}%)
        `;
        
        // Redraw image with filters
        photoManager.updatePhotoPreview();
    }
    
    setupSmartAutoSave() {
        let saveTimeout;
        const SAVE_DELAY = 2000; // 2 seconds
        
        // Enhanced auto-save with debouncing
        const smartSave = () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                if (window.cvBuilder) {
                    cvBuilder.saveToLocalStorage();
                    this.showSaveIndicator();
                }
            }, SAVE_DELAY);
        };
        
        // Listen to all form changes
        document.addEventListener('input', smartSave);
        document.addEventListener('change', smartSave);
        
        // Save on visibility change (tab switch, minimize)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && window.cvBuilder) {
                cvBuilder.saveToLocalStorage();
            }
        });
    }
    
    showSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'save-indicator';
        indicator.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>Saved</span>
        `;
        indicator.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #10b981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 10000;
            animation: slideInUp 0.3s ease, fadeOut 0.3s ease 2s forwards;
        `;
        
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 3000);
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S: Save (Export PDF)
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                if (window.pdfExporter) {
                    pdfExporter.exportToPDF();
                }
            }
            
            // Ctrl/Cmd + T: Open Template Gallery
            if ((e.ctrlKey || e.metaKey) && e.key === 't') {
                e.preventDefault();
                if (window.templateManager) {
                    templateManager.openTemplateGallery();
                }
            }
            
            // Ctrl/Cmd + U: Upload Photo
            if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
                e.preventDefault();
                const photoInput = document.getElementById('photoInput');
                if (photoInput) {
                    photoInput.click();
                }
            }
            
            // Escape: Close modals
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.template-gallery-modal.active, .photo-editor-modal.active');
                if (activeModal) {
                    activeModal.classList.remove('active');
                }
            }
            
            // Arrow keys: Navigate form sections
            if (e.key === 'ArrowLeft' && e.altKey) {
                e.preventDefault();
                if (window.cvBuilder) {
                    cvBuilder.previousSection();
                }
            }
            
            if (e.key === 'ArrowRight' && e.altKey) {
                e.preventDefault();
                if (window.cvBuilder) {
                    cvBuilder.nextSection();
                }
            }
        });
    }
    
    setupAnalytics() {
        // Track template views
        this.trackTemplateViews();
        
        // Track user interactions
        this.trackUserInteractions();
        
        // Track performance metrics
        this.trackPerformanceMetrics();
    }
    
    trackTemplateViews() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const templateId = entry.target.dataset.templateId;
                    if (templateId) {
                        this.analytics.templateViews[templateId] = 
                            (this.analytics.templateViews[templateId] || 0) + 1;
                    }
                }
            });
        }, { threshold: 0.5 });
        
        // Observe template cards when they're added
        const observeTemplateCards = () => {
            document.querySelectorAll('.template-card').forEach(card => {
                observer.observe(card);
            });
        };
        
        // Initial observation
        setTimeout(observeTemplateCards, 1000);
        
        // Re-observe when template grid changes
        const templateGrid = document.getElementById('templateGrid');
        if (templateGrid) {
            new MutationObserver(observeTemplateCards).observe(templateGrid, {
                childList: true
            });
        }
    }
    
    trackTemplateSelection(templateId) {
        this.analytics.templateSelections[templateId] = 
            (this.analytics.templateSelections[templateId] || 0) + 1;
        
        // Store analytics
        this.saveAnalytics();
    }
    
    trackUserInteractions() {
        // Track clicks
        document.addEventListener('click', (e) => {
            const target = e.target.closest('[data-track]');
            if (target) {
                const action = target.dataset.track;
                this.analytics.interactions = this.analytics.interactions || {};
                this.analytics.interactions[action] = 
                    (this.analytics.interactions[action] || 0) + 1;
            }
        });
        
        // Track form completion
        this.trackFormCompletion();
    }
    
    trackFormCompletion() {
        const requiredFields = document.querySelectorAll('[required]');
        const totalFields = requiredFields.length;
        
        const updateCompletion = () => {
            const completedFields = Array.from(requiredFields)
                .filter(field => field.value.trim() !== '').length;
            
            const completionRate = Math.round((completedFields / totalFields) * 100);
            this.analytics.formCompletion = completionRate;
            
            // Track milestones
            if (completionRate >= 25 && !this.analytics.milestone25) {
                this.analytics.milestone25 = Date.now();
            }
            if (completionRate >= 50 && !this.analytics.milestone50) {
                this.analytics.milestone50 = Date.now();
            }
            if (completionRate >= 75 && !this.analytics.milestone75) {
                this.analytics.milestone75 = Date.now();
            }
            if (completionRate === 100 && !this.analytics.milestone100) {
                this.analytics.milestone100 = Date.now();
            }
        };
        
        document.addEventListener('input', updateCompletion);
        document.addEventListener('change', updateCompletion);
    }
    
    setupPerformanceOptimizations() {
        // Lazy load template images
        this.setupLazyLoading();
        
        // Optimize rendering
        this.setupRenderOptimization();
        
        // Memory management
        this.setupMemoryManagement();
    }
    
    setupLazyLoading() {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });
        
        // Observe images with data-src
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    saveAnalytics() {
        try {
            localStorage.setItem('elashrafy-cv-analytics', JSON.stringify(this.analytics));
        } catch (error) {
            console.warn('Could not save analytics:', error);
        }
    }
    
    loadAnalytics() {
        try {
            const saved = localStorage.getItem('elashrafy-cv-analytics');
            if (saved) {
                this.analytics = { ...this.analytics, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.warn('Could not load analytics:', error);
        }
    }
    
    getAnalyticsReport() {
        const sessionDuration = Date.now() - this.analytics.sessionStart;
        
        return {
            ...this.analytics,
            sessionDuration: Math.round(sessionDuration / 1000), // in seconds
            mostViewedTemplate: this.getMostViewedTemplate(),
            mostSelectedTemplate: this.getMostSelectedTemplate(),
            averageCompletionTime: this.getAverageCompletionTime()
        };
    }
    
    getMostViewedTemplate() {
        const views = this.analytics.templateViews;
        return Object.keys(views).reduce((a, b) => views[a] > views[b] ? a : b, '');
    }
    
    getMostSelectedTemplate() {
        const selections = this.analytics.templateSelections;
        return Object.keys(selections).reduce((a, b) => selections[a] > selections[b] ? a : b, '');
    }
    
    getAverageCompletionTime() {
        const milestones = [
            this.analytics.milestone25,
            this.analytics.milestone50,
            this.analytics.milestone75,
            this.analytics.milestone100
        ].filter(Boolean);
        
        if (milestones.length < 2) return null;
        
        const totalTime = milestones[milestones.length - 1] - this.analytics.sessionStart;
        return Math.round(totalTime / 1000); // in seconds
    }
}

// Initialize advanced features
const advancedFeatures = new AdvancedFeaturesManager();

// Export for global access
window.advancedFeatures = advancedFeatures;
