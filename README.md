# ELASHRAFY CV - Professional Resume Builder | الأشرافي للسيرة الذاتية المهنية

The ultimate professional CV/resume builder with world-class design, 400+ premium templates, advanced photo integration, and full bilingual support (Arabic/English). Built to compete with international platforms while maintaining the highest standards of quality and user experience.

## Features

### 🌟 Core Features
- **400+ Professional Templates**: Extensive library of industry-specific CV designs
- **Advanced Photo Integration**: Upload, edit, crop, and position profile photos with professional tools
- **Bilingual Excellence**: Full Arabic and English language support with proper RTL/LTR text direction
- **World-Class UI**: Premium interface designed to compete with international platforms
- **Real-time Preview**: See your CV update as you type with instant template switching
- **Enhanced PDF Export**: Generate high-quality, ATS-compatible PDF files with perfect rendering
- **Template Gallery**: Browse templates by category (Executive, Academic, Technical, Medical, etc.)
- **Photo Editor**: Built-in image editing with brightness, contrast, rotation, and shape controls
- **Auto-save**: Your progress is automatically saved locally with cloud sync ready

### 🎨 Design Features
- **Modern UI/UX**: Clean, professional interface with smooth animations
- **CSS Grid & Flexbox**: Modern layout techniques for perfect responsiveness
- **Custom Themes**: Multiple professionally designed CV templates
- **Print-friendly**: Optimized for both screen and print
- **Accessibility**: Keyboard navigation and screen reader support

### 🔧 Technical Features
- **Pure Web Technologies**: HTML5, CSS3, and vanilla JavaScript
- **No Backend Required**: Runs entirely in the browser
- **Local Storage**: Data persistence without server dependency
- **Progressive Enhancement**: Works even with JavaScript disabled (basic functionality)
- **Cross-browser Compatible**: Supports all modern browsers

## Getting Started

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- No server or installation required

### Installation
1. Download or clone this repository
2. Open `index.html` in your web browser
3. Start building your professional CV!

### Usage

#### 1. Personal Information
- Fill in your basic contact details
- Add a professional summary
- Include social media links

#### 2. Work Experience
- Add multiple work experiences
- Include job descriptions and achievements
- Mark current positions

#### 3. Education
- Add your educational background
- Include degrees, institutions, and dates
- Add GPA if desired

#### 4. Skills
- Add technical and soft skills
- Set proficiency levels (Beginner to Expert)
- Skills are displayed with visual indicators

#### 5. Languages
- Add language proficiencies
- Set levels from Basic to Native
- Important for international opportunities

#### 6. Export
- Choose your preferred theme
- Adjust zoom level for preview
- Export as high-quality PDF

## Language Support

### Switching Languages
- Click the language toggle in the header (EN/عر)
- All interface elements update automatically
- Text direction changes appropriately (LTR/RTL)

### Supported Languages
- **English**: Full interface and content support
- **Arabic**: Complete RTL support with proper fonts

## Themes

### Available Themes
1. **Modern**: Contemporary design with gradient headers
2. **Classic**: Traditional professional layout
3. **Creative**: Colorful and dynamic design
4. **Minimal**: Clean, simple black and white

### Customization
- Themes automatically adapt to content
- Print-optimized versions for PDF export
- Responsive across all device sizes

## Browser Support

| Browser | Version | Support |
|---------|---------|---------|
| Chrome  | 80+     | ✅ Full |
| Firefox | 75+     | ✅ Full |
| Safari  | 13+     | ✅ Full |
| Edge    | 80+     | ✅ Full |

## File Structure

```
cv-builder/
├── index.html              # Main application file
├── css/
│   ├── styles.css          # Main stylesheet
│   ├── themes.css          # CV theme styles
│   └── responsive.css      # Responsive design
├── js/
│   ├── app.js             # Main application logic
│   ├── cv-builder.js      # Form handling and data management
│   ├── pdf-export.js      # PDF generation functionality
│   └── language.js        # Bilingual support
├── data/
│   └── sample-data.json   # Sample CV data
└── README.md              # This file
```

## Technical Details

### Dependencies
- **jsPDF**: PDF generation library
- **html2canvas**: HTML to canvas conversion
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Inter, Noto Sans Arabic)

### Data Storage
- **Local Storage**: Form data is automatically saved
- **JSON Export**: Export your data for backup
- **Import Functionality**: Restore from exported data

### Performance
- **Optimized Assets**: Compressed CSS and JavaScript
- **Lazy Loading**: Images and fonts load as needed
- **Efficient Rendering**: Minimal DOM manipulation

## Customization

### Adding New Themes
1. Create theme styles in `css/themes.css`
2. Add theme option to the selector
3. Update theme switching logic

### Extending Languages
1. Add translations to `js/language.js`
2. Update language selector
3. Test RTL/LTR functionality

### Custom Fields
1. Add form fields in `index.html`
2. Update form handling in `cv-builder.js`
3. Modify preview generation in `app.js`

## Troubleshooting

### Common Issues

**PDF Export Not Working**
- Ensure you're using a modern browser
- Check if JavaScript is enabled
- Try refreshing the page

**Language Not Switching**
- Clear browser cache
- Check browser language settings
- Ensure JavaScript is enabled

**Preview Not Updating**
- Check form validation errors
- Ensure all required fields are filled
- Try refreshing the page

### Browser Console
Open browser developer tools (F12) to see any error messages.

## Contributing

We welcome contributions! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

For support, please:
- Check the troubleshooting section
- Open an issue on GitHub
- Contact the development team

## Roadmap

### Upcoming Features
- [ ] Additional CV themes
- [ ] More language support
- [ ] Cloud storage integration
- [ ] Advanced formatting options
- [ ] Template marketplace
- [ ] Collaboration features

---

**Built with ❤️ for the global community**

*Professional CV Builder - Create stunning resumes in minutes*
