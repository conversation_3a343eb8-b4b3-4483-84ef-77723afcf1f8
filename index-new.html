<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELASHRAFY CV - منصة السيرة الذاتية الاحترافية</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --error-color: #ef4444;
            --gold-color: #ffd700;
            --bg-color: #f8fafc;
            --text-color: #1f2937;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: var(--shadow-lg);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Main Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            padding: 30px 0;
        }

        .sidebar {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow);
            height: fit-content;
        }

        .preview-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow);
            min-height: 800px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--primary-color);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-family: inherit;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Templates Grid */
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .template-card {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
            position: relative;
        }

        .template-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .template-card.selected {
            border-color: var(--success-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
        }

        .template-preview {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: var(--border-color);
        }

        .template-info {
            padding: 15px;
        }

        .template-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-color);
        }

        .template-category {
            font-size: 0.8rem;
            color: #6b7280;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        /* Preview Area */
        .preview-container {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            min-height: 600px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .preview-placeholder {
            text-align: center;
            color: #6b7280;
        }

        .preview-placeholder i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #d1d5db;
        }

        .cv-preview {
            width: 100%;
            max-width: 794px;
            background: white;
            box-shadow: var(--shadow-lg);
            border-radius: 8px;
            overflow: hidden;
            transform-origin: top center;
            padding: 40px;
            margin: 20px;
            min-height: 600px;
        }

        /* Actions */
        .actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Loading */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: var(--shadow-lg);
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* CV Styles */
        .cv-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--primary-color);
        }

        .cv-name {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .cv-title {
            font-size: 1.2rem;
            color: var(--text-color);
            margin-bottom: 15px;
        }

        .cv-contact {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .cv-section {
            margin-bottom: 30px;
        }

        .cv-section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
        }

        .cv-content {
            line-height: 1.8;
            color: var(--text-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px 0;
            }

            .header h1 {
                font-size: 2rem;
            }

            .templates-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .actions {
                flex-direction: column;
            }
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>ELASHRAFY CV</h1>
            <p>منصة السيرة الذاتية الاحترافية - صمم سيرتك الذاتية بأعلى معايير الجودة العالمية</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container">
        <div class="main-layout">
            <!-- Sidebar -->
            <aside class="sidebar">
                <h2 class="section-title">
                    <i class="fas fa-layer-group"></i>
                    القوالب الاحترافية
                </h2>

                <!-- Templates Grid -->
                <div id="templatesGrid" class="templates-grid">
                    <!-- Templates will be loaded here -->
                </div>

                <!-- Form Section -->
                <div id="formSection" class="hidden">
                    <h3 class="section-title">
                        <i class="fas fa-edit"></i>
                        تحرير البيانات
                    </h3>

                    <form id="cvForm">
                        <div class="form-group">
                            <label for="fullName">الاسم الكامل</label>
                            <input type="text" id="fullName" name="fullName" class="form-control" placeholder="أدخل اسمك الكامل">
                        </div>

                        <div class="form-group">
                            <label for="jobTitle">المسمى الوظيفي</label>
                            <input type="text" id="jobTitle" name="jobTitle" class="form-control" placeholder="مثل: مطور ويب، مصمم جرافيك">
                        </div>

                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone" class="form-control" placeholder="+966 50 123 4567">
                        </div>

                        <div class="form-group">
                            <label for="summary">الملخص المهني</label>
                            <textarea id="summary" name="summary" class="form-control" placeholder="اكتب ملخصاً مهنياً يبرز خبراتك ومهاراتك"></textarea>
                        </div>
                    </form>
                </div>

                <!-- Actions -->
                <div class="actions">
                    <button id="generateAI" class="btn btn-primary">
                        <i class="fas fa-robot"></i>
                        إنشاء بالذكاء الاصطناعي
                    </button>
                    <button id="editManual" class="btn btn-secondary">
                        <i class="fas fa-edit"></i>
                        تحرير يدوي
                    </button>
                </div>
            </aside>

            <!-- Preview Area -->
            <section class="preview-area">
                <h2 class="section-title">
                    <i class="fas fa-eye"></i>
                    المعاينة المباشرة
                </h2>

                <div id="previewContainer" class="preview-container">
                    <div class="preview-placeholder">
                        <i class="fas fa-file-alt"></i>
                        <h3>اختر قالباً لبدء المعاينة</h3>
                        <p>ستظهر هنا معاينة مباشرة لسيرتك الذاتية</p>
                    </div>
                </div>

                <!-- Export Actions -->
                <div class="actions">
                    <button id="exportPDF" class="btn btn-success">
                        <i class="fas fa-download"></i>
                        تصدير PDF
                    </button>
                    <button id="saveCV" class="btn btn-secondary">
                        <i class="fas fa-save"></i>
                        حفظ المشروع
                    </button>
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>جاري المعالجة...</h3>
            <p id="loadingText">يرجى الانتظار</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // ELASHRAFY CV Application
        class ElashrafyCV {
            constructor() {
                this.currentTemplate = null;
                this.cvData = {
                    personalInfo: {
                        name: '',
                        title: '',
                        email: '',
                        phone: '',
                        summary: ''
                    }
                };
                this.templates = this.generateTemplates();
                this.init();
            }

            init() {
                console.log('🚀 ELASHRAFY CV Application Starting...');
                this.loadTemplates();
                this.setupEventListeners();
                this.showNotification('مرحباً بك في ELASHRAFY CV - منصة السيرة الذاتية الاحترافية', 'success');
            }

            generateTemplates() {
                const categories = ['modern', 'creative', 'executive', 'academic', 'technical'];
                const styles = ['glass-morphism', 'neo-brutalism', 'gradient-mesh', 'organic-shapes', 'geometric-art'];
                const templates = [];

                for (let i = 0; i < 20; i++) {
                    const category = categories[i % categories.length];
                    const style = styles[i % styles.length];

                    templates.push({
                        id: `template_${i + 1}`,
                        name: `قالب ${this.getArabicNumber(i + 1)} - ${this.getCategoryName(category)}`,
                        category: category,
                        style: style,
                        description: `قالب احترافي ${this.getCategoryName(category)} بتصميم ${this.getStyleName(style)}`,
                        isPremium: i % 3 === 0,
                        rating: 4.5 + Math.random() * 0.5,
                        downloads: Math.floor(Math.random() * 10000) + 1000
                    });
                }

                return templates;
            }

            getArabicNumber(num) {
                const arabicNumbers = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس', 'السابع', 'الثامن', 'التاسع', 'العاشر'];
                return arabicNumbers[num - 1] || `رقم ${num}`;
            }

            getCategoryName(category) {
                const names = {
                    'modern': 'عصري',
                    'creative': 'إبداعي',
                    'executive': 'تنفيذي',
                    'academic': 'أكاديمي',
                    'technical': 'تقني'
                };
                return names[category] || category;
            }

            getStyleName(style) {
                const names = {
                    'glass-morphism': 'زجاجي شفاف',
                    'neo-brutalism': 'جريء عصري',
                    'gradient-mesh': 'متدرج شبكي',
                    'organic-shapes': 'أشكال عضوية',
                    'geometric-art': 'هندسي فني'
                };
                return names[style] || style;
            }

            loadTemplates() {
                const grid = document.getElementById('templatesGrid');
                if (!grid) return;

                grid.innerHTML = '';

                this.templates.forEach(template => {
                    const card = this.createTemplateCard(template);
                    grid.appendChild(card);
                });
            }

            createTemplateCard(template) {
                const card = document.createElement('div');
                card.className = 'template-card';
                card.dataset.templateId = template.id;

                card.innerHTML = `
                    <div class="template-preview">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="template-info">
                        <div class="template-name">${template.name}</div>
                        <div class="template-category">${this.getCategoryName(template.category)}</div>
                        ${template.isPremium ? '<div class="premium-badge">مميز</div>' : ''}
                    </div>
                `;

                card.addEventListener('click', () => {
                    this.selectTemplate(template);
                });

                return card;
            }

            selectTemplate(template) {
                // Remove previous selection
                document.querySelectorAll('.template-card').forEach(card => {
                    card.classList.remove('selected');
                });

                // Select current template
                const card = document.querySelector(`[data-template-id="${template.id}"]`);
                if (card) {
                    card.classList.add('selected');
                }

                this.currentTemplate = template;
                this.generatePreview();
                this.showNotification(`تم اختيار ${template.name}`, 'success');
            }

            generatePreview() {
                if (!this.currentTemplate) return;

                const container = document.getElementById('previewContainer');
                if (!container) return;

                // Show loading
                this.showLoading('جاري إنشاء المعاينة...');

                setTimeout(() => {
                    const cvData = this.getCVData();
                    const previewHTML = this.generateCVHTML(cvData);

                    container.innerHTML = `
                        <div class="cv-preview" id="cvPreview">
                            ${previewHTML}
                        </div>
                    `;

                    this.hideLoading();
                    this.showNotification('تم إنشاء المعاينة بنجاح', 'success');
                }, 1000);
            }

            getCVData() {
                return {
                    personalInfo: {
                        name: document.getElementById('fullName')?.value || 'اسمك الكامل',
                        title: document.getElementById('jobTitle')?.value || 'المسمى الوظيفي',
                        email: document.getElementById('email')?.value || 'البريد الإلكتروني',
                        phone: document.getElementById('phone')?.value || 'رقم الهاتف',
                        summary: document.getElementById('summary')?.value || 'أضف ملخصاً مهنياً يبرز خبراتك ومهاراتك الأساسية'
                    }
                };
            }

            generateCVHTML(data) {
                return `
                    <div class="cv-header">
                        <div class="cv-name">${data.personalInfo.name}</div>
                        <div class="cv-title">${data.personalInfo.title}</div>
                        <div class="cv-contact">
                            <span><i class="fas fa-envelope"></i> ${data.personalInfo.email}</span>
                            <span><i class="fas fa-phone"></i> ${data.personalInfo.phone}</span>
                        </div>
                    </div>

                    <div class="cv-section">
                        <div class="cv-section-title">الملخص المهني</div>
                        <div class="cv-content">${data.personalInfo.summary}</div>
                    </div>

                    <div class="cv-section">
                        <div class="cv-section-title">الخبرات المهنية</div>
                        <div class="cv-content">
                            <div style="margin-bottom: 15px;">
                                <strong>مطور ويب أول</strong> - شركة التقنية المتقدمة<br>
                                <small style="color: #6b7280;">2020 - حتى الآن</small><br>
                                تطوير وصيانة تطبيقات الويب باستخدام أحدث التقنيات والأدوات.
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>مطور ويب</strong> - شركة الحلول الرقمية<br>
                                <small style="color: #6b7280;">2018 - 2020</small><br>
                                العمل على مشاريع متنوعة وتطوير واجهات المستخدم التفاعلية.
                            </div>
                        </div>
                    </div>

                    <div class="cv-section">
                        <div class="cv-section-title">المهارات</div>
                        <div class="cv-content">
                            JavaScript, React, Node.js, Python, HTML/CSS, Git, Docker, AWS
                        </div>
                    </div>
                `;
            }

            setupEventListeners() {
                // Generate AI button
                const generateAI = document.getElementById('generateAI');
                if (generateAI) {
                    generateAI.addEventListener('click', () => {
                        this.generateWithAI();
                    });
                }

                // Edit Manual button
                const editManual = document.getElementById('editManual');
                if (editManual) {
                    editManual.addEventListener('click', () => {
                        this.showEditForm();
                    });
                }

                // Export PDF button
                const exportPDF = document.getElementById('exportPDF');
                if (exportPDF) {
                    exportPDF.addEventListener('click', () => {
                        this.exportToPDF();
                    });
                }

                // Form inputs live update
                const formInputs = document.querySelectorAll('#cvForm input, #cvForm textarea');
                formInputs.forEach(input => {
                    input.addEventListener('input', () => {
                        if (this.currentTemplate) {
                            this.generatePreview();
                        }
                    });
                });
            }

            generateWithAI() {
                this.showLoading('جاري إنشاء السيرة الذاتية بالذكاء الاصطناعي...');

                // Simulate AI generation
                setTimeout(() => {
                    // Fill form with AI-generated data
                    document.getElementById('fullName').value = 'أحمد محمد الأشرافي';
                    document.getElementById('jobTitle').value = 'مطور ويب متقدم';
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('phone').value = '+966 50 123 4567';
                    document.getElementById('summary').value = 'مطور ويب متخصص مع خبرة 5+ سنوات في تطوير التطبيقات الحديثة باستخدام JavaScript, React, و Node.js. شغوف بإنشاء حلول تقنية مبتكرة وتحسين تجربة المستخدم.';

                    // Select a random template
                    const randomTemplate = this.templates[Math.floor(Math.random() * this.templates.length)];
                    this.selectTemplate(randomTemplate);

                    this.hideLoading();
                    this.showEditForm();
                    this.showNotification('تم إنشاء السيرة الذاتية بالذكاء الاصطناعي بنجاح!', 'success');
                }, 2000);
            }

            showEditForm() {
                const formSection = document.getElementById('formSection');
                const templatesGrid = document.getElementById('templatesGrid');

                if (formSection && templatesGrid) {
                    formSection.classList.remove('hidden');
                    templatesGrid.style.display = 'none';
                }
            }

            async exportToPDF() {
                const cvPreview = document.getElementById('cvPreview');
                if (!cvPreview) {
                    this.showNotification('يرجى إنشاء معاينة أولاً', 'error');
                    return;
                }

                this.showLoading('جاري تصدير PDF...');

                try {
                    // Create canvas from CV preview
                    const canvas = await html2canvas(cvPreview, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: false,
                        backgroundColor: '#ffffff'
                    });

                    // Create PDF
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'mm',
                        format: 'a4'
                    });

                    const imgData = canvas.toDataURL('image/png');
                    const imgWidth = 210;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;

                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

                    // Download PDF
                    const fileName = `ELASHRAFY_CV_${new Date().getTime()}.pdf`;
                    pdf.save(fileName);

                    this.hideLoading();
                    this.showNotification('تم تصدير PDF بنجاح!', 'success');

                } catch (error) {
                    console.error('PDF Export Error:', error);
                    this.hideLoading();
                    this.showNotification('حدث خطأ أثناء التصدير', 'error');
                }
            }

            showLoading(text = 'جاري المعالجة...') {
                const overlay = document.getElementById('loadingOverlay');
                const loadingText = document.getElementById('loadingText');

                if (overlay && loadingText) {
                    loadingText.textContent = text;
                    overlay.classList.remove('hidden');
                }
            }

            hideLoading() {
                const overlay = document.getElementById('loadingOverlay');
                if (overlay) {
                    overlay.classList.add('hidden');
                }
            }

            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10001;
                    max-width: 300px;
                    font-weight: 600;
                    animation: slideIn 0.3s ease;
                `;

                notification.textContent = message;
                document.body.appendChild(notification);

                // Remove after 3 seconds
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }
        }

        // Initialize application when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.elashrafyCV = new ElashrafyCV();
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
                                <stop offset="100%" style="stop-color:#8b5cf6"/>
                            </linearGradient>
                            <filter id="brandShadow" x="-20%" y="-20%" width="140%" height="140%">
                                <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e40af" flood-opacity="0.3"/>
                            </filter>
                        </defs>
                        <rect width="50" height="50" rx="12" fill="url(#brandGradient)" filter="url(#brandShadow)"/>
                        <rect x="10" y="12" width="30" height="26" rx="3" fill="white" opacity="0.95"/>
                        <rect x="14" y="18" width="22" height="2" fill="url(#brandGradient)" rx="1"/>
                        <rect x="14" y="22" width="18" height="2" fill="url(#brandGradient)" rx="1" opacity="0.8"/>
                        <rect x="14" y="26" width="20" height="2" fill="url(#brandGradient)" rx="1" opacity="0.6"/>
                        <rect x="14" y="30" width="16" height="2" fill="url(#brandGradient)" rx="1" opacity="0.4"/>
                        <circle cx="20" cy="15" r="2" fill="url(#brandGradient)"/>
                    </svg>
                </div>
                <div class="brand-text">
                    <h1 class="brand-title">ELASHRAFY CV</h1>
                    <p class="brand-subtitle">منصة السيرة الذاتية الاحترافية العالمية</p>
                </div>
            </div>
            
            <nav class="main-navigation">
                <button class="nav-btn active" data-section="templates">
                    <i class="fas fa-layer-group"></i>
                    <span>القوالب</span>
                    <span class="nav-badge">400+</span>
                </button>
                <button class="nav-btn" data-section="editor">
                    <i class="fas fa-edit"></i>
                    <span>المحرر</span>
                </button>
                <button class="nav-btn" data-section="preview">
                    <i class="fas fa-eye"></i>
                    <span>المعاينة</span>
                </button>
                <button class="nav-btn" data-section="features">
                    <i class="fas fa-star"></i>
                    <span>الميزات</span>
                </button>
            </nav>
            
            <div class="header-actions">
                <button class="action-btn secondary" id="saveBtn">
                    <i class="fas fa-save"></i>
                    <span>حفظ</span>
                </button>
                <button class="action-btn primary" id="exportBtn">
                    <i class="fas fa-download"></i>
                    <span>تصدير PDF</span>
                </button>
                <button class="language-toggle" id="languageToggle">
                    <i class="fas fa-globe"></i>
                    <span class="lang-text">عر</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Application Container -->
    <main class="app-main">
        <!-- Templates Gallery Section -->
        <section class="app-section active" id="templatesSection">
            <div class="section-container">
                <div class="section-header">
                    <div class="header-content">
                        <h2 class="section-title">
                            <i class="fas fa-layer-group"></i>
                            اختر قالبك المثالي
                        </h2>
                        <p class="section-subtitle">أكثر من 400 قالب احترافي مصمم بعناية لجميع المجالات والتخصصات</p>
                    </div>
                    
                    <div class="templates-controls">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search"></i>
                                <input type="text" id="templateSearch" placeholder="ابحث في القوالب..." autocomplete="off">
                            </div>
                            <div class="search-filters">
                                <select id="categoryFilter" class="filter-select">
                                    <option value="all">جميع الفئات</option>
                                    <option value="modern">عصري</option>
                                    <option value="creative">إبداعي</option>
                                    <option value="executive">تنفيذي</option>
                                    <option value="academic">أكاديمي</option>
                                    <option value="technical">تقني</option>
                                    <option value="medical">طبي</option>
                                    <option value="business">تجاري</option>
                                    <option value="legal">قانوني</option>
                                    <option value="artistic">فني</option>
                                    <option value="minimalist">مينيمال</option>
                                    <option value="luxury">فاخر</option>
                                </select>
                                
                                <select id="styleFilter" class="filter-select">
                                    <option value="all">جميع الأنماط</option>
                                    <option value="glass-morphism">زجاجي شفاف</option>
                                    <option value="neo-brutalism">جريء عصري</option>
                                    <option value="gradient-mesh">تدرج شبكي</option>
                                    <option value="organic-shapes">أشكال عضوية</option>
                                    <option value="geometric-art">فن هندسي</option>
                                    <option value="watercolor">ألوان مائية</option>
                                    <option value="neon-cyber">نيون سايبر</option>
                                    <option value="crystal-clear">كريستال صافي</option>
                                </select>
                                
                                <button class="filter-btn" id="advancedFilters">
                                    <i class="fas fa-sliders-h"></i>
                                    فلاتر متقدمة
                                </button>
                            </div>
                        </div>
                        
                        <div class="view-controls">
                            <div class="view-toggle">
                                <button class="view-btn active" data-view="grid">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="view-btn" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                            
                            <div class="sort-controls">
                                <select id="sortBy" class="sort-select">
                                    <option value="popular">الأكثر شعبية</option>
                                    <option value="newest">الأحدث</option>
                                    <option value="rating">الأعلى تقييماً</option>
                                    <option value="name">الاسم</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Templates Statistics -->
                <div class="templates-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalTemplates">400+</div>
                            <div class="stat-label">قالب احترافي</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">15</div>
                            <div class="stat-label">فئة متخصصة</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">12</div>
                            <div class="stat-label">نمط فني</div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">100K+</div>
                            <div class="stat-label">تحميل شهرياً</div>
                        </div>
                    </div>
                </div>
                
                <!-- Templates Grid -->
                <div class="templates-container">
                    <div class="templates-grid" id="templatesGrid">
                        <!-- Templates will be loaded dynamically -->
                        <div class="loading-templates">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                            <p>جاري تحميل القوالب الجميلة...</p>
                        </div>
                    </div>
                    
                    <!-- Load More Button -->
                    <div class="load-more-container">
                        <button class="load-more-btn" id="loadMoreTemplates">
                            <i class="fas fa-plus"></i>
                            تحميل المزيد من القوالب
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- CV Editor Section -->
        <section class="app-section" id="editorSection">
            <div class="editor-container">
                <!-- Editor will be loaded here -->
                <div class="editor-placeholder">
                    <div class="placeholder-content">
                        <i class="fas fa-edit"></i>
                        <h3>محرر السيرة الذاتية المتقدم</h3>
                        <p>اختر قالباً أولاً لبدء التحرير</p>
                        <button class="btn primary" onclick="showSection('templates')">
                            <i class="fas fa-layer-group"></i>
                            اختيار قالب
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Preview Section -->
        <section class="app-section" id="previewSection">
            <div class="preview-container">
                <!-- Preview will be loaded here -->
                <div class="preview-placeholder">
                    <div class="placeholder-content">
                        <i class="fas fa-eye"></i>
                        <h3>معاينة السيرة الذاتية</h3>
                        <p>ابدأ بإنشاء سيرتك الذاتية لرؤية المعاينة</p>
                        <button class="btn primary" onclick="showSection('templates')">
                            <i class="fas fa-layer-group"></i>
                            البدء الآن
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="app-section" id="featuresSection">
            <div class="features-container">
                <div class="features-header">
                    <h2>ميزات احترافية متقدمة</h2>
                    <p>كل ما تحتاجه لإنشاء سيرة ذاتية مثالية</p>
                </div>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3>400+ قالب احترافي</h3>
                        <p>مجموعة ضخمة من القوالب المصممة بعناية لجميع المجالات والتخصصات</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h3>محرر متقدم</h3>
                        <p>محرر قوي مع إمكانيات تحرير متقدمة وواجهة سهلة الاستخدام</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-camera"></i>
                        </div>
                        <h3>تحرير الصور المتقدم</h3>
                        <p>أدوات تحرير صور احترافية مع فلاتر وتأثيرات متنوعة</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <h3>تصدير عالي الجودة</h3>
                        <p>تصدير PDF بجودة عالية مع دعم الطباعة والمشاركة</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h3>دعم متعدد اللغات</h3>
                        <p>واجهة باللغتين العربية والإنجليزية مع دعم RTL/LTR</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>تصميم متجاوب</h3>
                        <p>يعمل بشكل مثالي على جميع الأجهزة والشاشات</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="professional-footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                            <rect width="40" height="40" rx="8" fill="url(#footerGradient)"/>
                            <path d="M12 10h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H12a2 2 0 0 1-2-2V12a2 2 0 0 1 2-2z" fill="white" opacity="0.9"/>
                            <path d="M16 16h8M16 20h8M16 24h6" stroke="url(#footerGradient2)" stroke-width="1.5" stroke-linecap="round"/>
                            <circle cx="20" cy="14" r="1.5" fill="url(#footerGradient2)"/>
                            <defs>
                                <linearGradient id="footerGradient" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#1e40af"/>
                                    <stop offset="1" stop-color="#3b82f6"/>
                                </linearGradient>
                                <linearGradient id="footerGradient2" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#1e40af"/>
                                    <stop offset="1" stop-color="#8b5cf6"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <div class="footer-text">
                        <h3>ELASHRAFY CV</h3>
                        <p>منصة السيرة الذاتية الاحترافية العالمية</p>
                    </div>
                </div>
                
                <div class="developer-credit">
                    <div class="developer-avatar">
                        <svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="25" cy="25" r="25" fill="url(#developerGradient)"/>
                            <text x="25" y="32" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">م.أ</text>
                            <defs>
                                <linearGradient id="developerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <div class="developer-info">
                        <h4>تصميم وتطوير</h4>
                        <h3>محمد الأشرافي</h3>
                        <p>مطور ومصمم تطبيقات الويب المتقدمة</p>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 ELASHRAFY CV. جميع الحقوق محفوظة.</p>
                <p class="developer-signature">
                    صُنع بـ <i class="fas fa-heart" style="color: #ef4444;"></i> بواسطة 
                    <strong>محمد الأشرافي</strong>
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- Core Application Scripts -->
    <script src="js/ready-templates-database.js"></script>
    <script src="js/templates-display.js"></script>
    <script src="js/professional-app.js"></script>

    <!-- Feature Scripts -->
    <script src="js/advanced-editor.js"></script>
    <script src="js/live-preview.js"></script>
    <script src="js/professional-features.js"></script>

    <!-- Initialize Application -->
    <script>
        // Wait for all scripts to load then initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 ELASHRAFY CV Application Starting...');

            // Check if all required components are loaded
            const requiredComponents = [
                'readyTemplatesDB',
                'templatesDisplay',
                'professionalApp'
            ];

            const checkComponents = () => {
                const missing = requiredComponents.filter(comp => !window[comp]);

                if (missing.length === 0) {
                    console.log('✅ All components loaded successfully');
                    console.log(`📊 ${window.readyTemplatesDB.templates.length} templates ready`);

                    // Show welcome message
                    setTimeout(() => {
                        if (window.professionalApp && window.professionalApp.showNotification) {
                            window.professionalApp.showNotification(
                                'مرحباً بك في ELASHRAFY CV - منصة السيرة الذاتية الاحترافية العالمية! 🎉',
                                'success'
                            );
                        }
                    }, 1000);
                } else {
                    console.log('⏳ Waiting for components:', missing);
                    setTimeout(checkComponents, 100);
                }
            };

            checkComponents();
        });
    </script>
</body>
</html>
