<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELASHRAFY CV - منصة السيرة الذاتية الاحترافية</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #7c3aed;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --error-color: #ef4444;
            --gold-color: #ffd700;
            --bg-color: #f8fafc;
            --text-color: #1f2937;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: var(--shadow-lg);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* Main Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            padding: 30px 0;
        }

        .sidebar {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow);
            height: fit-content;
        }

        .preview-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow);
            min-height: 800px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--primary-color);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-family: inherit;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: var(--shadow);
        }

        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Templates Grid */
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .template-card {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            background: white;
            position: relative;
        }

        .template-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .template-card.selected {
            border-color: var(--success-color);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
        }

        .template-preview {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: var(--text-color);
            position: relative;
            overflow: hidden;
        }

        .template-preview-content {
            width: 90%;
            height: 90%;
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .template-header-preview {
            text-align: center;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--primary-color);
        }

        .template-name-preview {
            font-weight: 700;
            font-size: 0.9rem;
            color: var(--primary-color);
        }

        .template-title-preview {
            font-size: 0.7rem;
            color: var(--text-color);
            margin-top: 2px;
        }

        .template-section-preview {
            margin-top: 8px;
        }

        .template-section-title {
            font-size: 0.6rem;
            font-weight: 600;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 2px;
            margin-bottom: 4px;
        }

        .template-content-line {
            height: 3px;
            background: var(--border-color);
            border-radius: 2px;
            margin-bottom: 2px;
        }

        .template-content-line.short {
            width: 70%;
        }

        .template-content-line.medium {
            width: 85%;
        }

        .template-content-line.long {
            width: 100%;
        }

        .premium-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, var(--gold-color), var(--accent-color));
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
        }

        .template-info {
            padding: 15px;
        }

        .template-name {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-color);
        }

        .template-category {
            font-size: 0.8rem;
            color: #6b7280;
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        /* Preview Area */
        .preview-container {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            min-height: 600px;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .preview-placeholder {
            text-align: center;
            color: #6b7280;
        }

        .preview-placeholder i {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #d1d5db;
        }

        .cv-preview {
            width: 100%;
            max-width: 794px;
            background: white;
            box-shadow: var(--shadow-lg);
            border-radius: 8px;
            overflow: hidden;
            transform-origin: top center;
            padding: 40px;
            margin: 20px;
            min-height: 600px;
        }

        /* Actions */
        .actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Loading */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            box-shadow: var(--shadow-lg);
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* CV Styles */
        .cv-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--primary-color);
        }

        .cv-name {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .cv-title {
            font-size: 1.2rem;
            color: var(--text-color);
            margin-bottom: 15px;
        }

        .cv-contact {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .cv-section {
            margin-bottom: 30px;
        }

        .cv-section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--border-color);
        }

        .cv-content {
            line-height: 1.8;
            color: var(--text-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px 0;
            }

            .header h1 {
                font-size: 2rem;
            }

            .templates-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .actions {
                flex-direction: column;
            }
        }

        .hidden {
            display: none !important;
        }

        /* Advanced Editor Styles */
        .advanced-editor {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            height: 100vh;
            overflow: hidden;
        }

        .editor-panel {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: var(--shadow);
            overflow-y: auto;
        }

        .editor-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            border-bottom: 2px solid var(--border-color);
        }

        .editor-tab {
            padding: 12px 20px;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 600;
            color: #6b7280;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .editor-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .editor-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid var(--border-color);
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .editor-section:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-header h3 {
            color: var(--primary-color);
            font-size: 1.2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .add-item-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .add-item-btn:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row.full {
            grid-template-columns: 1fr;
        }

        .item-container {
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
            background: #f8fafc;
        }

        .remove-item-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--error-color);
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        /* AI Assistant Styles */
        .ai-assistant {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 25px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
        }

        .ai-assistant h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .ai-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .ai-option {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .ai-option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .ai-option i {
            font-size: 1.5rem;
            margin-bottom: 10px;
            display: block;
        }

        /* Skills and Tags */
        .skills-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .skill-tag {
            background: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .skill-tag:hover {
            background: #1d4ed8;
            transform: scale(1.05);
        }

        .skill-tag .remove-skill {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
        }

        .skill-input-container {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .skill-input {
            flex: 1;
        }

        .add-skill-btn {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>ELASHRAFY CV</h1>
            <p>منصة السيرة الذاتية الاحترافية - صمم سيرتك الذاتية بأعلى معايير الجودة العالمية</p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container">
        <div class="main-layout">
            <!-- Sidebar -->
            <aside class="sidebar">
                <h2 class="section-title">
                    <i class="fas fa-layer-group"></i>
                    القوالب الاحترافية
                </h2>

                <!-- Templates Grid -->
                <div id="templatesGrid" class="templates-grid">
                    <!-- Templates will be loaded here -->
                </div>

                <!-- AI Assistant Section -->
                <div id="aiSection" class="ai-assistant">
                    <h3>
                        <i class="fas fa-robot"></i>
                        مساعد الذكاء الاصطناعي
                    </h3>
                    <p>دع الذكاء الاصطناعي ينشئ سيرتك الذاتية بشكل احترافي</p>

                    <div class="ai-options">
                        <div class="ai-option" onclick="generateAICV('complete')">
                            <i class="fas fa-magic"></i>
                            <h4>إنشاء كامل</h4>
                            <p>إنشاء سيرة ذاتية كاملة</p>
                        </div>
                        <div class="ai-option" onclick="generateAICV('improve')">
                            <i class="fas fa-lightbulb"></i>
                            <h4>تحسين المحتوى</h4>
                            <p>تحسين النص الموجود</p>
                        </div>
                        <div class="ai-option" onclick="generateAICV('suggestions')">
                            <i class="fas fa-brain"></i>
                            <h4>اقتراحات ذكية</h4>
                            <p>اقتراحات للمهارات والخبرات</p>
                        </div>
                    </div>
                </div>

                <!-- Advanced Editor Section -->
                <div id="advancedEditor" class="hidden">
                    <div class="advanced-editor">
                        <!-- Editor Panel -->
                        <div class="editor-panel">
                            <div class="editor-tabs">
                                <button class="editor-tab active" data-tab="personal">
                                    <i class="fas fa-user"></i>
                                    البيانات الشخصية
                                </button>
                                <button class="editor-tab" data-tab="experience">
                                    <i class="fas fa-briefcase"></i>
                                    الخبرات
                                </button>
                                <button class="editor-tab" data-tab="education">
                                    <i class="fas fa-graduation-cap"></i>
                                    التعليم
                                </button>
                                <button class="editor-tab" data-tab="skills">
                                    <i class="fas fa-cogs"></i>
                                    المهارات
                                </button>
                            </div>

                            <!-- Personal Info Tab -->
                            <div class="editor-content" id="personalTab">
                                <div class="editor-section">
                                    <div class="section-header">
                                        <h3>
                                            <i class="fas fa-user"></i>
                                            المعلومات الشخصية
                                        </h3>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="fullName">الاسم الكامل</label>
                                            <input type="text" id="fullName" name="fullName" class="form-control" placeholder="أدخل اسمك الكامل">
                                        </div>
                                        <div class="form-group">
                                            <label for="jobTitle">المسمى الوظيفي</label>
                                            <input type="text" id="jobTitle" name="jobTitle" class="form-control" placeholder="مثل: مطور ويب، مصمم جرافيك">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="email">البريد الإلكتروني</label>
                                            <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>">
                                        </div>
                                        <div class="form-group">
                                            <label for="phone">رقم الهاتف</label>
                                            <input type="tel" id="phone" name="phone" class="form-control" placeholder="+966 50 123 4567">
                                        </div>
                                    </div>

                                    <div class="form-row full">
                                        <div class="form-group">
                                            <label for="summary">الملخص المهني</label>
                                            <textarea id="summary" name="summary" class="form-control" rows="4" placeholder="اكتب ملخصاً مهنياً يبرز خبراتك ومهاراتك"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Experience Tab -->
                            <div class="editor-content hidden" id="experienceTab">
                                <div class="editor-section">
                                    <div class="section-header">
                                        <h3>
                                            <i class="fas fa-briefcase"></i>
                                            الخبرات المهنية
                                        </h3>
                                        <button class="add-item-btn" onclick="addExperience()">
                                            <i class="fas fa-plus"></i>
                                            إضافة خبرة
                                        </button>
                                    </div>

                                    <div id="experienceContainer">
                                        <!-- Experience items will be added here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Education Tab -->
                            <div class="editor-content hidden" id="educationTab">
                                <div class="editor-section">
                                    <div class="section-header">
                                        <h3>
                                            <i class="fas fa-graduation-cap"></i>
                                            التعليم والمؤهلات
                                        </h3>
                                        <button class="add-item-btn" onclick="addEducation()">
                                            <i class="fas fa-plus"></i>
                                            إضافة مؤهل
                                        </button>
                                    </div>

                                    <div id="educationContainer">
                                        <!-- Education items will be added here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Skills Tab -->
                            <div class="editor-content hidden" id="skillsTab">
                                <div class="editor-section">
                                    <div class="section-header">
                                        <h3>
                                            <i class="fas fa-cogs"></i>
                                            المهارات والقدرات
                                        </h3>
                                    </div>

                                    <div class="skill-input-container">
                                        <input type="text" id="skillInput" class="form-control skill-input" placeholder="أدخل مهارة جديدة">
                                        <button class="add-skill-btn" onclick="addSkill()">
                                            <i class="fas fa-plus"></i>
                                            إضافة
                                        </button>
                                    </div>

                                    <div class="skills-container" id="skillsContainer">
                                        <!-- Skills will be added here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Live Preview Panel -->
                        <div class="editor-panel">
                            <h3 class="section-title">
                                <i class="fas fa-eye"></i>
                                المعاينة المباشرة
                            </h3>

                            <div id="livePreviewContainer" class="preview-container">
                                <div class="preview-placeholder">
                                    <i class="fas fa-file-alt"></i>
                                    <h3>المعاينة المباشرة</h3>
                                    <p>ستظهر هنا التغييرات فوراً</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="actions">
                    <button id="generateAI" class="btn btn-primary">
                        <i class="fas fa-robot"></i>
                        إنشاء بالذكاء الاصطناعي
                    </button>
                    <button id="editManual" class="btn btn-secondary">
                        <i class="fas fa-edit"></i>
                        تحرير يدوي
                    </button>
                </div>
            </aside>

            <!-- Preview Area -->
            <section class="preview-area">
                <h2 class="section-title">
                    <i class="fas fa-eye"></i>
                    المعاينة المباشرة
                </h2>

                <div id="previewContainer" class="preview-container">
                    <div class="preview-placeholder">
                        <i class="fas fa-file-alt"></i>
                        <h3>اختر قالباً لبدء المعاينة</h3>
                        <p>ستظهر هنا معاينة مباشرة لسيرتك الذاتية</p>
                    </div>
                </div>

                <!-- Export Actions -->
                <div class="actions">
                    <button id="exportPDF" class="btn btn-success">
                        <i class="fas fa-download"></i>
                        تصدير PDF
                    </button>
                    <button id="saveCV" class="btn btn-secondary">
                        <i class="fas fa-save"></i>
                        حفظ المشروع
                    </button>
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>جاري المعالجة...</h3>
            <p id="loadingText">يرجى الانتظار</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // ELASHRAFY CV Application
        class ElashrafyCV {
            constructor() {
                this.currentTemplate = null;
                this.cvData = {
                    personalInfo: {
                        name: '',
                        title: '',
                        email: '',
                        phone: '',
                        summary: ''
                    }
                };
                this.templates = this.generateTemplates();
                this.init();
            }

            init() {
                console.log('🚀 ELASHRAFY CV Application Starting...');
                this.loadTemplates();
                this.setupEventListeners();
                this.showNotification('مرحباً بك في ELASHRAFY CV - منصة السيرة الذاتية الاحترافية', 'success');
            }

            generateTemplates() {
                const categories = ['modern', 'creative', 'executive', 'academic', 'technical'];
                const styles = ['glass-morphism', 'neo-brutalism', 'gradient-mesh', 'organic-shapes', 'geometric-art'];
                const templates = [];

                for (let i = 0; i < 20; i++) {
                    const category = categories[i % categories.length];
                    const style = styles[i % styles.length];

                    templates.push({
                        id: `template_${i + 1}`,
                        name: `قالب ${this.getArabicNumber(i + 1)} - ${this.getCategoryName(category)}`,
                        category: category,
                        style: style,
                        description: `قالب احترافي ${this.getCategoryName(category)} بتصميم ${this.getStyleName(style)}`,
                        isPremium: i % 3 === 0,
                        rating: 4.5 + Math.random() * 0.5,
                        downloads: Math.floor(Math.random() * 10000) + 1000
                    });
                }

                return templates;
            }

            getArabicNumber(num) {
                const arabicNumbers = ['الأول', 'الثاني', 'الثالث', 'الرابع', 'الخامس', 'السادس', 'السابع', 'الثامن', 'التاسع', 'العاشر'];
                return arabicNumbers[num - 1] || `رقم ${num}`;
            }

            getCategoryName(category) {
                const names = {
                    'modern': 'عصري',
                    'creative': 'إبداعي',
                    'executive': 'تنفيذي',
                    'academic': 'أكاديمي',
                    'technical': 'تقني'
                };
                return names[category] || category;
            }

            getStyleName(style) {
                const names = {
                    'glass-morphism': 'زجاجي شفاف',
                    'neo-brutalism': 'جريء عصري',
                    'gradient-mesh': 'متدرج شبكي',
                    'organic-shapes': 'أشكال عضوية',
                    'geometric-art': 'هندسي فني'
                };
                return names[style] || style;
            }

            loadTemplates() {
                const grid = document.getElementById('templatesGrid');
                if (!grid) return;

                grid.innerHTML = '';

                this.templates.forEach(template => {
                    const card = this.createTemplateCard(template);
                    grid.appendChild(card);
                });
            }

            createTemplateCard(template) {
                const card = document.createElement('div');
                card.className = 'template-card';
                card.dataset.templateId = template.id;

                card.innerHTML = `
                    <div class="template-preview">
                        ${template.isPremium ? '<div class="premium-badge">مميز</div>' : ''}
                        <div class="template-preview-content">
                            <div class="template-header-preview">
                                <div class="template-name-preview">أحمد محمد</div>
                                <div class="template-title-preview">مطور ويب</div>
                            </div>
                            <div class="template-section-preview">
                                <div class="template-section-title">الخبرات</div>
                                <div class="template-content-line long"></div>
                                <div class="template-content-line medium"></div>
                            </div>
                            <div class="template-section-preview">
                                <div class="template-section-title">المهارات</div>
                                <div class="template-content-line short"></div>
                                <div class="template-content-line medium"></div>
                            </div>
                        </div>
                    </div>
                    <div class="template-info">
                        <div class="template-name">${template.name}</div>
                        <div class="template-category">${this.getCategoryName(template.category)}</div>
                    </div>
                `;

                card.addEventListener('click', () => {
                    this.selectTemplate(template);
                });

                return card;
            }

            selectTemplate(template) {
                // Remove previous selection
                document.querySelectorAll('.template-card').forEach(card => {
                    card.classList.remove('selected');
                });

                // Select current template
                const card = document.querySelector(`[data-template-id="${template.id}"]`);
                if (card) {
                    card.classList.add('selected');
                }

                this.currentTemplate = template;
                this.generatePreview();
                this.showNotification(`تم اختيار ${template.name}`, 'success');
            }

            generatePreview() {
                if (!this.currentTemplate) return;

                const container = document.getElementById('previewContainer');
                if (!container) return;

                // Show loading
                this.showLoading('جاري إنشاء المعاينة...');

                setTimeout(() => {
                    const cvData = this.getCVData();
                    const previewHTML = this.generateCVHTML(cvData);

                    container.innerHTML = `
                        <div class="cv-preview" id="cvPreview">
                            ${previewHTML}
                        </div>
                    `;

                    this.hideLoading();
                    this.showNotification('تم إنشاء المعاينة بنجاح', 'success');
                }, 1000);
            }

            getCVData() {
                return {
                    personalInfo: {
                        name: document.getElementById('fullName')?.value || 'اسمك الكامل',
                        title: document.getElementById('jobTitle')?.value || 'المسمى الوظيفي',
                        email: document.getElementById('email')?.value || 'البريد الإلكتروني',
                        phone: document.getElementById('phone')?.value || 'رقم الهاتف',
                        summary: document.getElementById('summary')?.value || 'أضف ملخصاً مهنياً يبرز خبراتك ومهاراتك الأساسية'
                    }
                };
            }

            generateCVHTML(data) {
                return `
                    <div class="cv-header">
                        <div class="cv-name">${data.personalInfo.name}</div>
                        <div class="cv-title">${data.personalInfo.title}</div>
                        <div class="cv-contact">
                            <span><i class="fas fa-envelope"></i> ${data.personalInfo.email}</span>
                            <span><i class="fas fa-phone"></i> ${data.personalInfo.phone}</span>
                        </div>
                    </div>

                    <div class="cv-section">
                        <div class="cv-section-title">الملخص المهني</div>
                        <div class="cv-content">${data.personalInfo.summary}</div>
                    </div>

                    <div class="cv-section">
                        <div class="cv-section-title">الخبرات المهنية</div>
                        <div class="cv-content">
                            <div style="margin-bottom: 15px;">
                                <strong>مطور ويب أول</strong> - شركة التقنية المتقدمة<br>
                                <small style="color: #6b7280;">2020 - حتى الآن</small><br>
                                تطوير وصيانة تطبيقات الويب باستخدام أحدث التقنيات والأدوات.
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>مطور ويب</strong> - شركة الحلول الرقمية<br>
                                <small style="color: #6b7280;">2018 - 2020</small><br>
                                العمل على مشاريع متنوعة وتطوير واجهات المستخدم التفاعلية.
                            </div>
                        </div>
                    </div>

                    <div class="cv-section">
                        <div class="cv-section-title">المهارات</div>
                        <div class="cv-content">
                            JavaScript, React, Node.js, Python, HTML/CSS, Git, Docker, AWS
                        </div>
                    </div>
                `;
            }

            setupEventListeners() {
                // Generate AI button
                const generateAI = document.getElementById('generateAI');
                if (generateAI) {
                    generateAI.addEventListener('click', () => {
                        this.generateWithAI();
                    });
                }

                // Edit Manual button
                const editManual = document.getElementById('editManual');
                if (editManual) {
                    editManual.addEventListener('click', () => {
                        this.showEditForm();
                    });
                }

                // Export PDF button
                const exportPDF = document.getElementById('exportPDF');
                if (exportPDF) {
                    exportPDF.addEventListener('click', () => {
                        this.exportToPDF();
                    });
                }

                // Form inputs live update
                const formInputs = document.querySelectorAll('#cvForm input, #cvForm textarea');
                formInputs.forEach(input => {
                    input.addEventListener('input', () => {
                        if (this.currentTemplate) {
                            this.generatePreview();
                        }
                    });
                });
            }

            generateWithAI() {
                this.showLoading('جاري إنشاء السيرة الذاتية بالذكاء الاصطناعي...');

                // Simulate AI generation
                setTimeout(() => {
                    // Fill form with AI-generated data
                    document.getElementById('fullName').value = 'أحمد محمد الأشرافي';
                    document.getElementById('jobTitle').value = 'مطور ويب متقدم';
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('phone').value = '+966 50 123 4567';
                    document.getElementById('summary').value = 'مطور ويب متخصص مع خبرة 5+ سنوات في تطوير التطبيقات الحديثة باستخدام JavaScript, React, و Node.js. شغوف بإنشاء حلول تقنية مبتكرة وتحسين تجربة المستخدم.';

                    // Add AI-generated skills
                    this.addSkillToContainer('JavaScript');
                    this.addSkillToContainer('React');
                    this.addSkillToContainer('Node.js');
                    this.addSkillToContainer('Python');
                    this.addSkillToContainer('MongoDB');

                    // Add AI-generated experience
                    this.addExperienceItem({
                        title: 'مطور ويب أول',
                        company: 'شركة التقنية المتقدمة',
                        location: 'الرياض، السعودية',
                        startDate: '2020-01',
                        endDate: '',
                        current: true,
                        description: 'تطوير وصيانة تطبيقات الويب باستخدام أحدث التقنيات والأدوات. قيادة فريق من 5 مطورين وتحسين الأداء بنسبة 40%.'
                    });

                    // Select a random template
                    const randomTemplate = this.templates[Math.floor(Math.random() * this.templates.length)];
                    this.selectTemplate(randomTemplate);

                    this.hideLoading();
                    this.showAdvancedEditor();
                    this.showNotification('تم إنشاء السيرة الذاتية بالذكاء الاصطناعي بنجاح!', 'success');
                }, 2000);
            }

            showAdvancedEditor() {
                const advancedEditor = document.getElementById('advancedEditor');
                const templatesGrid = document.getElementById('templatesGrid');
                const aiSection = document.getElementById('aiSection');

                if (advancedEditor && templatesGrid && aiSection) {
                    advancedEditor.classList.remove('hidden');
                    templatesGrid.style.display = 'none';
                    aiSection.style.display = 'none';

                    // Setup editor tabs
                    this.setupEditorTabs();

                    // Setup live preview
                    this.setupLivePreview();
                }
            }

            setupEditorTabs() {
                const tabs = document.querySelectorAll('.editor-tab');
                const contents = document.querySelectorAll('.editor-content');

                tabs.forEach(tab => {
                    tab.addEventListener('click', () => {
                        // Remove active class from all tabs and contents
                        tabs.forEach(t => t.classList.remove('active'));
                        contents.forEach(c => c.classList.add('hidden'));

                        // Add active class to clicked tab and corresponding content
                        tab.classList.add('active');
                        const targetTab = tab.dataset.tab;
                        const targetContent = document.getElementById(targetTab + 'Tab');
                        if (targetContent) {
                            targetContent.classList.remove('hidden');
                        }
                    });
                });
            }

            setupLivePreview() {
                // Setup live preview updates
                const inputs = document.querySelectorAll('#advancedEditor input, #advancedEditor textarea');
                inputs.forEach(input => {
                    input.addEventListener('input', () => {
                        this.updateLivePreview();
                    });
                });

                // Initial preview update
                this.updateLivePreview();
            }

            updateLivePreview() {
                const previewContainer = document.getElementById('livePreviewContainer');
                if (!previewContainer) return;

                const cvData = this.getCVData();
                const previewHTML = this.generateCVHTML(cvData);

                previewContainer.innerHTML = `
                    <div class="cv-preview" id="cvPreview">
                        ${previewHTML}
                    </div>
                `;
            }

            addSkillToContainer(skillName) {
                const container = document.getElementById('skillsContainer');
                if (!container) return;

                const skillTag = document.createElement('div');
                skillTag.className = 'skill-tag';
                skillTag.innerHTML = `
                    ${skillName}
                    <span class="remove-skill" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </span>
                `;

                container.appendChild(skillTag);
            }

            addExperienceItem(data = {}) {
                const container = document.getElementById('experienceContainer');
                if (!container) return;

                const experienceItem = document.createElement('div');
                experienceItem.className = 'item-container';
                experienceItem.innerHTML = `
                    <button class="remove-item-btn" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>

                    <div class="form-row">
                        <div class="form-group">
                            <label>المسمى الوظيفي</label>
                            <input type="text" class="form-control" value="${data.title || ''}" placeholder="مثل: مطور ويب">
                        </div>
                        <div class="form-group">
                            <label>اسم الشركة</label>
                            <input type="text" class="form-control" value="${data.company || ''}" placeholder="اسم الشركة">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>الموقع</label>
                            <input type="text" class="form-control" value="${data.location || ''}" placeholder="المدينة، البلد">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" ${data.current ? 'checked' : ''}> أعمل حالياً
                            </label>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>تاريخ البداية</label>
                            <input type="month" class="form-control" value="${data.startDate || ''}">
                        </div>
                        <div class="form-group">
                            <label>تاريخ النهاية</label>
                            <input type="month" class="form-control" value="${data.endDate || ''}">
                        </div>
                    </div>

                    <div class="form-row full">
                        <div class="form-group">
                            <label>وصف المهام</label>
                            <textarea class="form-control" rows="3" placeholder="اكتب وصفاً للمهام والإنجازات">${data.description || ''}</textarea>
                        </div>
                    </div>
                `;

                container.appendChild(experienceItem);

                // Setup live preview for new inputs
                const inputs = experienceItem.querySelectorAll('input, textarea');
                inputs.forEach(input => {
                    input.addEventListener('input', () => {
                        this.updateLivePreview();
                    });
                });
            }

            addEducationItem(data = {}) {
                const container = document.getElementById('educationContainer');
                if (!container) return;

                const educationItem = document.createElement('div');
                educationItem.className = 'item-container';
                educationItem.innerHTML = `
                    <button class="remove-item-btn" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>

                    <div class="form-row">
                        <div class="form-group">
                            <label>الدرجة العلمية</label>
                            <input type="text" class="form-control" value="${data.degree || ''}" placeholder="مثل: بكالوريوس علوم الحاسوب">
                        </div>
                        <div class="form-group">
                            <label>اسم المؤسسة</label>
                            <input type="text" class="form-control" value="${data.institution || ''}" placeholder="اسم الجامعة أو المعهد">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>سنة التخرج</label>
                            <input type="number" class="form-control" value="${data.year || ''}" placeholder="2023" min="1950" max="2030">
                        </div>
                        <div class="form-group">
                            <label>المعدل (اختياري)</label>
                            <input type="text" class="form-control" value="${data.gpa || ''}" placeholder="3.8/4.0">
                        </div>
                    </div>
                `;

                container.appendChild(educationItem);

                // Setup live preview for new inputs
                const inputs = educationItem.querySelectorAll('input');
                inputs.forEach(input => {
                    input.addEventListener('input', () => {
                        this.updateLivePreview();
                    });
                });
            }

            async exportToPDF() {
                const cvPreview = document.getElementById('cvPreview');
                if (!cvPreview) {
                    this.showNotification('يرجى إنشاء معاينة أولاً', 'error');
                    return;
                }

                this.showLoading('جاري تصدير PDF...');

                try {
                    // Create canvas from CV preview
                    const canvas = await html2canvas(cvPreview, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: false,
                        backgroundColor: '#ffffff'
                    });

                    // Create PDF
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'mm',
                        format: 'a4'
                    });

                    const imgData = canvas.toDataURL('image/png');
                    const imgWidth = 210;
                    const imgHeight = (canvas.height * imgWidth) / canvas.width;

                    pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

                    // Download PDF
                    const fileName = `ELASHRAFY_CV_${new Date().getTime()}.pdf`;
                    pdf.save(fileName);

                    this.hideLoading();
                    this.showNotification('تم تصدير PDF بنجاح!', 'success');

                } catch (error) {
                    console.error('PDF Export Error:', error);
                    this.hideLoading();
                    this.showNotification('حدث خطأ أثناء التصدير', 'error');
                }
            }

            showLoading(text = 'جاري المعالجة...') {
                const overlay = document.getElementById('loadingOverlay');
                const loadingText = document.getElementById('loadingText');

                if (overlay && loadingText) {
                    loadingText.textContent = text;
                    overlay.classList.remove('hidden');
                }
            }

            hideLoading() {
                const overlay = document.getElementById('loadingOverlay');
                if (overlay) {
                    overlay.classList.add('hidden');
                }
            }

            showNotification(message, type = 'info') {
                // Create notification element
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 10001;
                    max-width: 300px;
                    font-weight: 600;
                    animation: slideIn 0.3s ease;
                `;

                notification.textContent = message;
                document.body.appendChild(notification);

                // Remove after 3 seconds
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }
        }

        // Global functions for AI and Editor
        function generateAICV(type) {
            if (window.elashrafyCV) {
                switch(type) {
                    case 'complete':
                        window.elashrafyCV.generateWithAI();
                        break;
                    case 'improve':
                        window.elashrafyCV.showNotification('جاري تحسين المحتوى...', 'info');
                        // Add improvement logic here
                        break;
                    case 'suggestions':
                        window.elashrafyCV.showNotification('جاري إنشاء الاقتراحات...', 'info');
                        // Add suggestions logic here
                        break;
                }
            }
        }

        function addExperience() {
            if (window.elashrafyCV) {
                window.elashrafyCV.addExperienceItem();
            }
        }

        function addEducation() {
            if (window.elashrafyCV) {
                window.elashrafyCV.addEducationItem();
            }
        }

        function addSkill() {
            const skillInput = document.getElementById('skillInput');
            if (skillInput && skillInput.value.trim() && window.elashrafyCV) {
                window.elashrafyCV.addSkillToContainer(skillInput.value.trim());
                skillInput.value = '';
            }
        }

        // Initialize application when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.elashrafyCV = new ElashrafyCV();
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
