/* CSS Variables for Theme Management */
:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family-en: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-family-ar: '<PERSON>o <PERSON>s Arabic', '<PERSON><PERSON><PERSON>', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* RTL Support */
[dir="rtl"] {
    --font-family-primary: var(--font-family-ar);
}

[dir="ltr"] {
    --font-family-primary: var(--font-family-en);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary, var(--font-family-en));
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Header Styles */
.app-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-text {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.logo-main {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--primary-color);
    letter-spacing: -0.5px;
    background: linear-gradient(135deg, var(--primary-color), #8B5CF6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-subtitle {
    font-size: var(--font-size-xs);
    font-weight: 500;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

/* Language Toggle */
.language-toggle {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: var(--spacing-1);
}

.lang-btn {
    padding: var(--spacing-2) var(--spacing-4);
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--gray-600);
}

.lang-btn.active {
    background: var(--white);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.lang-btn:hover:not(.active) {
    color: var(--gray-800);
}

/* Theme Selector */
.theme-selector select {
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: border-color var(--transition-fast);
}

.theme-selector select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* Template Gallery Button */
.template-gallery-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-5);
    background: linear-gradient(135deg, #8B5CF6, #3B82F6);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.template-gallery-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.template-gallery-btn:hover::before {
    left: 100%;
}

.template-gallery-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.template-gallery-btn:active {
    transform: translateY(0);
}

/* Demo Button */
.demo-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-5);
    background: var(--accent-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.demo-btn:hover {
    background: #d97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.demo-btn:active {
    transform: translateY(0);
}

/* Export Button */
.export-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.export-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.export-btn:active {
    transform: translateY(0);
}

/* Main Layout */
.app-main {
    padding: var(--spacing-6) 0;
}

.app-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-8);
    min-height: calc(100vh - 120px);
}

/* Form Panel */
.form-panel {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.form-container {
    padding: var(--spacing-6);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.form-container h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

/* Progress Indicator */
.progress-indicator {
    margin-bottom: var(--spacing-8);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    transition: width var(--transition-normal);
    width: 0%;
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
}

/* Form Sections */
.cv-form {
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 300px);
}

.form-section {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.form-section.active {
    display: block;
}

.form-section h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--gray-200);
}

.form-section h3 i {
    color: var(--primary-color);
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    background: var(--white);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Dynamic Containers */
.dynamic-container {
    margin-bottom: var(--spacing-6);
}

.dynamic-item {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
    position: relative;
}

.dynamic-item .remove-btn {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    background: var(--error-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: all var(--transition-fast);
}

.dynamic-item .remove-btn:hover {
    background: #dc2626;
    transform: scale(1.1);
}

/* Add Buttons */
.add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    width: 100%;
    padding: var(--spacing-4);
    background: transparent;
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.add-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgb(37 99 235 / 0.05);
}

/* Skills Container */
.skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
    min-height: 60px;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    background: var(--gray-50);
}

.skill-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.skill-tag .skill-level {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
}

.skill-tag .remove-skill {
    background: none;
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background var(--transition-fast);
}

.skill-tag .remove-skill:hover {
    background: rgba(255, 255, 255, 0.2);
}

.skill-input-group {
    display: flex;
    gap: var(--spacing-2);
}

.skill-input-group input {
    flex: 1;
}

.skill-input-group select {
    min-width: 120px;
}

.skill-input-group button {
    padding: var(--spacing-3);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background var(--transition-fast);
}

.skill-input-group button:hover {
    background: var(--primary-dark);
}

/* Photo Upload Section */
.photo-upload-section {
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 2px dashed var(--gray-300);
}

.photo-upload-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.photo-upload-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.photo-preview {
    width: 150px;
    height: 150px;
    border-radius: var(--radius-lg);
    border: 2px dashed var(--gray-300);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    overflow: hidden;
    position: relative;
    margin: 0 auto;
    transition: all var(--transition-normal);
}

.photo-preview:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.photo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--gray-500);
    text-align: center;
}

.photo-placeholder i {
    font-size: var(--font-size-2xl);
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-preview.circle {
    border-radius: 50%;
}

.photo-preview.circle img {
    border-radius: 50%;
}

.photo-preview.rounded img {
    border-radius: var(--radius-lg);
}

.photo-controls {
    display: flex;
    gap: var(--spacing-2);
    justify-content: center;
}

.photo-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.photo-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.photo-btn.secondary {
    background: var(--error-color);
    color: var(--white);
    border-color: var(--error-color);
}

.photo-btn.secondary:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.photo-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--white);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.photo-shape-selector,
.photo-position-selector {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.photo-shape-selector label,
.photo-position-selector label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.photo-shape-selector select,
.photo-position-selector select {
    padding: var(--spacing-2);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
}

/* Form Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-4);
    margin-top: var(--spacing-6);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
    color: var(--gray-700);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.next-btn {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.next-btn:hover:not(:disabled) {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Loading States */
.loading {
    animation: pulse 2s infinite;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

[dir="rtl"] .text-left {
    text-align: right;
}

[dir="rtl"] .text-right {
    text-align: left;
}

/* Preview Panel */
.preview-panel {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.preview-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.zoom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.zoom-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.zoom-level {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    min-width: 50px;
    text-align: center;
}

.preview-wrapper {
    flex: 1;
    overflow: auto;
    padding: var(--spacing-6);
    background: var(--gray-100);
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.cv-preview {
    background: var(--white);
    box-shadow: var(--shadow-xl);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transform-origin: top center;
    transition: transform var(--transition-normal);
    width: 210mm;
    min-height: 297mm;
    max-width: 100%;
}

/* CV Preview Content Styles */
.cv-content {
    padding: var(--spacing-8);
    font-family: var(--font-family-primary);
}

.cv-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-6);
    border-bottom: 2px solid var(--primary-color);
}

.cv-name {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.cv-title {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-4);
}

.cv-contact {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.cv-contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.cv-contact-item i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.cv-section {
    margin-bottom: var(--spacing-8);
}

.cv-section-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--gray-300);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.cv-section-title i {
    color: var(--primary-color);
}

.cv-summary {
    font-size: var(--font-size-base);
    line-height: 1.7;
    color: var(--gray-700);
    text-align: justify;
}

.cv-experience-item,
.cv-education-item {
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
}

.cv-experience-item:last-child,
.cv-education-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.cv-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-2);
}

.cv-item-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
}

.cv-item-company,
.cv-item-institution {
    font-size: var(--font-size-base);
    color: var(--primary-color);
    font-weight: 500;
}

.cv-item-date {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
    white-space: nowrap;
}

.cv-item-description {
    font-size: var(--font-size-sm);
    line-height: 1.6;
    color: var(--gray-700);
    margin-top: var(--spacing-2);
}

.cv-skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.cv-skill-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
}

.cv-skill-name {
    font-weight: 500;
    color: var(--gray-800);
}

.cv-skill-level {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    background: var(--gray-200);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    font-weight: 500;
}

.cv-languages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-3);
}

.cv-language-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2);
    background: var(--gray-50);
    border-radius: var(--radius-md);
}

.cv-language-name {
    font-weight: 500;
    color: var(--gray-800);
}

.cv-language-level {
    font-size: var(--font-size-xs);
    color: var(--primary-color);
    font-weight: 600;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: var(--white);
}

.loading-spinner i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
}

.loading-spinner p {
    font-size: var(--font-size-lg);
    font-weight: 500;
}

/* Modal Styles */
.template-gallery-modal,
.photo-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.template-gallery-modal.active,
.photo-editor-modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.template-gallery-modal .modal-content {
    width: 1200px;
    height: 800px;
}

.photo-editor-modal .modal-content {
    width: 900px;
    height: 600px;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.modal-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
}

.modal-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--gray-200);
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--error-color);
    color: var(--white);
}

.modal-body {
    flex: 1;
    overflow: auto;
    padding: var(--spacing-6);
}

/* Template Gallery Styles */
.template-categories {
    display: flex;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-6);
    flex-wrap: wrap;
}

.category-btn {
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
    color: var(--gray-700);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.category-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.category-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-4);
}

.template-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.template-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.template-preview-img {
    width: 100%;
    height: 200px;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.template-preview-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-info {
    padding: var(--spacing-4);
}

.template-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.template-category {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.template-description {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    line-height: 1.5;
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-select-btn {
    padding: var(--spacing-3) var(--spacing-6);
    background: var(--white);
    color: var(--primary-color);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.template-select-btn:hover {
    background: var(--gray-100);
}

/* Photo Editor Styles */
.photo-editor-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--spacing-6);
    height: 100%;
}

.photo-editor-canvas {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.photo-editor-canvas canvas {
    max-width: 100%;
    max-height: 100%;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
}

.photo-editor-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.control-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--gray-300);
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

.editor-buttons {
    display: flex;
    gap: var(--spacing-2);
    margin-top: var(--spacing-4);
}

.btn {
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    flex: 1;
}

.btn.primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn.primary:hover {
    background: var(--primary-dark);
}

.btn.secondary {
    background: var(--gray-300);
    color: var(--gray-700);
}

.btn.secondary:hover {
    background: var(--gray-400);
}

/* Drag and Drop Styles */
.photo-preview.drag-over {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: scale(1.02);
}

.photo-preview.drag-over .photo-placeholder {
    color: var(--primary-color);
}

/* ATS Optimization Styles */
.cv-preview.ats-optimized {
    font-family: 'Arial', sans-serif;
    color: #000000;
    background: #ffffff;
}

.cv-preview.ats-optimized * {
    color: inherit;
    background: transparent;
}

.cv-preview.ats-optimized .cv-section-title {
    font-weight: bold;
    text-transform: uppercase;
    border-bottom: 1px solid #000000;
    margin-bottom: 10px;
}

/* Print Optimization */
@media print {
    .photo-upload-section,
    .template-gallery-modal,
    .photo-editor-modal,
    .loading-overlay {
        display: none !important;
    }

    .cv-preview {
        width: 100% !important;
        height: auto !important;
        transform: none !important;
        box-shadow: none !important;
        border: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .cv-photo {
        width: 80px !important;
        height: 80px !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* Responsive Modal Styles */
@media (max-width: 768px) {
    .template-gallery-modal .modal-content,
    .photo-editor-modal .modal-content {
        width: 95vw;
        height: 95vh;
        margin: 2.5vh auto;
    }

    .template-grid {
        grid-template-columns: 1fr;
    }

    .photo-editor-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .template-categories {
        flex-wrap: wrap;
        gap: var(--spacing-1);
    }

    .category-btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-1) var(--spacing-2);
    }
}

/* Loading States for Templates */
.template-grid.loading {
    opacity: 0.6;
    pointer-events: none;
}

.template-card.loading {
    animation: pulse 2s infinite;
}

/* Success States */
.template-card.selected {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.template-card.selected .template-overlay {
    opacity: 1;
    background: rgba(16, 185, 129, 0.9);
}

/* Error States */
.photo-preview.error {
    border-color: var(--error-color);
    background: rgba(239, 68, 68, 0.05);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
    border-color: var(--error-color);
    background: rgba(239, 68, 68, 0.05);
}

.error-message {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.error-message::before {
    content: '⚠';
    font-size: var(--font-size-xs);
}

/* Footer Styles */
.app-footer {
    background: linear-gradient(135deg, #1e293b, #0f172a);
    color: white;
    margin-top: var(--spacing-16);
    position: relative;
    overflow: hidden;
}

.app-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-12) var(--spacing-6);
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--spacing-8);
    align-items: center;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.footer-logo svg {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.footer-text h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-1);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-text p {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

.footer-developer {
    display: flex;
    justify-content: center;
}

.developer-credit {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.developer-credit:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(139, 92, 246, 0.3);
}

.developer-avatar {
    flex-shrink: 0;
}

.developer-avatar svg {
    filter: drop-shadow(0 4px 12px rgba(139, 92, 246, 0.4));
}

.developer-info h4 {
    font-size: var(--font-size-xs);
    color: var(--gray-400);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: var(--spacing-1);
}

.developer-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-1);
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.developer-info p {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-3);
}

.developer-links {
    display: flex;
    gap: var(--spacing-2);
}

.developer-link {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-fast);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.developer-link:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.footer-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.footer-stats .stat-item {
    text-align: center;
    padding: var(--spacing-3);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-stats .stat-number {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-1);
}

.footer-stats .stat-label {
    font-size: var(--font-size-xs);
    color: var(--gray-400);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-copyright p {
    margin: 0;
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

.developer-signature {
    margin-top: var(--spacing-2) !important;
    font-size: var(--font-size-xs) !important;
}

.developer-signature strong {
    color: var(--primary-color);
    font-weight: 600;
}

.footer-links {
    display: flex;
    gap: var(--spacing-6);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Footer Responsive Design */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
        text-align: center;
    }

    .developer-credit {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
    }

    .footer-stats {
        flex-direction: row;
        justify-content: center;
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-4);
        text-align: center;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-4);
    }
}
