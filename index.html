<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ELASHRAFY CV - Professional Resume Builder | الأشرافي للسيرة الذاتية المهنية</title>

    <!-- Meta Tags -->
    <meta name="description" content="ELASHRAFY CV - Create world-class professional CVs with advanced templates, photo integration, and bilingual support. Premium resume builder with 400+ templates.">
    <meta name="keywords" content="ELASHRAFY CV, professional CV builder, resume maker, Arabic CV, English CV, PDF export, professional resume, CV templates, photo CV">
    <meta name="author" content="ELASHRAFY CV Team">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://elashrafy-cv.com/">
    <meta property="og:title" content="ELASHRAFY CV - Professional Resume Builder">
    <meta property="og:description" content="Create world-class professional CVs with 400+ premium templates, photo integration, and bilingual support. The ultimate resume builder.">
    <meta property="og:image" content="https://elashrafy-cv.com/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://elashrafy-cv.com/">
    <meta property="twitter:title" content="ELASHRAFY CV - Professional Resume Builder">
    <meta property="twitter:description" content="Create world-class professional CVs with 400+ premium templates, photo integration, and bilingual support. The ultimate resume builder.">
    <meta property="twitter:image" content="https://elashrafy-cv.com/twitter-image.png">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Theme Color -->
    <meta name="theme-color" content="#2563eb">
    <meta name="msapplication-TileColor" content="#2563eb">

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDE4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxODAiIGhlaWdodD0iMTgwIiByeD0iMjAiIGZpbGw9IiMyNTYzZWIiLz4KPHN2ZyB4PSI0NSIgeT0iNDUiIHdpZHRoPSI5MCIgaGVpZ2h0PSI5MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Ik0xNCAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWOHoiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPC9zdmc+Cjwvc3ZnPgo=">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iNCIgZmlsbD0iIzI1NjNlYiIvPgo8c3ZnIHg9IjgiIHk9IjgiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+CjxwYXRoIGQ9Ik0xNCAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWOHoiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPHN0cm9rZSBkPSJNMTQsOGw2LTYiLz4KPC9zdmc+Cjwvc3ZnPgo=">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/themes.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/professional-features.css">
    <link rel="stylesheet" href="css/templates-gallery.css">
    <link rel="stylesheet" href="css/advanced-features.css">
    <link rel="stylesheet" href="css/auth-system.css">
    <link rel="stylesheet" href="css/auto-template-system.css">
</head>
<body>
    <!-- Header -->
    <header class="app-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="40" height="40" rx="8" fill="url(#gradient1)"/>
                            <path d="M12 10h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H12a2 2 0 0 1-2-2V12a2 2 0 0 1 2-2z" fill="white" opacity="0.9"/>
                            <path d="M16 16h8M16 20h8M16 24h6" stroke="url(#gradient2)" stroke-width="2" stroke-linecap="round"/>
                            <circle cx="20" cy="14" r="2" fill="url(#gradient2)"/>
                            <defs>
                                <linearGradient id="gradient1" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#3B82F6"/>
                                    <stop offset="1" stop-color="#1D4ED8"/>
                                </linearGradient>
                                <linearGradient id="gradient2" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#3B82F6"/>
                                    <stop offset="1" stop-color="#8B5CF6"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <span class="logo-main" data-translate="app_title">ELASHRAFY CV</span>
                        <span class="logo-subtitle" data-translate="app_subtitle">Professional Resume Builder</span>
                    </div>
                </div>
                
                <div class="header-controls">
                    <!-- Language Toggle -->
                    <div class="language-toggle">
                        <button class="lang-btn active" data-lang="en">EN</button>
                        <button class="lang-btn" data-lang="ar">عر</button>
                    </div>
                    
                    <!-- Template Gallery Button -->
                    <button class="template-gallery-btn" id="openTemplateGallery">
                        <i class="fas fa-th-large"></i>
                        <span data-translate="template_gallery">Template Gallery</span>
                    </button>

                    <!-- Theme Selector -->
                    <div class="theme-selector">
                        <select id="themeSelect">
                            <option value="modern" data-translate="theme_modern">Modern</option>
                            <option value="classic" data-translate="theme_classic">Classic</option>
                            <option value="creative" data-translate="theme_creative">Creative</option>
                            <option value="minimal" data-translate="theme_minimal">Minimal</option>
                            <option value="executive" data-translate="theme_executive">Executive</option>
                            <option value="academic" data-translate="theme_academic">Academic</option>
                            <option value="technical" data-translate="theme_technical">Technical</option>
                            <option value="medical" data-translate="theme_medical">Medical</option>
                        </select>
                    </div>
                    
                    <!-- Demo Button -->
                    <button class="demo-btn" id="loadDemo">
                        <i class="fas fa-magic"></i>
                        <span data-translate="load_demo">Load Demo</span>
                    </button>

                    <!-- Export Button -->
                    <button class="export-btn" id="exportPDF">
                        <i class="fas fa-download"></i>
                        <span data-translate="export_pdf">Export PDF</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Application -->
    <main class="app-main">
        <div class="container">
            <div class="app-layout">
                <!-- CV Form Panel -->
                <aside class="form-panel">
                    <div class="form-container">
                        <h2 data-translate="build_cv">Build Your CV</h2>
                        
                        <!-- Progress Indicator -->
                        <div class="progress-indicator">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>
                            <span class="progress-text" id="progressText">0% Complete</span>
                        </div>

                        <!-- Form Sections -->
                        <form id="cvForm" class="cv-form">
                            <!-- Personal Information -->
                            <section class="form-section active" data-section="personal">
                                <h3>
                                    <i class="fas fa-user"></i>
                                    <span data-translate="personal_info">Personal Information</span>
                                </h3>

                                <!-- Photo Upload Section -->
                                <div class="photo-upload-section">
                                    <h4 data-translate="profile_photo">Profile Photo</h4>
                                    <div class="photo-upload-container">
                                        <div class="photo-preview" id="photoPreview">
                                            <div class="photo-placeholder">
                                                <i class="fas fa-camera"></i>
                                                <span data-translate="add_photo">Add Photo</span>
                                            </div>
                                        </div>
                                        <div class="photo-controls">
                                            <input type="file" id="photoInput" accept="image/*" style="display: none;">
                                            <button type="button" class="photo-btn" id="uploadPhotoBtn">
                                                <i class="fas fa-upload"></i>
                                                <span data-translate="upload_photo">Upload Photo</span>
                                            </button>
                                            <button type="button" class="photo-btn secondary" id="removePhotoBtn" style="display: none;">
                                                <i class="fas fa-trash"></i>
                                                <span data-translate="remove_photo">Remove</span>
                                            </button>
                                        </div>
                                        <div class="photo-options" id="photoOptions" style="display: none;">
                                            <div class="photo-shape-selector">
                                                <label data-translate="photo_shape">Photo Shape:</label>
                                                <select id="photoShape">
                                                    <option value="circle" data-translate="circle">Circle</option>
                                                    <option value="square" data-translate="square">Square</option>
                                                    <option value="rounded" data-translate="rounded">Rounded</option>
                                                </select>
                                            </div>
                                            <div class="photo-position-selector">
                                                <label data-translate="photo_position">Position:</label>
                                                <select id="photoPosition">
                                                    <option value="left" data-translate="left">Left</option>
                                                    <option value="center" data-translate="center">Center</option>
                                                    <option value="right" data-translate="right">Right</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="fullName" data-translate="full_name">Full Name</label>
                                        <input type="text" id="fullName" name="fullName" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="jobTitle" data-translate="job_title">Job Title</label>
                                        <input type="text" id="jobTitle" name="jobTitle" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="email" data-translate="email">Email</label>
                                        <input type="email" id="email" name="email" required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="phone" data-translate="phone">Phone</label>
                                        <input type="tel" id="phone" name="phone" required>
                                    </div>
                                    
                                    <div class="form-group full-width">
                                        <label for="address" data-translate="address">Address</label>
                                        <input type="text" id="address" name="address">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="linkedin" data-translate="linkedin">LinkedIn</label>
                                        <input type="url" id="linkedin" name="linkedin">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="website" data-translate="website">Website</label>
                                        <input type="url" id="website" name="website">
                                    </div>
                                    
                                    <div class="form-group full-width">
                                        <label for="summary" data-translate="professional_summary">Professional Summary</label>
                                        <textarea id="summary" name="summary" rows="4" placeholder="Brief description of your professional background..."></textarea>
                                    </div>
                                </div>
                            </section>

                            <!-- Experience Section -->
                            <section class="form-section" data-section="experience">
                                <h3>
                                    <i class="fas fa-briefcase"></i>
                                    <span data-translate="work_experience">Work Experience</span>
                                </h3>
                                
                                <div id="experienceContainer" class="dynamic-container">
                                    <!-- Experience items will be added dynamically -->
                                </div>
                                
                                <button type="button" class="add-btn" id="addExperience">
                                    <i class="fas fa-plus"></i>
                                    <span data-translate="add_experience">Add Experience</span>
                                </button>
                            </section>

                            <!-- Education Section -->
                            <section class="form-section" data-section="education">
                                <h3>
                                    <i class="fas fa-graduation-cap"></i>
                                    <span data-translate="education">Education</span>
                                </h3>
                                
                                <div id="educationContainer" class="dynamic-container">
                                    <!-- Education items will be added dynamically -->
                                </div>
                                
                                <button type="button" class="add-btn" id="addEducation">
                                    <i class="fas fa-plus"></i>
                                    <span data-translate="add_education">Add Education</span>
                                </button>
                            </section>

                            <!-- Skills Section -->
                            <section class="form-section" data-section="skills">
                                <h3>
                                    <i class="fas fa-cogs"></i>
                                    <span data-translate="skills">Skills</span>
                                </h3>
                                
                                <div id="skillsContainer" class="skills-container">
                                    <!-- Skills will be added dynamically -->
                                </div>
                                
                                <div class="skill-input-group">
                                    <input type="text" id="skillInput" placeholder="Enter a skill...">
                                    <select id="skillLevel">
                                        <option value="beginner" data-translate="beginner">Beginner</option>
                                        <option value="intermediate" data-translate="intermediate">Intermediate</option>
                                        <option value="advanced" data-translate="advanced">Advanced</option>
                                        <option value="expert" data-translate="expert">Expert</option>
                                    </select>
                                    <button type="button" id="addSkill">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </section>

                            <!-- Languages Section -->
                            <section class="form-section" data-section="languages">
                                <h3>
                                    <i class="fas fa-language"></i>
                                    <span data-translate="languages">Languages</span>
                                </h3>
                                
                                <div id="languagesContainer" class="dynamic-container">
                                    <!-- Languages will be added dynamically -->
                                </div>
                                
                                <button type="button" class="add-btn" id="addLanguage">
                                    <i class="fas fa-plus"></i>
                                    <span data-translate="add_language">Add Language</span>
                                </button>
                            </section>
                        </form>

                        <!-- Navigation Buttons -->
                        <div class="form-navigation">
                            <button type="button" class="nav-btn prev-btn" id="prevSection" disabled>
                                <i class="fas fa-chevron-left"></i>
                                <span data-translate="previous">Previous</span>
                            </button>
                            <button type="button" class="nav-btn next-btn" id="nextSection">
                                <span data-translate="next">Next</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </aside>

                <!-- CV Preview Panel -->
                <main class="preview-panel">
                    <div class="preview-container">
                        <div class="preview-header">
                            <h2 data-translate="preview">Preview</h2>
                            <div class="preview-controls">
                                <button class="zoom-btn" id="zoomOut">
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <span class="zoom-level" id="zoomLevel">100%</span>
                                <button class="zoom-btn" id="zoomIn">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="preview-wrapper" id="previewWrapper">
                            <div class="cv-preview" id="cvPreview">
                                <!-- CV content will be generated here -->
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </main>

    <!-- Template Gallery Modal -->
    <div class="template-gallery-modal" id="templateGalleryModal">
        <div class="modal-overlay" id="modalOverlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 data-translate="template_gallery">Template Gallery</h2>
                <button class="modal-close" id="closeTemplateGallery">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="template-categories">
                    <button class="category-btn active" data-category="all" data-translate="all_templates">All Templates</button>
                    <button class="category-btn" data-category="modern" data-translate="modern_templates">Modern</button>
                    <button class="category-btn" data-category="classic" data-translate="classic_templates">Classic</button>
                    <button class="category-btn" data-category="creative" data-translate="creative_templates">Creative</button>
                    <button class="category-btn" data-category="executive" data-translate="executive_templates">Executive</button>
                    <button class="category-btn" data-category="academic" data-translate="academic_templates">Academic</button>
                    <button class="category-btn" data-category="technical" data-translate="technical_templates">Technical</button>
                    <button class="category-btn" data-category="medical" data-translate="medical_templates">Medical</button>
                </div>
                <div class="template-grid" id="templateGrid">
                    <!-- Templates will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Photo Editor Modal -->
    <div class="photo-editor-modal" id="photoEditorModal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 data-translate="edit_photo">Edit Photo</h2>
                <button class="modal-close" id="closePhotoEditor">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="photo-editor-container">
                    <div class="photo-editor-canvas">
                        <canvas id="photoCanvas"></canvas>
                    </div>
                    <div class="photo-editor-controls">
                        <div class="control-group">
                            <label data-translate="brightness">Brightness</label>
                            <input type="range" id="brightnessSlider" min="-100" max="100" value="0">
                        </div>
                        <div class="control-group">
                            <label data-translate="contrast">Contrast</label>
                            <input type="range" id="contrastSlider" min="-100" max="100" value="0">
                        </div>
                        <div class="control-group">
                            <label data-translate="rotation">Rotation</label>
                            <input type="range" id="rotationSlider" min="0" max="360" value="0">
                        </div>
                        <div class="editor-buttons">
                            <button class="btn secondary" id="resetPhoto" data-translate="reset">Reset</button>
                            <button class="btn primary" id="applyPhotoChanges" data-translate="apply">Apply</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p data-translate="generating_pdf">Generating PDF...</p>
        </div>
    </div>

    <!-- Footer with Developer Credit -->
    <footer class="app-footer">
        <div class="footer-content">
            <div class="footer-brand">
                <div class="footer-logo">
                    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                        <rect width="40" height="40" rx="8" fill="url(#footerGradient)"/>
                        <path d="M12 10h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H12a2 2 0 0 1-2-2V12a2 2 0 0 1 2-2z" fill="white" opacity="0.9"/>
                        <path d="M16 16h8M16 20h8M16 24h6" stroke="url(#footerGradient2)" stroke-width="1.5" stroke-linecap="round"/>
                        <circle cx="20" cy="14" r="1.5" fill="url(#footerGradient2)"/>
                        <defs>
                            <linearGradient id="footerGradient" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#1e40af"/>
                                <stop offset="1" stop-color="#3b82f6"/>
                            </linearGradient>
                            <linearGradient id="footerGradient2" x1="0" y1="0" x2="40" y2="40" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#1e40af"/>
                                <stop offset="1" stop-color="#8b5cf6"/>
                            </linearGradient>
                        </defs>
                    </svg>
                </div>
                <div class="footer-text">
                    <h3>ELASHRAFY CV</h3>
                    <p data-translate="footer_tagline">منصة السيرة الذاتية الاحترافية</p>
                </div>
            </div>

            <div class="footer-developer">
                <div class="developer-credit">
                    <div class="developer-avatar">
                        <svg width="50" height="50" viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="25" cy="25" r="25" fill="url(#developerGradient)"/>
                            <text x="25" y="32" text-anchor="middle" fill="white" font-family="Arial" font-size="18" font-weight="bold">م.أ</text>
                            <defs>
                                <linearGradient id="developerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                    <div class="developer-info">
                        <h4>تصميم وتطوير</h4>
                        <h3>محمد الأشرافي</h3>
                        <p>مطور ومصمم تطبيقات الويب</p>
                        <div class="developer-links">
                            <a href="#" class="developer-link" title="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="#" class="developer-link" title="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="#" class="developer-link" title="Portfolio">
                                <i class="fas fa-globe"></i>
                            </a>
                            <a href="#" class="developer-link" title="Email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-stats">
                <div class="stat-item">
                    <div class="stat-number">400+</div>
                    <div class="stat-label">قالب احترافي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50K+</div>
                    <div class="stat-label">مستخدم راضي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100K+</div>
                    <div class="stat-label">سيرة ذاتية</div>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <div class="footer-copyright">
                <p>&copy; 2024 ELASHRAFY CV. جميع الحقوق محفوظة.</p>
                <p class="developer-signature">
                    صُنع بـ <i class="fas fa-heart" style="color: #ef4444;"></i> بواسطة
                    <strong>محمد الأشرافي</strong>
                </p>
            </div>

            <div class="footer-links">
                <a href="#" data-translate="privacy_policy">سياسة الخصوصية</a>
                <a href="#" data-translate="terms_of_service">شروط الخدمة</a>
                <a href="#" data-translate="contact_us">اتصل بنا</a>
                <a href="#" data-translate="help">المساعدة</a>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/photo-manager.js"></script>
    <script src="js/template-manager.js"></script>
    <script src="js/beautiful-templates-generator.js"></script>
    <script src="js/ready-templates-database.js"></script>
    <script src="js/templates-display.js"></script>
    <script src="js/live-preview.js"></script>
    <script src="js/advanced-editor.js"></script>
    <script src="js/professional-app.js"></script>
    <script src="js/professional-features-integration.js"></script>
    <script src="js/advanced-features.js"></script>
    <script src="js/auth-system.js"></script>
    <script src="js/auto-template-system.js"></script>
    <script src="js/advanced-projects.js"></script>
    <script src="js/cv-builder.js"></script>
    <script src="js/pdf-export.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
