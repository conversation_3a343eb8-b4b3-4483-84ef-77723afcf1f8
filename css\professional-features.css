/* ELASHRAFY CV - Professional Features Styles */
/* أنماط الميزات الاحترافية - الأشرافي للسيرة الذاتية */

:root {
    --elashrafy-primary: #2563eb;
    --elashrafy-secondary: #7c3aed;
    --elashrafy-accent: #f59e0b;
    --elashrafy-success: #10b981;
    --elashrafy-warning: #f59e0b;
    --elashrafy-error: #ef4444;
    --elashrafy-gold: #ffd700;
    --elashrafy-silver: #c0c0c0;
    --elashrafy-bronze: #cd7f32;
    
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    --premium-gradient: linear-gradient(135deg, var(--elashrafy-gold), var(--elashrafy-accent));
    --luxury-gradient: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    --crystal-gradient: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

/* Advanced Photo Editor Styles */
.advanced-photo-editor-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: var(--premium-gradient);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 12px;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.advanced-photo-editor-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.4);
}

.advanced-photo-editor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.advanced-photo-editor-modal.active {
    opacity: 1;
    visibility: visible;
}

.photo-editor-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 1200px;
    height: 80%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.photo-editor-header {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 20px 30px;
    background: var(--premium-gradient);
    color: white;
}

.photo-editor-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.close-editor {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-editor:hover {
    background: rgba(255, 255, 255, 0.2);
}

.photo-editor-workspace {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.photo-canvas-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8fafc;
    padding: 20px;
}

#advancedPhotoCanvas {
    max-width: 100%;
    max-height: 100%;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.photo-editor-tools {
    width: 300px;
    background: white;
    border-left: 1px solid #e5e7eb;
    padding: 20px;
    overflow-y: auto;
}

.tool-section {
    margin-bottom: 30px;
}

.tool-section h4 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
}

.filter-control {
    margin-bottom: 15px;
}

.filter-control label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #6b7280;
}

.filter-control input[type="range"] {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.filter-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--elashrafy-primary);
    border-radius: 50%;
    cursor: pointer;
}

.effects-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.effect-btn, .crop-btn {
    padding: 10px 15px;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.effect-btn:hover, .crop-btn:hover {
    border-color: var(--elashrafy-primary);
    background: #eff6ff;
    color: var(--elashrafy-primary);
}

.crop-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.photo-editor-actions {
    display: flex;
    justify-content: space-between;
    padding: 20px 30px;
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
}

.editor-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.editor-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
}

.editor-btn.primary {
    background: var(--premium-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.editor-btn:hover {
    transform: translateY(-2px);
}

/* Professional Font Selector */
.professional-font-selector {
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.font-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.font-selector-header h4 {
    margin: 0;
    color: #374151;
    font-weight: 600;
}

.font-selector-toggle {
    background: var(--elashrafy-primary);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.font-selector-toggle:hover {
    background: #1d4ed8;
    transform: scale(1.05);
}

.font-options {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.font-options.active {
    max-height: 400px;
}

.font-category {
    margin-bottom: 20px;
}

.font-category h5 {
    margin: 0 0 10px 0;
    color: #6b7280;
    font-weight: 500;
    font-size: 0.9rem;
}

.font-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.font-option {
    padding: 8px 16px;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.font-option:hover {
    border-color: var(--elashrafy-primary);
    background: #eff6ff;
    color: var(--elashrafy-primary);
}

.font-option.active {
    background: var(--elashrafy-primary);
    color: white;
    border-color: var(--elashrafy-primary);
}

/* Advanced Export Dialog */
.advanced-export-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.advanced-export-dialog.active {
    opacity: 1;
    visibility: visible;
}

.export-dialog-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.export-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: var(--premium-gradient);
    color: white;
}

.export-dialog-header h2 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
}

.close-dialog {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-dialog:hover {
    background: rgba(255, 255, 255, 0.2);
}

.export-options {
    padding: 30px;
}

.export-section {
    margin-bottom: 25px;
}

.export-section h4 {
    margin: 0 0 15px 0;
    color: #374151;
    font-weight: 600;
}

.format-options, .orientation-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.format-option, .orientation-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.format-option input, .orientation-option input {
    margin: 0;
}

.format-option span, .orientation-option span {
    font-weight: 500;
    color: #6b7280;
}

#exportQuality, #pageSize {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

#exportQuality:focus, #pageSize:focus {
    outline: none;
    border-color: var(--elashrafy-primary);
}

.export-actions {
    display: flex;
    justify-content: space-between;
    padding: 20px 30px;
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
}

.export-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.export-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
}

.export-btn.primary {
    background: var(--premium-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.export-btn:hover {
    transform: translateY(-2px);
}

/* Export Progress */
.export-progress {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    z-index: 10001;
    text-align: center;
    min-width: 300px;
}

.progress-icon {
    font-size: 3rem;
    color: var(--elashrafy-primary);
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    height: 100%;
    background: var(--premium-gradient);
    width: 0%;
    transition: width 0.3s ease;
}

/* Export Success */
.export-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    z-index: 10001;
    text-align: center;
    min-width: 300px;
}

.success-icon {
    font-size: 4rem;
    color: var(--elashrafy-success);
    margin-bottom: 20px;
}

.success-btn {
    background: var(--elashrafy-success);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 20px;
    transition: all 0.3s ease;
}

.success-btn:hover {
    background: #059669;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .photo-editor-content {
        width: 95%;
        height: 90%;
    }
    
    .photo-editor-workspace {
        flex-direction: column;
    }
    
    .photo-editor-tools {
        width: 100%;
        max-height: 200px;
    }
    
    .export-dialog-content {
        width: 95%;
    }
    
    .format-options, .orientation-options {
        flex-direction: column;
    }
}
