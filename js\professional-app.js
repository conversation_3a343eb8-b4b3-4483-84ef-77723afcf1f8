// ELASHRAFY CV - Professional Application
// التطبيق الاحترافي - الأشرافي للسيرة الذاتية

class ProfessionalCVApp {
    constructor() {
        this.currentSection = 'templates';
        this.selectedTemplate = null;
        this.currentLanguage = 'ar';
        this.templatesPerPage = 20;
        this.currentPage = 1;
        this.filteredTemplates = [];
        this.searchQuery = '';
        this.selectedCategory = 'all';
        this.selectedStyle = 'all';
        this.sortBy = 'popular';
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadTemplates();
        this.updateUI();
        this.showWelcomeMessage();
    }
    
    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.showSection(section);
            });
        });
        
        // Search
        const searchInput = document.getElementById('templateSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.filterAndDisplayTemplates();
            });
        }
        
        // Filters
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.selectedCategory = e.target.value;
                this.filterAndDisplayTemplates();
            });
        }
        
        const styleFilter = document.getElementById('styleFilter');
        if (styleFilter) {
            styleFilter.addEventListener('change', (e) => {
                this.selectedStyle = e.target.value;
                this.filterAndDisplayTemplates();
            });
        }
        
        const sortSelect = document.getElementById('sortBy');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.filterAndDisplayTemplates();
            });
        }
        
        // View toggle
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.currentTarget.classList.add('active');
                
                const view = e.currentTarget.dataset.view;
                this.changeView(view);
            });
        });
        
        // Load more templates
        const loadMoreBtn = document.getElementById('loadMoreTemplates');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreTemplates();
            });
        }
        
        // Language toggle
        const languageToggle = document.getElementById('languageToggle');
        if (languageToggle) {
            languageToggle.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }
        
        // Export button
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportCV();
            });
        }
        
        // Save button
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveCV();
            });
        }
    }
    
    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
        
        // Update sections
        document.querySelectorAll('.app-section').forEach(section => {
            section.classList.remove('active');
        });
        
        document.getElementById(`${sectionName}Section`).classList.add('active');
        
        this.currentSection = sectionName;
        
        // Load section content if needed
        this.loadSectionContent(sectionName);
    }
    
    loadSectionContent(sectionName) {
        switch (sectionName) {
            case 'templates':
                this.loadTemplates();
                break;
            case 'editor':
                this.loadEditor();
                break;
            case 'preview':
                this.loadPreview();
                break;
            case 'features':
                this.loadFeatures();
                break;
        }
    }
    
    async loadTemplates() {
        try {
            // Show loading
            this.showTemplatesLoading();
            
            // Wait for templates database to be ready
            if (!window.readyTemplatesDB) {
                await this.waitForTemplatesDB();
            }
            
            // Get all templates
            this.filteredTemplates = window.readyTemplatesDB.templates;
            
            // Apply current filters
            this.filterAndDisplayTemplates();
            
            // Update statistics
            this.updateTemplatesStats();
            
        } catch (error) {
            console.error('Error loading templates:', error);
            this.showTemplatesError();
        }
    }
    
    waitForTemplatesDB() {
        return new Promise((resolve) => {
            const checkDB = () => {
                if (window.readyTemplatesDB && window.readyTemplatesDB.templates.length > 0) {
                    resolve();
                } else {
                    setTimeout(checkDB, 100);
                }
            };
            checkDB();
        });
    }
    
    filterAndDisplayTemplates() {
        let templates = window.readyTemplatesDB.templates;
        
        // Apply search filter
        if (this.searchQuery) {
            templates = window.readyTemplatesDB.searchTemplates(this.searchQuery);
        }
        
        // Apply category filter
        if (this.selectedCategory !== 'all') {
            templates = templates.filter(t => t.category === this.selectedCategory);
        }
        
        // Apply style filter
        if (this.selectedStyle !== 'all') {
            templates = templates.filter(t => t.style === this.selectedStyle);
        }
        
        // Sort templates
        templates = window.readyTemplatesDB.sortTemplates(templates, this.sortBy);
        
        this.filteredTemplates = templates;
        this.currentPage = 1;
        
        this.displayTemplates();
        this.updateLoadMoreButton();
    }
    
    displayTemplates() {
        const grid = document.getElementById('templatesGrid');
        if (!grid) return;
        
        const startIndex = 0;
        const endIndex = this.currentPage * this.templatesPerPage;
        const templatesToShow = this.filteredTemplates.slice(startIndex, endIndex);
        
        if (templatesToShow.length === 0) {
            this.showNoTemplatesMessage();
            return;
        }
        
        grid.innerHTML = templatesToShow.map(template => this.createTemplateCard(template)).join('');
        
        // Add click events to template cards
        this.setupTemplateCardEvents();
    }
    
    createTemplateCard(template) {
        const categoryName = window.readyTemplatesDB.categories[template.category] || template.category;
        const styleName = window.readyTemplatesDB.styles[template.style] || template.style;
        
        return `
            <div class="template-card ${template.isPremium ? 'premium' : ''}" data-template-id="${template.id}">
                ${template.isPremium ? '<div class="premium-badge"><i class="fas fa-crown"></i> بريميوم</div>' : ''}
                
                <div class="template-preview">
                    <img src="${template.preview}" alt="${template.name}" loading="lazy">
                    <div class="template-overlay">
                        <div class="template-actions">
                            <button class="template-btn preview-btn" title="معاينة">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="template-btn select-btn" title="اختيار">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="template-btn favorite-btn" title="إضافة للمفضلة">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="template-info">
                    <div class="template-header">
                        <h3 class="template-name">${template.name}</h3>
                        <div class="template-rating">
                            <div class="stars">
                                ${this.generateStars(template.rating)}
                            </div>
                            <span class="rating-value">${template.rating}</span>
                        </div>
                    </div>
                    
                    <p class="template-description">${template.description}</p>
                    
                    <div class="template-meta">
                        <div class="template-tags">
                            <span class="tag category-tag">${categoryName}</span>
                            <span class="tag style-tag">${styleName}</span>
                        </div>
                        
                        <div class="template-stats">
                            <span class="stat">
                                <i class="fas fa-download"></i>
                                ${this.formatNumber(template.downloads)}
                            </span>
                        </div>
                    </div>
                    
                    <div class="template-features">
                        ${template.features.slice(0, 3).map(feature => 
                            `<span class="feature-tag">${feature}</span>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    generateStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        
        let stars = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="fas fa-star"></i>';
        }
        
        // Half star
        if (hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="far fa-star"></i>';
        }
        
        return stars;
    }
    
    formatNumber(num) {
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    setupTemplateCardEvents() {
        document.querySelectorAll('.template-card').forEach(card => {
            const templateId = card.dataset.templateId;
            
            // Preview button
            const previewBtn = card.querySelector('.preview-btn');
            previewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.previewTemplate(templateId);
            });
            
            // Select button
            const selectBtn = card.querySelector('.select-btn');
            selectBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectTemplate(templateId);
            });
            
            // Favorite button
            const favoriteBtn = card.querySelector('.favorite-btn');
            favoriteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFavorite(templateId, favoriteBtn);
            });
            
            // Card click
            card.addEventListener('click', () => {
                this.previewTemplate(templateId);
            });
        });
    }
    
    previewTemplate(templateId) {
        const template = window.readyTemplatesDB.getTemplateById(templateId);
        if (!template) return;
        
        // Create preview modal
        this.showTemplatePreviewModal(template);
    }
    
    selectTemplate(templateId) {
        const template = window.readyTemplatesDB.getTemplateById(templateId);
        if (!template) return;
        
        this.selectedTemplate = template;
        
        // Show success message
        this.showNotification(`تم اختيار قالب "${template.name}" بنجاح!`, 'success');
        
        // Switch to editor
        this.showSection('editor');
        
        // Load template in editor
        this.loadTemplateInEditor(template);
    }
    
    toggleFavorite(templateId, button) {
        const icon = button.querySelector('i');
        const isFavorited = icon.classList.contains('fas');
        
        if (isFavorited) {
            icon.classList.remove('fas');
            icon.classList.add('far');
            this.showNotification('تم إزالة القالب من المفضلة', 'info');
        } else {
            icon.classList.remove('far');
            icon.classList.add('fas');
            this.showNotification('تم إضافة القالب للمفضلة', 'success');
        }
        
        // Save to localStorage
        this.saveFavoriteTemplate(templateId, !isFavorited);
    }
    
    saveFavoriteTemplate(templateId, isFavorite) {
        let favorites = JSON.parse(localStorage.getItem('elashrafy-favorites') || '[]');
        
        if (isFavorite) {
            if (!favorites.includes(templateId)) {
                favorites.push(templateId);
            }
        } else {
            favorites = favorites.filter(id => id !== templateId);
        }
        
        localStorage.setItem('elashrafy-favorites', JSON.stringify(favorites));
    }
    
    loadMoreTemplates() {
        this.currentPage++;
        this.displayTemplates();
        this.updateLoadMoreButton();
    }
    
    updateLoadMoreButton() {
        const loadMoreBtn = document.getElementById('loadMoreTemplates');
        if (!loadMoreBtn) return;
        
        const totalShown = this.currentPage * this.templatesPerPage;
        const hasMore = totalShown < this.filteredTemplates.length;
        
        if (hasMore) {
            loadMoreBtn.style.display = 'flex';
            const remaining = this.filteredTemplates.length - totalShown;
            loadMoreBtn.innerHTML = `
                <i class="fas fa-plus"></i>
                تحميل ${Math.min(remaining, this.templatesPerPage)} قالب إضافي
            `;
        } else {
            loadMoreBtn.style.display = 'none';
        }
    }
    
    updateTemplatesStats() {
        const stats = window.readyTemplatesDB.getTemplateStats();
        
        const totalElement = document.getElementById('totalTemplates');
        if (totalElement) {
            totalElement.textContent = `${stats.total}+`;
        }
        
        // Update other stats if elements exist
        const elements = {
            'totalCategories': stats.categories,
            'totalStyles': stats.styles,
            'averageRating': stats.averageRating
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    showTemplatesLoading() {
        const grid = document.getElementById('templatesGrid');
        if (grid) {
            grid.innerHTML = `
                <div class="loading-templates">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <p>جاري تحميل القوالب الجميلة...</p>
                </div>
            `;
        }
    }
    
    showTemplatesError() {
        const grid = document.getElementById('templatesGrid');
        if (grid) {
            grid.innerHTML = `
                <div class="templates-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>خطأ في تحميل القوالب</h3>
                    <p>حدث خطأ أثناء تحميل القوالب. يرجى إعادة تحميل الصفحة.</p>
                    <button class="btn primary" onclick="location.reload()">
                        <i class="fas fa-redo"></i>
                        إعادة تحميل
                    </button>
                </div>
            `;
        }
    }
    
    showNoTemplatesMessage() {
        const grid = document.getElementById('templatesGrid');
        if (grid) {
            grid.innerHTML = `
                <div class="no-templates">
                    <i class="fas fa-search"></i>
                    <h3>لا توجد قوالب مطابقة</h3>
                    <p>لم نجد قوالب تطابق معايير البحث الحالية. جرب تغيير الفلاتر أو البحث.</p>
                    <button class="btn secondary" onclick="professionalApp.clearFilters()">
                        <i class="fas fa-times"></i>
                        مسح الفلاتر
                    </button>
                </div>
            `;
        }
    }
    
    clearFilters() {
        this.searchQuery = '';
        this.selectedCategory = 'all';
        this.selectedStyle = 'all';
        this.sortBy = 'popular';
        
        // Reset form elements
        const searchInput = document.getElementById('templateSearch');
        if (searchInput) searchInput.value = '';
        
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) categoryFilter.value = 'all';
        
        const styleFilter = document.getElementById('styleFilter');
        if (styleFilter) styleFilter.value = 'all';
        
        const sortSelect = document.getElementById('sortBy');
        if (sortSelect) sortSelect.value = 'popular';
        
        this.filterAndDisplayTemplates();
    }
    
    changeView(view) {
        const grid = document.getElementById('templatesGrid');
        if (grid) {
            grid.className = `templates-grid view-${view}`;
        }
    }
    
    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        
        const html = document.documentElement;
        const langToggle = document.getElementById('languageToggle');
        
        if (this.currentLanguage === 'en') {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            langToggle.querySelector('.lang-text').textContent = 'EN';
        } else {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            langToggle.querySelector('.lang-text').textContent = 'عر';
        }
        
        // Update UI text based on language
        this.updateLanguageText();
    }
    
    updateLanguageText() {
        // This would update all text elements based on current language
        // Implementation depends on your translation system
        console.log(`Language changed to: ${this.currentLanguage}`);
    }
    
    loadEditor() {
        if (!this.selectedTemplate) {
            return;
        }
        
        // Load advanced editor
        if (window.advancedEditor) {
            window.advancedEditor.loadTemplate(this.selectedTemplate);
        }
    }
    
    loadPreview() {
        if (!this.selectedTemplate) {
            return;
        }
        
        // Load live preview
        if (window.livePreview) {
            window.livePreview.updatePreview(this.selectedTemplate);
        }
    }
    
    loadFeatures() {
        // Features section is static, no loading needed
    }
    
    exportCV() {
        if (!this.selectedTemplate) {
            this.showNotification('يرجى اختيار قالب أولاً', 'warning');
            return;
        }
        
        this.showNotification('جاري تصدير السيرة الذاتية...', 'info');
        
        // Export functionality would be implemented here
        setTimeout(() => {
            this.showNotification('تم تصدير السيرة الذاتية بنجاح!', 'success');
        }, 2000);
    }
    
    saveCV() {
        if (!this.selectedTemplate) {
            this.showNotification('يرجى اختيار قالب أولاً', 'warning');
            return;
        }
        
        // Save functionality would be implemented here
        this.showNotification('تم حفظ السيرة الذاتية بنجاح!', 'success');
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 4000);
        
        // Close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }
    
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    getNotificationColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || '#3b82f6';
    }
    
    showWelcomeMessage() {
        setTimeout(() => {
            this.showNotification('مرحباً بك في ELASHRAFY CV - منصة السيرة الذاتية الاحترافية!', 'success');
        }, 1000);
    }
    
    updateUI() {
        // Update any UI elements that need to be refreshed
        this.updateTemplatesStats();
    }
}

// Initialize the professional app
const professionalApp = new ProfessionalCVApp();

// Export for global access
window.professionalApp = professionalApp;

// Global function for section switching
window.showSection = (sectionName) => {
    professionalApp.showSection(sectionName);
};

// Add notification animations CSS
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .notification-close {
        position: absolute;
        top: 8px;
        right: 8px;
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        opacity: 0.7;
    }
    
    .notification-close:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.1);
    }
`;

document.head.appendChild(notificationStyles);
