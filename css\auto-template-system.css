/* ELASHRAFY CV - Auto Template System Styles */
/* أنماط نظام إضافة القوالب التلقائي - الأشرافي للسيرة الذاتية */

/* Template Generation Panel */
.template-generation-panel {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-6);
    position: relative;
    overflow: hidden;
}

.template-generation-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Generation Header */
.generation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.generation-header h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.generation-header h3 i {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s ease-in-out infinite;
}

.generation-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.status-indicator {
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.active {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    animation: pulse 2s ease-in-out infinite;
}

.status-indicator.processing {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    animation: spin 1s linear infinite;
}

.status-indicator.completed {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.template-count {
    background: linear-gradient(135deg, var(--accent-color), #8b5cf6);
    color: white;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-lg);
    font-weight: 700;
    font-size: var(--font-size-sm);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* Generation Controls */
.generation-controls {
    display: flex;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    flex-wrap: wrap;
}

.generation-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.generation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.generation-btn:hover::before {
    left: 100%;
}

.generation-btn:not(.secondary):not(.tertiary) {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    color: white;
    box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
}

.generation-btn:not(.secondary):not(.tertiary):hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.generation-btn.secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

.generation-btn.secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
}

.generation-btn.tertiary {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.generation-btn.tertiary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Generation Progress */
.generation-progress {
    margin-bottom: var(--spacing-4);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-2);
    position: relative;
}

.progress-fill {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
    text-align: center;
}

/* Recent Templates */
.recent-templates h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.recent-templates h4::before {
    content: '🆕';
    font-size: var(--font-size-base);
}

.recent-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    max-height: 200px;
    overflow-y: auto;
}

.recent-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.recent-item:hover {
    border-color: var(--primary-color);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.recent-preview {
    width: 50px;
    height: 35px;
    border-radius: var(--radius-md);
    overflow: hidden;
    border: 1px solid var(--gray-200);
    flex-shrink: 0;
}

.recent-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recent-info {
    flex: 1;
    min-width: 0;
}

.recent-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.recent-category {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    margin-bottom: var(--spacing-1);
}

.recent-time {
    font-size: var(--font-size-xs);
    color: var(--accent-color);
    font-weight: 500;
}

.recent-actions {
    display: flex;
    gap: var(--spacing-1);
}

.recent-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: var(--gray-100);
    color: var(--gray-600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.recent-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* New Template Animation */
.template-card.new-template {
    position: relative;
    overflow: hidden;
}

.template-card.new-template::before {
    content: 'جديد!';
    position: absolute;
    top: 10px;
    left: -30px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 4px 40px;
    font-size: 12px;
    font-weight: 600;
    transform: rotate(-45deg);
    z-index: 10;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.template-card.new-template {
    border-color: #10b981;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
    animation: newTemplateGlow 2s ease-in-out infinite;
}

@keyframes newTemplateGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.2); }
    50% { box-shadow: 0 0 30px rgba(16, 185, 129, 0.4); }
}

/* Template Notifications */
.template-notification {
    border-left: 4px solid #10b981;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: var(--spacing-1);
}

.notification-text {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.notification-close {
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(100%);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .template-generation-panel {
        padding: var(--spacing-4);
    }
    
    .generation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
    
    .generation-status {
        align-self: stretch;
        justify-content: space-between;
    }
    
    .generation-controls {
        flex-direction: column;
    }
    
    .generation-btn {
        justify-content: center;
    }
    
    .recent-item {
        padding: var(--spacing-2);
    }
    
    .recent-preview {
        width: 40px;
        height: 28px;
    }
}

/* RTL Support */
[dir="rtl"] .recent-item:hover {
    transform: translateX(-4px);
}

[dir="rtl"] .template-card.new-template::before {
    left: auto;
    right: -30px;
    transform: rotate(45deg);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .template-generation-panel {
        background: linear-gradient(135deg, #1f2937, #111827);
        border-color: #374151;
    }
    
    .recent-item {
        background: #1f2937;
        border-color: #374151;
    }
    
    .recent-name {
        color: #f9fafb;
    }
    
    .recent-category {
        color: #9ca3af;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .template-generation-panel {
        border-width: 3px;
    }
    
    .generation-btn {
        border: 2px solid currentColor;
    }
    
    .recent-item {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .template-generation-panel::before,
    .progress-fill::after,
    .status-indicator.active,
    .template-card.new-template {
        animation: none;
    }
    
    .generation-btn::before {
        display: none;
    }
}
