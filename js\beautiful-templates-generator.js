// ELASHRAFY CV - Beautiful Templates Generator
// مولد القوالب الجميلة - الأشرافي للسيرة الذاتية

class BeautifulTemplatesGenerator {
    constructor() {
        this.templates = [];
        this.templateStyles = this.getTemplateStyles();
        this.colorPalettes = this.getColorPalettes();
        this.layoutPatterns = this.getLayoutPatterns();
        this.decorativeElements = this.getDecorativeElements();
        
        this.init();
    }
    
    init() {
        this.generateAllTemplates();
        this.setupTemplatePreloader();
    }
    
    async generateAllTemplates() {
        console.log('🎨 بدء إنشاء أكثر من 400 قالب جميل...');
        
        const categories = [
            'modern', 'creative', 'executive', 'academic', 'technical', 
            'medical', 'business', 'legal', 'artistic', 'minimalist',
            'luxury', 'corporate', 'startup', 'freelancer', 'designer'
        ];
        
        let templateCount = 0;
        
        for (const category of categories) {
            for (let i = 0; i < 30; i++) { // 30 قالب لكل فئة
                const template = await this.generateBeautifulTemplate(category, i + 1);
                this.templates.push(template);
                templateCount++;
                
                // تحديث العداد
                if (templateCount % 50 === 0) {
                    console.log(`✨ تم إنشاء ${templateCount} قالب...`);
                }
            }
        }
        
        console.log(`🎉 تم إنشاء ${templateCount} قالب جميل بنجاح!`);
        
        // إضافة القوالب إلى النظام
        if (window.templateManager) {
            window.templateManager.templates = this.templates;
        }
    }
    
    async generateBeautifulTemplate(category, index) {
        const style = this.getRandomStyle();
        const colorPalette = this.getRandomColorPalette();
        const layout = this.getRandomLayout();
        const decorative = this.getRandomDecorativeElements();
        
        const templateId = `${category}_beautiful_${index}_${Date.now()}`;
        
        return {
            id: templateId,
            name: this.generateTemplateName(category, style, index),
            category: category,
            description: this.generateTemplateDescription(category, style),
            preview: this.generateBeautifulPreview(style, colorPalette, layout, decorative),
            isPremium: Math.random() > 0.6, // 40% premium
            rating: (4.2 + Math.random() * 0.8).toFixed(1),
            downloads: Math.floor(Math.random() * 8000) + 1000,
            tags: this.generateTemplateTags(category, style),
            colors: colorPalette,
            layout: layout,
            style: style,
            decorative: decorative,
            atsCompatible: true,
            multiPage: Math.random() > 0.5,
            photoSupport: true,
            customizable: true,
            responsive: true,
            printOptimized: true,
            createdAt: new Date().toISOString(),
            features: this.generateTemplateFeatures(style, layout)
        };
    }
    
    generateBeautifulPreview(style, colors, layout, decorative) {
        const svg = this.createStunningSVG(style, colors, layout, decorative);
        return `data:image/svg+xml;base64,${btoa(svg)}`;
    }
    
    createStunningSVG(style, colors, layout, decorative) {
        const { primary, secondary, accent, background, text } = colors;
        
        const svgTemplates = {
            'glass-morphism': this.createGlassMorphismTemplate(colors, layout, decorative),
            'neo-brutalism': this.createNeoBrutalismTemplate(colors, layout, decorative),
            'gradient-mesh': this.createGradientMeshTemplate(colors, layout, decorative),
            'organic-shapes': this.createOrganicShapesTemplate(colors, layout, decorative),
            'geometric-art': this.createGeometricArtTemplate(colors, layout, decorative),
            'watercolor': this.createWatercolorTemplate(colors, layout, decorative),
            'paper-cut': this.createPaperCutTemplate(colors, layout, decorative),
            'neon-cyber': this.createNeonCyberTemplate(colors, layout, decorative),
            'vintage-retro': this.createVintageRetroTemplate(colors, layout, decorative),
            'crystal-clear': this.createCrystalClearTemplate(colors, layout, decorative),
            'aurora-borealis': this.createAuroraBorealisTemplate(colors, layout, decorative),
            'holographic': this.createHolographicTemplate(colors, layout, decorative)
        };
        
        return svgTemplates[style] || svgTemplates['glass-morphism'];
    }
    
    createGlassMorphismTemplate(colors, layout, decorative) {
        const { primary, secondary, accent, background } = colors;
        
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="glassGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.8" />
                    <stop offset="50%" style="stop-color:${accent};stop-opacity:0.6" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:0.4" />
                </linearGradient>
                <filter id="blur" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
                </filter>
                <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                    <feMerge> 
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>
            
            <!-- Background with gradient -->
            <rect width="280" height="200" fill="url(#glassGrad)" rx="12"/>
            
            <!-- Glass morphism shapes -->
            <circle cx="50" cy="50" r="40" fill="rgba(255,255,255,0.2)" filter="url(#blur)"/>
            <circle cx="230" cy="150" r="35" fill="rgba(255,255,255,0.15)" filter="url(#blur)"/>
            
            <!-- Glass header -->
            <rect x="20" y="20" width="240" height="60" fill="rgba(255,255,255,0.25)" 
                  rx="15" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
            
            <!-- Photo with glass effect -->
            <circle cx="60" cy="50" r="18" fill="rgba(255,255,255,0.9)" filter="url(#glow)"/>
            <circle cx="60" cy="50" r="14" fill="${accent}" opacity="0.7"/>
            
            <!-- Glass text areas -->
            <rect x="90" y="35" width="130" height="8" fill="rgba(255,255,255,0.9)" rx="4"/>
            <rect x="90" y="47" width="90" height="6" fill="rgba(255,255,255,0.7)" rx="3"/>
            <rect x="90" y="57" width="110" height="4" fill="rgba(255,255,255,0.6)" rx="2"/>
            
            <!-- Glass content sections -->
            <rect x="20" y="95" width="240" height="40" fill="rgba(255,255,255,0.2)" 
                  rx="12" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="30" y="105" width="70" height="6" fill="rgba(255,255,255,0.8)" rx="3"/>
            <rect x="30" y="115" width="200" height="3" fill="rgba(255,255,255,0.6)" rx="1"/>
            <rect x="30" y="122" width="180" height="3" fill="rgba(255,255,255,0.5)" rx="1"/>
            
            <!-- Glass skills section -->
            <rect x="20" y="150" width="240" height="30" fill="rgba(255,255,255,0.15)" 
                  rx="10" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="30" y="160" width="50" height="5" fill="rgba(255,255,255,0.8)" rx="2"/>
            <rect x="90" y="160" width="45" height="5" fill="rgba(255,255,255,0.7)" rx="2"/>
            <rect x="145" y="160" width="40" height="5" fill="rgba(255,255,255,0.6)" rx="2"/>
            
            <!-- Decorative glass elements -->
            <circle cx="250" cy="30" r="8" fill="rgba(255,255,255,0.3)" filter="url(#blur)"/>
            <circle cx="30" cy="180" r="6" fill="rgba(255,255,255,0.25)" filter="url(#blur)"/>
        </svg>`;
    }
    
    createNeoBrutalismTemplate(colors, layout, decorative) {
        const { primary, secondary, accent } = colors;
        
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="4" dy="4" stdDeviation="0" flood-color="#000" flood-opacity="1"/>
                </filter>
            </defs>
            
            <!-- Bold background -->
            <rect width="280" height="200" fill="${primary}" stroke="#000" stroke-width="3"/>
            
            <!-- Brutal header -->
            <rect x="10" y="10" width="260" height="50" fill="${accent}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            
            <!-- Bold photo frame -->
            <rect x="25" y="20" width="35" height="35" fill="#fff" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            <rect x="28" y="23" width="29" height="29" fill="${secondary}"/>
            
            <!-- Brutal typography -->
            <rect x="70" y="25" width="120" height="8" fill="#000"/>
            <rect x="70" y="37" width="80" height="6" fill="#000"/>
            <rect x="70" y="47" width="100" height="4" fill="#000"/>
            
            <!-- Bold content blocks -->
            <rect x="15" y="75" width="250" height="35" fill="${secondary}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            <rect x="25" y="85" width="60" height="6" fill="#000"/>
            <rect x="25" y="95" width="200" height="3" fill="#000"/>
            
            <!-- Brutal skills -->
            <rect x="15" y="125" width="80" height="20" fill="${accent}" 
                  stroke="#000" stroke-width="2" filter="url(#shadow)"/>
            <rect x="105" y="125" width="70" height="20" fill="${primary}" 
                  stroke="#000" stroke-width="2" filter="url(#shadow)"/>
            <rect x="185" y="125" width="80" height="20" fill="${secondary}" 
                  stroke="#000" stroke-width="2" filter="url(#shadow)"/>
            
            <!-- Bold decorative elements -->
            <rect x="15" y="160" width="250" height="25" fill="#fff" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            <rect x="25" y="170" width="40" height="5" fill="#000"/>
            <rect x="75" y="170" width="35" height="5" fill="#000"/>
            <rect x="120" y="170" width="45" height="5" fill="#000"/>
        </svg>`;
    }
    
    createGradientMeshTemplate(colors, layout, decorative) {
        const { primary, secondary, accent } = colors;
        
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="mesh1" cx="30%" cy="30%" r="40%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:${accent};stop-opacity:0.3" />
                </radialGradient>
                <radialGradient id="mesh2" cx="70%" cy="70%" r="50%">
                    <stop offset="0%" style="stop-color:${secondary};stop-opacity:0.7" />
                    <stop offset="100%" style="stop-color:${primary};stop-opacity:0.2" />
                </radialGradient>
                <radialGradient id="mesh3" cx="50%" cy="20%" r="60%">
                    <stop offset="0%" style="stop-color:${accent};stop-opacity:0.6" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:0.1" />
                </radialGradient>
                <filter id="meshBlur" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="2"/>
                </filter>
            </defs>
            
            <!-- Mesh background -->
            <rect width="280" height="200" fill="url(#mesh1)" rx="16"/>
            <rect width="280" height="200" fill="url(#mesh2)" rx="16" opacity="0.7"/>
            <rect width="280" height="200" fill="url(#mesh3)" rx="16" opacity="0.5"/>
            
            <!-- Floating header -->
            <rect x="20" y="20" width="240" height="50" fill="rgba(255,255,255,0.9)" 
                  rx="12" filter="url(#meshBlur)"/>
            
            <!-- Mesh photo -->
            <circle cx="55" cy="45" r="16" fill="rgba(255,255,255,0.95)"/>
            <circle cx="55" cy="45" r="12" fill="url(#mesh1)"/>
            
            <!-- Flowing text -->
            <rect x="80" y="30" width="140" height="7" fill="rgba(0,0,0,0.8)" rx="3"/>
            <rect x="80" y="42" width="100" height="5" fill="rgba(0,0,0,0.6)" rx="2"/>
            <rect x="80" y="52" width="120" height="4" fill="rgba(0,0,0,0.5)" rx="2"/>
            
            <!-- Mesh content -->
            <rect x="20" y="85" width="240" height="35" fill="rgba(255,255,255,0.8)" 
                  rx="10" filter="url(#meshBlur)"/>
            <rect x="30" y="95" width="80" height="6" fill="rgba(0,0,0,0.7)" rx="3"/>
            <rect x="30" y="105" width="200" height="3" fill="rgba(0,0,0,0.5)" rx="1"/>
            <rect x="30" y="112" width="180" height="3" fill="rgba(0,0,0,0.4)" rx="1"/>
            
            <!-- Gradient skills -->
            <rect x="20" y="135" width="240" height="25" fill="rgba(255,255,255,0.7)" 
                  rx="8" filter="url(#meshBlur)"/>
            <rect x="30" y="145" width="60" height="5" fill="url(#mesh1)" rx="2"/>
            <rect x="100" y="145" width="50" height="5" fill="url(#mesh2)" rx="2"/>
            <rect x="160" y="145" width="55" height="5" fill="url(#mesh3)" rx="2"/>
            
            <!-- Floating elements -->
            <circle cx="250" cy="180" r="12" fill="rgba(255,255,255,0.6)" filter="url(#meshBlur)"/>
            <circle cx="30" cy="175" r="8" fill="rgba(255,255,255,0.5)" filter="url(#meshBlur)"/>
        </svg>`;
    }
    
    createOrganicShapesTemplate(colors, layout, decorative) {
        const { primary, secondary, accent } = colors;
        
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="organicShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="2" dy="3" stdDeviation="4" flood-color="${primary}" flood-opacity="0.3"/>
                </filter>
            </defs>
            
            <!-- Organic background -->
            <path d="M0,0 Q140,30 280,0 Q250,100 280,200 Q140,170 0,200 Q30,100 0,0 Z" 
                  fill="${primary}" opacity="0.1"/>
            
            <!-- Flowing header -->
            <path d="M20,20 Q140,10 260,25 Q250,50 240,70 Q140,60 40,75 Q30,50 20,20 Z" 
                  fill="${primary}" opacity="0.8" filter="url(#organicShadow)"/>
            
            <!-- Organic photo -->
            <path d="M40,30 Q65,25 70,50 Q65,65 40,60 Q35,45 40,30 Z" 
                  fill="rgba(255,255,255,0.9)"/>
            <circle cx="55" cy="47" r="10" fill="${accent}" opacity="0.7"/>
            
            <!-- Flowing text areas -->
            <path d="M85,35 Q180,30 220,40 Q215,45 85,42 Z" fill="rgba(255,255,255,0.9)"/>
            <path d="M85,50 Q150,47 180,52 Q175,57 85,55 Z" fill="rgba(255,255,255,0.8)"/>
            
            <!-- Organic content -->
            <path d="M25,90 Q140,80 255,95 Q250,120 240,130 Q140,125 40,135 Q30,115 25,90 Z" 
                  fill="${secondary}" opacity="0.6" filter="url(#organicShadow)"/>
            <path d="M35,105 Q100,100 120,108 Q115,113 35,110 Z" fill="rgba(255,255,255,0.9)"/>
            <path d="M35,118 Q180,115 200,120 Q195,125 35,123 Z" fill="rgba(255,255,255,0.7)"/>
            
            <!-- Organic skills -->
            <path d="M30,150 Q80,145 90,155 Q85,165 30,160 Z" fill="${accent}" opacity="0.8"/>
            <path d="M100,150 Q140,148 150,158 Q145,168 100,163 Z" fill="${primary}" opacity="0.7"/>
            <path d="M160,152 Q200,150 210,160 Q205,170 160,165 Z" fill="${secondary}" opacity="0.8"/>
            
            <!-- Decorative organic elements -->
            <path d="M220,170 Q250,165 260,180 Q255,190 220,185 Z" fill="${accent}" opacity="0.4"/>
            <circle cx="40" cy="180" r="6" fill="${primary}" opacity="0.5"/>
        </svg>`;
    }
    
    createGeometricArtTemplate(colors, layout, decorative) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="triangles" patternUnits="userSpaceOnUse" width="20" height="20">
                    <polygon points="10,2 18,16 2,16" fill="${accent}" opacity="0.1"/>
                </pattern>
                <linearGradient id="geoGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:1" />
                </linearGradient>
            </defs>

            <!-- Geometric background -->
            <rect width="280" height="200" fill="url(#triangles)"/>
            <polygon points="0,0 280,0 140,100" fill="url(#geoGrad)" opacity="0.8"/>

            <!-- Geometric header -->
            <polygon points="20,20 260,15 250,65 30,70" fill="${primary}"/>

            <!-- Geometric photo -->
            <polygon points="45,30 70,25 75,50 50,55" fill="white"/>
            <polygon points="48,33 67,28 72,47 53,52" fill="${accent}" opacity="0.7"/>

            <!-- Angular text -->
            <polygon points="85,30 200,25 195,35 90,38" fill="white"/>
            <polygon points="85,45 160,42 158,50 87,52" fill="white" opacity="0.9"/>

            <!-- Geometric content -->
            <polygon points="25,85 255,80 250,115 30,120" fill="${secondary}" opacity="0.7"/>
            <polygon points="35,95 120,92 118,100 37,102" fill="white"/>
            <polygon points="35,108 200,105 198,112 37,115" fill="white" opacity="0.8"/>

            <!-- Geometric skills -->
            <polygon points="30,140 80,138 78,155 32,157" fill="${accent}"/>
            <polygon points="90,140 140,138 138,155 92,157" fill="${primary}"/>
            <polygon points="150,140 200,138 198,155 152,157" fill="${secondary}"/>

            <!-- Decorative triangles -->
            <polygon points="230,160 250,150 260,170" fill="${accent}" opacity="0.6"/>
            <polygon points="20,170 30,160 40,180" fill="${primary}" opacity="0.5"/>
        </svg>`;
    }

    createWatercolorTemplate(colors, layout, decorative) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="watercolor" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="3" result="blur"/>
                    <feColorMatrix in="blur" mode="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 18 -7"/>
                </filter>
                <radialGradient id="waterGrad1" cx="30%" cy="40%" r="60%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.6" />
                    <stop offset="100%" style="stop-color:${primary};stop-opacity:0.1" />
                </radialGradient>
                <radialGradient id="waterGrad2" cx="70%" cy="60%" r="50%">
                    <stop offset="0%" style="stop-color:${accent};stop-opacity:0.5" />
                    <stop offset="100%" style="stop-color:${accent};stop-opacity:0.1" />
                </radialGradient>
            </defs>

            <!-- Watercolor background -->
            <circle cx="80" cy="60" r="50" fill="url(#waterGrad1)" filter="url(#watercolor)"/>
            <circle cx="200" cy="140" r="60" fill="url(#waterGrad2)" filter="url(#watercolor)"/>
            <circle cx="150" cy="30" r="40" fill="${secondary}" opacity="0.3" filter="url(#watercolor)"/>

            <!-- Soft header -->
            <rect x="20" y="20" width="240" height="50" fill="rgba(255,255,255,0.8)"
                  rx="15" filter="url(#watercolor)"/>

            <!-- Watercolor photo -->
            <circle cx="55" cy="45" r="18" fill="rgba(255,255,255,0.9)" filter="url(#watercolor)"/>
            <circle cx="55" cy="45" r="14" fill="${accent}" opacity="0.6"/>

            <!-- Soft text areas -->
            <rect x="85" y="30" width="140" height="8" fill="rgba(0,0,0,0.7)" rx="4" filter="url(#watercolor)"/>
            <rect x="85" y="42" width="100" height="6" fill="rgba(0,0,0,0.6)" rx="3" filter="url(#watercolor)"/>
            <rect x="85" y="52" width="120" height="4" fill="rgba(0,0,0,0.5)" rx="2" filter="url(#watercolor)"/>

            <!-- Watercolor content -->
            <rect x="20" y="85" width="240" height="35" fill="rgba(255,255,255,0.7)"
                  rx="12" filter="url(#watercolor)"/>
            <rect x="30" y="95" width="80" height="6" fill="rgba(0,0,0,0.6)" rx="3" filter="url(#watercolor)"/>
            <rect x="30" y="105" width="200" height="3" fill="rgba(0,0,0,0.4)" rx="1" filter="url(#watercolor)"/>
            <rect x="30" y="112" width="180" height="3" fill="rgba(0,0,0,0.3)" rx="1" filter="url(#watercolor)"/>

            <!-- Watercolor skills -->
            <rect x="20" y="135" width="240" height="25" fill="rgba(255,255,255,0.6)"
                  rx="8" filter="url(#watercolor)"/>
            <rect x="30" y="145" width="60" height="5" fill="${primary}" opacity="0.7" rx="2" filter="url(#watercolor)"/>
            <rect x="100" y="145" width="50" height="5" fill="${accent}" opacity="0.6" rx="2" filter="url(#watercolor)"/>
            <rect x="160" y="145" width="55" height="5" fill="${secondary}" opacity="0.7" rx="2" filter="url(#watercolor)"/>
        </svg>`;
    }

    createNeonCyberTemplate(colors, layout, decorative) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="neonGlow" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
                <linearGradient id="cyberGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#00ffff;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#ff00ff;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ffff00;stop-opacity:1" />
                </linearGradient>
                <pattern id="grid" patternUnits="userSpaceOnUse" width="20" height="20">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#00ffff" stroke-width="0.5" opacity="0.3"/>
                </pattern>
            </defs>

            <!-- Cyber background -->
            <rect width="280" height="200" fill="#0a0a0a"/>
            <rect width="280" height="200" fill="url(#grid)"/>

            <!-- Neon header -->
            <rect x="20" y="20" width="240" height="50" fill="none"
                  stroke="url(#cyberGrad)" stroke-width="2" rx="8" filter="url(#neonGlow)"/>

            <!-- Cyber photo -->
            <rect x="35" y="30" width="30" height="30" fill="none"
                  stroke="#00ffff" stroke-width="2" filter="url(#neonGlow)"/>
            <rect x="38" y="33" width="24" height="24" fill="#00ffff" opacity="0.3"/>

            <!-- Neon text -->
            <rect x="75" y="35" width="120" height="6" fill="#00ffff" filter="url(#neonGlow)"/>
            <rect x="75" y="45" width="80" height="4" fill="#ff00ff" filter="url(#neonGlow)"/>
            <rect x="75" y="53" width="100" height="3" fill="#ffff00" filter="url(#neonGlow)"/>

            <!-- Cyber content -->
            <rect x="20" y="85" width="240" height="35" fill="none"
                  stroke="#ff00ff" stroke-width="1" rx="6" filter="url(#neonGlow)"/>
            <rect x="30" y="95" width="70" height="4" fill="#ff00ff" filter="url(#neonGlow)"/>
            <rect x="30" y="105" width="180" height="2" fill="#00ffff" opacity="0.7"/>
            <rect x="30" y="112" width="160" height="2" fill="#ffff00" opacity="0.6"/>

            <!-- Neon skills -->
            <rect x="30" y="140" width="50" height="15" fill="none"
                  stroke="#00ffff" stroke-width="1" filter="url(#neonGlow)"/>
            <rect x="90" y="140" width="45" height="15" fill="none"
                  stroke="#ff00ff" stroke-width="1" filter="url(#neonGlow)"/>
            <rect x="145" y="140" width="40" height="15" fill="none"
                  stroke="#ffff00" stroke-width="1" filter="url(#neonGlow)"/>

            <!-- Cyber decorations -->
            <circle cx="250" cy="30" r="5" fill="#00ffff" filter="url(#neonGlow)"/>
            <circle cx="30" cy="180" r="4" fill="#ff00ff" filter="url(#neonGlow)"/>
            <rect x="200" y="170" width="60" height="2" fill="url(#cyberGrad)" filter="url(#neonGlow)"/>
        </svg>`;
    }

    createCrystalClearTemplate(colors, layout, decorative) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="crystal1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.9" />
                    <stop offset="50%" style="stop-color:rgba(255,255,255,0.8);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${accent};stop-opacity:0.7" />
                </linearGradient>
                <filter id="crystalShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="8" stdDeviation="8" flood-color="${primary}" flood-opacity="0.2"/>
                </filter>
                <filter id="innerGlow" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>

            <!-- Crystal background -->
            <polygon points="0,0 280,0 260,100 280,200 0,200 20,100" fill="url(#crystal1)" opacity="0.1"/>

            <!-- Crystal header -->
            <polygon points="20,20 260,15 250,65 30,70" fill="rgba(255,255,255,0.95)"
                     stroke="rgba(255,255,255,0.5)" stroke-width="1" filter="url(#crystalShadow)"/>

            <!-- Crystal photo -->
            <polygon points="40,30 65,25 70,50 45,55" fill="rgba(255,255,255,0.98)" filter="url(#innerGlow)"/>
            <polygon points="43,33 62,28 67,47 48,52" fill="${accent}" opacity="0.6"/>

            <!-- Crystal text -->
            <polygon points="80,32 190,28 188,38 82,40" fill="rgba(0,0,0,0.8)" filter="url(#innerGlow)"/>
            <polygon points="80,45 150,42 148,50 82,52" fill="rgba(0,0,0,0.7)" filter="url(#innerGlow)"/>
            <polygon points="80,55 170,52 168,58 82,60" fill="rgba(0,0,0,0.6)" filter="url(#innerGlow)"/>

            <!-- Crystal content -->
            <polygon points="25,85 255,80 250,115 30,120" fill="rgba(255,255,255,0.9)"
                     stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#crystalShadow)"/>
            <polygon points="35,95 110,92 108,100 37,102" fill="rgba(0,0,0,0.7)" filter="url(#innerGlow)"/>
            <polygon points="35,105 200,102 198,108 37,110" fill="rgba(0,0,0,0.5)" filter="url(#innerGlow)"/>

            <!-- Crystal skills -->
            <polygon points="30,140 85,138 83,155 32,157" fill="rgba(255,255,255,0.8)"
                     stroke="${primary}" stroke-width="1" filter="url(#crystalShadow)"/>
            <polygon points="95,140 150,138 148,155 97,157" fill="rgba(255,255,255,0.8)"
                     stroke="${accent}" stroke-width="1" filter="url(#crystalShadow)"/>
            <polygon points="160,140 215,138 213,155 162,157" fill="rgba(255,255,255,0.8)"
                     stroke="${secondary}" stroke-width="1" filter="url(#crystalShadow)"/>

            <!-- Crystal decorations -->
            <polygon points="230,165 250,160 255,175 235,180" fill="rgba(255,255,255,0.7)" filter="url(#innerGlow)"/>
            <polygon points="20,175 35,170 40,185 25,190" fill="rgba(255,255,255,0.6)" filter="url(#innerGlow)"/>
        </svg>`;
    }

    getTemplateStyles() {
        return [
            'glass-morphism', 'neo-brutalism', 'gradient-mesh', 'organic-shapes',
            'geometric-art', 'watercolor', 'paper-cut', 'neon-cyber',
            'vintage-retro', 'crystal-clear', 'aurora-borealis', 'holographic',
            'minimalist-zen', 'art-deco', 'bauhaus', 'memphis-design',
            'swiss-design', 'material-design', 'neumorphism', 'claymorphism'
        ];
    }

    getColorPalettes() {
        return [
            // Modern palettes
            { primary: '#1e40af', secondary: '#3b82f6', accent: '#8b5cf6', background: '#f8fafc', text: '#1e293b' },
            { primary: '#059669', secondary: '#10b981', accent: '#34d399', background: '#f0fdf4', text: '#064e3b' },
            { primary: '#dc2626', secondary: '#ef4444', accent: '#f87171', background: '#fef2f2', text: '#7f1d1d' },

            // Creative palettes
            { primary: '#7c2d12', secondary: '#ea580c', accent: '#fb923c', background: '#fff7ed', text: '#9a3412' },
            { primary: '#581c87', secondary: '#8b5cf6', accent: '#a78bfa', background: '#faf5ff', text: '#4c1d95' },
            { primary: '#1e293b', secondary: '#475569', accent: '#64748b', background: '#f8fafc', text: '#0f172a' },

            // Luxury palettes
            { primary: '#0f172a', secondary: '#1e293b', accent: '#334155', background: '#f1f5f9', text: '#020617' },
            { primary: '#7f1d1d', secondary: '#dc2626', accent: '#f87171', background: '#fef2f2', text: '#450a0a' },
            { primary: '#365314', secondary: '#65a30d', accent: '#a3e635', background: '#f7fee7', text: '#1a2e05' },

            // Artistic palettes
            { primary: '#be185d', secondary: '#ec4899', accent: '#f9a8d4', background: '#fdf2f8', text: '#831843' },
            { primary: '#1e3a8a', secondary: '#3b82f6', accent: '#93c5fd', background: '#eff6ff', text: '#1e40af' },
            { primary: '#92400e', secondary: '#d97706', accent: '#fbbf24', background: '#fffbeb', text: '#78350f' }
        ];
    }

    getRandomStyle() {
        const styles = this.getTemplateStyles();
        return styles[Math.floor(Math.random() * styles.length)];
    }

    getRandomColorPalette() {
        return this.colorPalettes[Math.floor(Math.random() * this.colorPalettes.length)];
    }

    getRandomLayout() {
        const layouts = ['single-column', 'two-column', 'sidebar-left', 'sidebar-right', 'header-focus', 'creative-flow'];
        return layouts[Math.floor(Math.random() * layouts.length)];
    }

    getRandomDecorativeElements() {
        const elements = ['geometric', 'organic', 'minimal', 'ornate', 'modern', 'artistic'];
        return elements[Math.floor(Math.random() * elements.length)];
    }

    generateTemplateName(category, style, index) {
        const styleNames = {
            'glass-morphism': 'زجاجي شفاف',
            'neo-brutalism': 'جريء عصري',
            'gradient-mesh': 'تدرج شبكي',
            'organic-shapes': 'أشكال عضوية',
            'geometric-art': 'فن هندسي',
            'watercolor': 'ألوان مائية',
            'neon-cyber': 'نيون سايبر',
            'crystal-clear': 'كريستال صافي'
        };

        const categoryNames = {
            modern: 'عصري',
            creative: 'إبداعي',
            executive: 'تنفيذي',
            academic: 'أكاديمي',
            technical: 'تقني',
            medical: 'طبي',
            business: 'تجاري',
            legal: 'قانوني',
            artistic: 'فني',
            minimalist: 'مينيمال',
            luxury: 'فاخر',
            corporate: 'مؤسسي',
            startup: 'ناشئ',
            freelancer: 'مستقل',
            designer: 'مصمم'
        };

        const styleName = styleNames[style] || 'متميز';
        const categoryName = categoryNames[category] || category;

        return `${categoryName} ${styleName} ${index}`;
    }

    generateTemplateDescription(category, style) {
        const descriptions = {
            'glass-morphism': 'تصميم زجاجي شفاف مع تأثيرات بصرية متقدمة',
            'neo-brutalism': 'تصميم جريء وقوي مع خطوط واضحة وألوان صارخة',
            'gradient-mesh': 'تدرجات لونية متداخلة تخلق عمقاً بصرياً رائعاً',
            'organic-shapes': 'أشكال طبيعية متدفقة تضفي حيوية على التصميم',
            'geometric-art': 'فن هندسي معاصر مع أشكال هندسية متوازنة',
            'watercolor': 'تأثيرات الألوان المائية الناعمة والفنية',
            'neon-cyber': 'تصميم مستقبلي بألوان نيون وتأثيرات إلكترونية',
            'crystal-clear': 'تصميم كريستالي شفاف مع انعكاسات ضوئية'
        };

        return descriptions[style] || 'تصميم احترافي متميز يجمع بين الجمال والوظائف العملية';
    }

    generateTemplateTags(category, style) {
        const baseTags = ['احترافي', 'جميل', 'حديث', 'متميز'];
        const styleTags = {
            'glass-morphism': ['شفاف', 'زجاجي', 'متقدم'],
            'neo-brutalism': ['جريء', 'قوي', 'واضح'],
            'gradient-mesh': ['متدرج', 'ملون', 'عميق'],
            'organic-shapes': ['طبيعي', 'متدفق', 'حيوي'],
            'geometric-art': ['هندسي', 'متوازن', 'فني'],
            'watercolor': ['مائي', 'ناعم', 'فني'],
            'neon-cyber': ['مستقبلي', 'نيون', 'تقني'],
            'crystal-clear': ['كريستالي', 'شفاف', 'أنيق']
        };

        return [...baseTags, ...(styleTags[style] || [])];
    }

    generateTemplateFeatures(style, layout) {
        const baseFeatures = ['ATS متوافق', 'متعدد الصفحات', 'دعم الصور', 'قابل للتخصيص'];
        const styleFeatures = {
            'glass-morphism': ['تأثيرات زجاجية', 'شفافية متقدمة'],
            'neo-brutalism': ['تصميم جريء', 'ألوان قوية'],
            'gradient-mesh': ['تدرجات معقدة', 'عمق بصري'],
            'organic-shapes': ['أشكال طبيعية', 'تدفق سلس'],
            'geometric-art': ['أشكال هندسية', 'توازن مثالي'],
            'watercolor': ['تأثيرات مائية', 'نعومة فنية'],
            'neon-cyber': ['إضاءة نيون', 'مظهر مستقبلي'],
            'crystal-clear': ['وضوح كريستالي', 'انعكاسات ضوئية']
        };

        return [...baseFeatures, ...(styleFeatures[style] || [])];
    }

    setupTemplatePreloader() {
        // Pre-load template images for better performance
        this.templates.forEach((template, index) => {
            if (index < 50) { // Pre-load first 50 templates
                const img = new Image();
                img.src = template.preview;
            }
        });
    }

    getTemplateById(id) {
        return this.templates.find(template => template.id === id);
    }

    getTemplatesByCategory(category) {
        return this.templates.filter(template => template.category === category);
    }

    searchTemplates(query) {
        const searchTerm = query.toLowerCase();
        return this.templates.filter(template =>
            template.name.toLowerCase().includes(searchTerm) ||
            template.description.toLowerCase().includes(searchTerm) ||
            template.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
    }
}

// Initialize beautiful templates generator
const beautifulTemplatesGenerator = new BeautifulTemplatesGenerator();

// Export for global access
window.beautifulTemplatesGenerator = beautifulTemplatesGenerator;
