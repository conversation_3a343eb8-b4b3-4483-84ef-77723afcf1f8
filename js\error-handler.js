// ELASHRAFY CV - Error Handler & Performance Optimizer
// معالج الأخطاء ومحسن الأداء - الأشرافي للسيرة الذاتية

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.performanceMetrics = {};
        this.isDebugMode = false;
        
        this.init();
    }
    
    init() {
        this.setupGlobalErrorHandling();
        this.setupPerformanceMonitoring();
        this.setupConsoleOverride();
        this.checkBrowserCompatibility();
    }
    
    setupGlobalErrorHandling() {
        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error?.stack,
                timestamp: new Date().toISOString()
            });
        });
        
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason?.message || event.reason,
                stack: event.reason?.stack,
                timestamp: new Date().toISOString()
            });
        });
        
        // Handle resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError({
                    type: 'Resource Loading Error',
                    message: `Failed to load: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);
    }
    
    setupPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                this.performanceMetrics.pageLoad = {
                    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                    loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                    totalTime: perfData.loadEventEnd - perfData.fetchStart,
                    timestamp: new Date().toISOString()
                };
                
                this.logPerformance('Page Load', this.performanceMetrics.pageLoad);
            }, 0);
        });
        
        // Monitor long tasks
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.duration > 50) { // Tasks longer than 50ms
                            this.logPerformance('Long Task', {
                                duration: entry.duration,
                                startTime: entry.startTime,
                                timestamp: new Date().toISOString()
                            });
                        }
                    }
                });
                observer.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                console.warn('Performance Observer not supported for longtask');
            }
        }
    }
    
    setupConsoleOverride() {
        // Override console methods to capture logs
        const originalConsole = { ...console };
        
        ['log', 'warn', 'error', 'info'].forEach(method => {
            console[method] = (...args) => {
                // Call original method
                originalConsole[method](...args);
                
                // Log to our system
                if (method === 'error') {
                    this.logError({
                        type: 'Console Error',
                        message: args.join(' '),
                        timestamp: new Date().toISOString()
                    });
                }
            };
        });
    }
    
    checkBrowserCompatibility() {
        const requiredFeatures = [
            'fetch',
            'Promise',
            'localStorage',
            'sessionStorage',
            'addEventListener'
        ];
        
        const missingFeatures = requiredFeatures.filter(feature => !(feature in window));
        
        if (missingFeatures.length > 0) {
            this.logError({
                type: 'Browser Compatibility',
                message: `Missing features: ${missingFeatures.join(', ')}`,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
            
            this.showCompatibilityWarning(missingFeatures);
        }
    }
    
    logError(error) {
        this.errors.push(error);
        
        // Keep only last 100 errors
        if (this.errors.length > 100) {
            this.errors = this.errors.slice(-100);
        }
        
        // Store in localStorage for debugging
        try {
            localStorage.setItem('elashrafy-errors', JSON.stringify(this.errors.slice(-10)));
        } catch (e) {
            // localStorage might be full
        }
        
        // Send to console in debug mode
        if (this.isDebugMode) {
            console.error('ELASHRAFY CV Error:', error);
        }
        
        // Show user-friendly error message for critical errors
        if (this.isCriticalError(error)) {
            this.showUserError(error);
        }
    }
    
    logPerformance(type, data) {
        if (this.isDebugMode) {
            console.log(`ELASHRAFY CV Performance - ${type}:`, data);
        }
        
        // Store performance data
        if (!this.performanceMetrics[type]) {
            this.performanceMetrics[type] = [];
        }
        this.performanceMetrics[type].push(data);
    }
    
    isCriticalError(error) {
        const criticalPatterns = [
            'Script error',
            'Network error',
            'Failed to fetch',
            'TypeError',
            'ReferenceError'
        ];
        
        return criticalPatterns.some(pattern => 
            error.message && error.message.includes(pattern)
        );
    }
    
    showUserError(error) {
        // Create user-friendly error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="error-content">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="error-message">
                    <h4>حدث خطأ غير متوقع</h4>
                    <p>نعتذر عن هذا الإزعاج. يرجى إعادة تحميل الصفحة أو المحاولة مرة أخرى.</p>
                </div>
                <div class="error-actions">
                    <button onclick="location.reload()" class="error-btn primary">
                        <i class="fas fa-redo"></i>
                        إعادة تحميل
                    </button>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" class="error-btn secondary">
                        <i class="fas fa-times"></i>
                        إغلاق
                    </button>
                </div>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fee2e2;
            border: 1px solid #fecaca;
            border-radius: 12px;
            padding: 20px;
            max-width: 400px;
            z-index: 10000;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in forwards';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 10000);
    }
    
    showCompatibilityWarning(missingFeatures) {
        const warning = document.createElement('div');
        warning.className = 'compatibility-warning';
        warning.innerHTML = `
            <div class="warning-content">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="warning-message">
                    <h4>متصفح غير مدعوم بالكامل</h4>
                    <p>بعض الميزات قد لا تعمل بشكل صحيح في هذا المتصفح.</p>
                    <p>يُنصح بتحديث المتصفح أو استخدام Chrome أو Firefox الحديث.</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="warning-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        warning.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #fef3c7;
            border-bottom: 2px solid #f59e0b;
            padding: 15px;
            z-index: 10001;
            text-align: center;
        `;
        
        document.body.insertBefore(warning, document.body.firstChild);
    }
    
    // Recovery methods
    recoverFromError(errorType) {
        switch (errorType) {
            case 'template-load-error':
                this.recoverTemplateLoading();
                break;
            case 'pdf-export-error':
                this.recoverPDFExport();
                break;
            case 'photo-upload-error':
                this.recoverPhotoUpload();
                break;
            default:
                this.genericRecovery();
        }
    }
    
    recoverTemplateLoading() {
        console.log('🔄 Attempting to recover template loading...');
        
        // Clear template cache
        if (window.templateManager) {
            window.templateManager.templates = [];
            window.templateManager.loadTemplates();
        }
    }
    
    recoverPDFExport() {
        console.log('🔄 Attempting to recover PDF export...');
        
        // Clear PDF cache and retry
        if (window.pdfExporter) {
            window.pdfExporter.clearCache();
        }
    }
    
    recoverPhotoUpload() {
        console.log('🔄 Attempting to recover photo upload...');
        
        // Reset photo manager
        if (window.photoManager) {
            window.photoManager.reset();
        }
    }
    
    genericRecovery() {
        console.log('🔄 Attempting generic recovery...');
        
        // Clear caches
        this.clearCaches();
        
        // Reinitialize core components
        this.reinitializeComponents();
    }
    
    clearCaches() {
        try {
            // Clear localStorage items that might be corrupted
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('elashrafy-')) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => {
                try {
                    localStorage.removeItem(key);
                } catch (e) {
                    console.warn(`Failed to remove ${key} from localStorage`);
                }
            });
        } catch (e) {
            console.warn('Failed to clear localStorage');
        }
    }
    
    reinitializeComponents() {
        // Reinitialize main components
        setTimeout(() => {
            if (window.cvApp && typeof window.cvApp.init === 'function') {
                window.cvApp.init();
            }
        }, 1000);
    }
    
    // Debug methods
    enableDebugMode() {
        this.isDebugMode = true;
        console.log('🐛 ELASHRAFY CV Debug mode enabled');
    }
    
    disableDebugMode() {
        this.isDebugMode = false;
        console.log('🐛 ELASHRAFY CV Debug mode disabled');
    }
    
    getErrorReport() {
        return {
            errors: this.errors,
            performance: this.performanceMetrics,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            url: window.location.href
        };
    }
    
    exportErrorReport() {
        const report = this.getErrorReport();
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `elashrafy-cv-error-report-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// Initialize error handler
const errorHandler = new ErrorHandler();

// Export for global access
window.errorHandler = errorHandler;

// Add CSS for error notifications
const errorStyles = document.createElement('style');
errorStyles.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .error-notification .error-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
    }
    
    .error-notification .error-icon {
        color: #dc2626;
        font-size: 20px;
        margin-top: 2px;
    }
    
    .error-notification .error-message h4 {
        margin: 0 0 8px 0;
        color: #7f1d1d;
        font-size: 16px;
    }
    
    .error-notification .error-message p {
        margin: 0;
        color: #991b1b;
        font-size: 14px;
        line-height: 1.4;
    }
    
    .error-notification .error-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
    }
    
    .error-btn {
        padding: 6px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
    }
    
    .error-btn.primary {
        background: #dc2626;
        color: white;
    }
    
    .error-btn.primary:hover {
        background: #b91c1c;
    }
    
    .error-btn.secondary {
        background: #f3f4f6;
        color: #6b7280;
    }
    
    .error-btn.secondary:hover {
        background: #e5e7eb;
    }
    
    .compatibility-warning .warning-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
    }
    
    .compatibility-warning .warning-icon {
        color: #d97706;
        font-size: 20px;
    }
    
    .compatibility-warning .warning-message h4 {
        margin: 0 0 4px 0;
        color: #92400e;
        font-size: 16px;
    }
    
    .compatibility-warning .warning-message p {
        margin: 0;
        color: #a16207;
        font-size: 14px;
    }
    
    .compatibility-warning .warning-close {
        position: absolute;
        right: 15px;
        background: none;
        border: none;
        color: #a16207;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
    }
    
    .compatibility-warning .warning-close:hover {
        background: rgba(0, 0, 0, 0.1);
    }
`;

document.head.appendChild(errorStyles);
