// ELASHRAFY CV - Automatic Template Addition System
// نظام إضافة القوالب التلقائي - الأشرافي للسيرة الذاتية

class AutoTemplateSystem {
    constructor() {
        this.templateQueue = [];
        this.isProcessing = false;
        this.additionInterval = 5000; // 5 seconds between additions
        this.maxTemplatesPerBatch = 10;
        this.currentBatch = 0;
        
        this.templateDesigns = {
            modern: this.getModernDesigns(),
            creative: this.getCreativeDesigns(),
            executive: this.getExecutiveDesigns(),
            academic: this.getAcademicDesigns(),
            technical: this.getTechnicalDesigns(),
            medical: this.getMedicalDesigns(),
            business: this.getBusinessDesigns(),
            legal: this.getLegalDesigns()
        };
        
        this.init();
    }
    
    init() {
        this.setupAutoAddition();
        this.createTemplateGenerationUI();
        this.startAutoGeneration();
    }
    
    setupAutoAddition() {
        // Start automatic template addition
        setInterval(() => {
            if (!this.isProcessing && this.templateQueue.length > 0) {
                this.processTemplateQueue();
            }
        }, this.additionInterval);
        
        // Generate new templates periodically
        setInterval(() => {
            this.generateNewTemplates();
        }, 30000); // Every 30 seconds
    }
    
    createTemplateGenerationUI() {
        const generationPanel = document.createElement('div');
        generationPanel.className = 'template-generation-panel';
        generationPanel.innerHTML = `
            <div class="generation-header">
                <h3>
                    <i class="fas fa-magic"></i>
                    <span data-translate="auto_template_generation">إنشاء القوالب التلقائي</span>
                </h3>
                <div class="generation-status">
                    <span class="status-indicator" id="generationStatus">نشط</span>
                    <span class="template-count" id="templateCount">400+</span>
                </div>
            </div>
            
            <div class="generation-controls">
                <button class="generation-btn" id="generateNowBtn">
                    <i class="fas fa-plus-circle"></i>
                    <span data-translate="generate_now">إنشاء الآن</span>
                </button>
                
                <button class="generation-btn secondary" id="pauseGenerationBtn">
                    <i class="fas fa-pause"></i>
                    <span data-translate="pause_generation">إيقاف مؤقت</span>
                </button>
                
                <button class="generation-btn tertiary" id="clearTemplatesBtn">
                    <i class="fas fa-trash"></i>
                    <span data-translate="clear_templates">مسح الكل</span>
                </button>
            </div>
            
            <div class="generation-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="generationProgress"></div>
                </div>
                <div class="progress-text">
                    <span id="progressText">جاري إنشاء القوالب...</span>
                </div>
            </div>
            
            <div class="recent-templates" id="recentTemplates">
                <h4 data-translate="recently_added">المضاف حديثاً</h4>
                <div class="recent-list" id="recentList">
                    <!-- Recent templates will be added here -->
                </div>
            </div>
        `;
        
        // Add to template gallery modal
        const templateModal = document.getElementById('templateGalleryModal');
        if (templateModal) {
            const modalBody = templateModal.querySelector('.modal-body');
            modalBody.insertBefore(generationPanel, modalBody.firstChild);
        }
        
        this.setupGenerationControls();
    }
    
    setupGenerationControls() {
        document.getElementById('generateNowBtn').addEventListener('click', () => {
            this.generateTemplatesBatch();
        });
        
        document.getElementById('pauseGenerationBtn').addEventListener('click', () => {
            this.toggleGeneration();
        });
        
        document.getElementById('clearTemplatesBtn').addEventListener('click', () => {
            this.clearAllTemplates();
        });
    }
    
    async generateTemplatesBatch() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        this.updateGenerationStatus('جاري الإنشاء...', 'processing');
        
        const categories = Object.keys(this.templateDesigns);
        const selectedCategory = categories[Math.floor(Math.random() * categories.length)];
        
        for (let i = 0; i < this.maxTemplatesPerBatch; i++) {
            const template = await this.generateUniqueTemplate(selectedCategory);
            this.templateQueue.push(template);
            
            // Update progress
            const progress = ((i + 1) / this.maxTemplatesPerBatch) * 100;
            this.updateProgress(progress);
            
            // Add delay for smooth animation
            await this.delay(200);
        }
        
        this.isProcessing = false;
        this.updateGenerationStatus('مكتمل', 'completed');
        
        // Process the queue
        this.processTemplateQueue();
    }
    
    async generateUniqueTemplate(category) {
        const designs = this.templateDesigns[category];
        const design = designs[Math.floor(Math.random() * designs.length)];
        
        const templateId = this.generateTemplateId(category);
        const colors = this.generateRandomColors();
        const layout = this.getRandomLayout();
        
        return {
            id: templateId,
            name: this.generateTemplateName(category, design.style),
            category: category,
            description: this.generateTemplateDescription(category, design.style),
            preview: this.generateAdvancedPreview(design, colors, layout),
            isPremium: Math.random() > 0.7, // 30% premium
            rating: (4.0 + Math.random() * 1.0).toFixed(1),
            downloads: Math.floor(Math.random() * 5000) + 500,
            tags: this.generateTemplateTags(category, design.style),
            colors: colors,
            layout: layout,
            design: design,
            atsCompatible: true,
            multiPage: Math.random() > 0.6,
            photoSupport: true,
            customizable: true,
            createdAt: new Date().toISOString()
        };
    }
    
    generateAdvancedPreview(design, colors, layout) {
        const [primary, secondary, accent] = colors;
        
        return `data:image/svg+xml;base64,${btoa(this.createAdvancedSVG(design, primary, secondary, accent, layout))}`;
    }
    
    createAdvancedSVG(design, primary, secondary, accent, layout) {
        const svgTemplates = {
            geometric: this.createGeometricTemplate(primary, secondary, accent),
            elegant: this.createElegantTemplate(primary, secondary, accent),
            bold: this.createBoldTemplate(primary, secondary, accent),
            minimal: this.createMinimalTemplate(primary, secondary, accent),
            artistic: this.createArtisticTemplate(primary, secondary, accent),
            professional: this.createProfessionalTemplate(primary, secondary, accent),
            modern: this.createModernTemplate(primary, secondary, accent),
            luxury: this.createLuxuryTemplate(primary, secondary, accent)
        };
        
        return svgTemplates[design.style] || svgTemplates.modern;
    }
    
    createGeometricTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="hexPattern" patternUnits="userSpaceOnUse" width="30" height="26">
                    <polygon points="15,2 25,8 25,20 15,26 5,20 5,8" fill="${accent}" opacity="0.1"/>
                </pattern>
                <linearGradient id="geoGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:1" />
                </linearGradient>
            </defs>
            
            <rect width="280" height="200" fill="white"/>
            <rect width="280" height="200" fill="url(#hexPattern)"/>
            
            <!-- Geometric Header -->
            <polygon points="0,0 280,0 260,60 0,60" fill="url(#geoGrad)"/>
            
            <!-- Geometric Photo Frame -->
            <polygon points="40,20 70,15 75,40 45,45" fill="white" opacity="0.95"/>
            <polygon points="45,22 67,18 70,38 48,42" fill="${accent}" opacity="0.7"/>
            
            <!-- Geometric Text Areas -->
            <rect x="90" y="25" width="120" height="8" fill="white" opacity="0.95" rx="4"/>
            <rect x="90" y="37" width="80" height="6" fill="white" opacity="0.8" rx="3"/>
            
            <!-- Diamond Decorations -->
            <polygon points="240,25 250,15 260,25 250,35" fill="white" opacity="0.8"/>
            
            <!-- Content Sections -->
            <rect x="20" y="80" width="240" height="2" fill="${primary}"/>
            <polygon points="20,90 80,85 85,95 25,100" fill="${primary}" opacity="0.8"/>
            
            <rect x="20" y="110" width="200" height="4" fill="${secondary}" opacity="0.6" rx="2"/>
            <rect x="30" y="118" width="180" height="4" fill="${secondary}" opacity="0.5" rx="2"/>
            <rect x="20" y="126" width="190" height="4" fill="${secondary}" opacity="0.4" rx="2"/>
            
            <!-- Geometric Skills -->
            <polygon points="20,150 35,145 40,155 25,160" fill="${accent}" opacity="0.8"/>
            <polygon points="50,150 65,145 70,155 55,160" fill="${accent}" opacity="0.7"/>
            <polygon points="80,150 95,145 100,155 85,160" fill="${accent}" opacity="0.6"/>
            
            <!-- Geometric Footer -->
            <polygon points="0,180 280,175 280,200 0,200" fill="${secondary}" opacity="0.2"/>
        </svg>`;
    }
    
    createElegantTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="elegantGrad" cx="50%" cy="30%" r="70%">
                    <stop offset="0%" style="stop-color:${accent};stop-opacity:0.3" />
                    <stop offset="100%" style="stop-color:${primary};stop-opacity:0.1" />
                </radialGradient>
                <filter id="elegantShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="${primary}" flood-opacity="0.2"/>
                </filter>
            </defs>
            
            <rect width="280" height="200" fill="url(#elegantGrad)"/>
            
            <!-- Elegant Header -->
            <rect x="0" y="0" width="280" height="70" fill="${primary}" opacity="0.05"/>
            <rect x="20" y="60" width="240" height="1" fill="${primary}" opacity="0.3"/>
            <rect x="20" y="62" width="240" height="1" fill="${accent}" opacity="0.5"/>
            
            <!-- Elegant Photo -->
            <circle cx="60" cy="35" r="20" fill="white" filter="url(#elegantShadow)"/>
            <circle cx="60" cy="35" r="16" fill="${secondary}" opacity="0.2"/>
            <circle cx="60" cy="35" r="12" fill="${accent}" opacity="0.3"/>
            
            <!-- Elegant Typography -->
            <rect x="90" y="25" width="140" height="10" fill="${primary}" opacity="0.9" rx="5"/>
            <rect x="90" y="40" width="100" height="6" fill="${secondary}" opacity="0.7" rx="3"/>
            
            <!-- Decorative Elements -->
            <circle cx="250" cy="30" r="3" fill="${accent}" opacity="0.6"/>
            <circle cx="260" cy="25" r="2" fill="${accent}" opacity="0.5"/>
            <circle cx="255" cy="40" r="2" fill="${accent}" opacity="0.4"/>
            
            <!-- Elegant Content -->
            <rect x="30" y="90" width="220" height="1" fill="${primary}" opacity="0.4"/>
            <rect x="30" y="100" width="80" height="8" fill="${primary}" opacity="0.8" rx="4"/>
            
            <rect x="30" y="120" width="200" height="3" fill="${secondary}" opacity="0.6" rx="1"/>
            <rect x="30" y="127" width="180" height="3" fill="${secondary}" opacity="0.5" rx="1"/>
            <rect x="30" y="134" width="190" height="3" fill="${secondary}" opacity="0.4" rx="1"/>
            
            <!-- Elegant Skills -->
            <rect x="30" y="155" width="220" height="1" fill="${accent}" opacity="0.4"/>
            <rect x="30" y="165" width="60" height="6" fill="${accent}" opacity="0.8" rx="3"/>
            <rect x="100" y="165" width="50" height="6" fill="${accent}" opacity="0.7" rx="3"/>
            <rect x="160" y="165" width="55" height="6" fill="${accent}" opacity="0.6" rx="3"/>
            
            <!-- Elegant Border -->
            <rect x="10" y="10" width="260" height="180" fill="none" stroke="${primary}" stroke-width="1" opacity="0.2" rx="8"/>
        </svg>`;
    }
    
    createBoldTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="boldGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:1" />
                    <stop offset="50%" style="stop-color:${accent};stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:1" />
                </linearGradient>
            </defs>
            
            <rect width="280" height="200" fill="white"/>
            
            <!-- Bold Header -->
            <rect x="0" y="0" width="280" height="80" fill="url(#boldGrad)"/>
            
            <!-- Bold Photo -->
            <rect x="30" y="20" width="50" height="50" fill="white" rx="8"/>
            <rect x="35" y="25" width="40" height="40" fill="${accent}" opacity="0.3" rx="6"/>
            
            <!-- Bold Typography -->
            <rect x="90" y="25" width="150" height="15" fill="white" rx="2"/>
            <rect x="90" y="45" width="100" height="10" fill="white" opacity="0.9" rx="2"/>
            
            <!-- Bold Accent -->
            <rect x="250" y="20" width="8" height="50" fill="white" opacity="0.8" rx="4"/>
            
            <!-- Bold Content Blocks -->
            <rect x="20" y="100" width="240" height="5" fill="${primary}" rx="2"/>
            <rect x="20" y="115" width="100" height="12" fill="${primary}" rx="2"/>
            
            <rect x="20" y="140" width="220" height="6" fill="${secondary}" rx="3"/>
            <rect x="20" y="150" width="200" height="6" fill="${secondary}" opacity="0.8" rx="3"/>
            <rect x="20" y="160" width="180" height="6" fill="${secondary}" opacity="0.6" rx="3"/>
            
            <!-- Bold Skills Bars -->
            <rect x="20" y="180" width="80" height="8" fill="${accent}" rx="4"/>
            <rect x="110" y="180" width="70" height="8" fill="${accent}" opacity="0.8" rx="4"/>
            <rect x="190" y="180" width="60" height="8" fill="${accent}" opacity="0.6" rx="4"/>
        </svg>`;
    }
    
    createArtisticTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="artisticGrad" cx="30%" cy="30%" r="80%">
                    <stop offset="0%" style="stop-color:${accent};stop-opacity:0.8" />
                    <stop offset="50%" style="stop-color:${primary};stop-opacity:0.4" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:0.2" />
                </radialGradient>
                <filter id="artisticBlur" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="1"/>
                </filter>
            </defs>
            
            <rect width="280" height="200" fill="url(#artisticGrad)"/>
            
            <!-- Artistic Shapes -->
            <circle cx="50" cy="50" r="30" fill="${primary}" opacity="0.3" filter="url(#artisticBlur)"/>
            <circle cx="230" cy="150" r="40" fill="${accent}" opacity="0.2" filter="url(#artisticBlur)"/>
            
            <!-- Flowing Header -->
            <path d="M0,0 Q140,60 280,20 L280,80 Q140,40 0,80 Z" fill="${primary}" opacity="0.8"/>
            
            <!-- Artistic Photo -->
            <circle cx="60" cy="40" r="18" fill="white" opacity="0.9"/>
            <circle cx="60" cy="40" r="14" fill="${accent}" opacity="0.6"/>
            
            <!-- Flowing Text -->
            <path d="M100,30 Q180,25 220,35" stroke="white" stroke-width="8" fill="none" opacity="0.9"/>
            <path d="M100,45 Q160,40 200,50" stroke="white" stroke-width="6" fill="none" opacity="0.7"/>
            
            <!-- Artistic Content -->
            <path d="M20,100 Q140,95 260,105" stroke="${primary}" stroke-width="3" fill="none"/>
            <rect x="20" y="115" width="80" height="8" fill="${primary}" opacity="0.8" rx="4"/>
            
            <!-- Flowing Sections -->
            <path d="M20,140 Q120,135 220,145" stroke="${secondary}" stroke-width="4" fill="none" opacity="0.6"/>
            <path d="M30,155 Q130,150 230,160" stroke="${secondary}" stroke-width="3" fill="none" opacity="0.5"/>
            <path d="M25,170 Q125,165 225,175" stroke="${secondary}" stroke-width="3" fill="none" opacity="0.4"/>
            
            <!-- Artistic Elements -->
            <circle cx="240" cy="120" r="5" fill="${accent}" opacity="0.7"/>
            <circle cx="250" cy="110" r="3" fill="${accent}" opacity="0.6"/>
            <circle cx="235" cy="105" r="4" fill="${accent}" opacity="0.5"/>
        </svg>`;
    }
    
    createLuxuryTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="luxuryGrad" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:${primary};stop-opacity:1" />
                </linearGradient>
                <linearGradient id="goldGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#ffed4e;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ffd700;stop-opacity:1" />
                </linearGradient>
            </defs>
            
            <rect width="280" height="200" fill="url(#luxuryGrad)"/>
            
            <!-- Luxury Border -->
            <rect x="5" y="5" width="270" height="190" fill="none" stroke="url(#goldGrad)" stroke-width="2" rx="8"/>
            <rect x="10" y="10" width="260" height="180" fill="none" stroke="url(#goldGrad)" stroke-width="1" opacity="0.5" rx="6"/>
            
            <!-- Luxury Header -->
            <rect x="15" y="15" width="250" height="60" fill="rgba(255,215,0,0.1)" rx="4"/>
            
            <!-- Luxury Photo -->
            <rect x="30" y="25" width="40" height="40" fill="url(#goldGrad)" rx="4"/>
            <rect x="33" y="28" width="34" height="34" fill="white" opacity="0.9" rx="2"/>
            <rect x="36" y="31" width="28" height="28" fill="${accent}" opacity="0.3" rx="2"/>
            
            <!-- Luxury Typography -->
            <rect x="80" y="30" width="140" height="10" fill="url(#goldGrad)" rx="2"/>
            <rect x="80" y="45" width="100" height="6" fill="white" opacity="0.9" rx="2"/>
            <rect x="80" y="55" width="120" height="4" fill="url(#goldGrad)" opacity="0.7" rx="2"/>
            
            <!-- Luxury Decorations -->
            <polygon points="240,30 250,25 260,30 255,40 245,40" fill="url(#goldGrad)" opacity="0.8"/>
            
            <!-- Luxury Content -->
            <rect x="20" y="90" width="240" height="2" fill="url(#goldGrad)"/>
            <rect x="20" y="100" width="90" height="8" fill="white" opacity="0.9" rx="2"/>
            
            <rect x="20" y="120" width="220" height="4" fill="white" opacity="0.7" rx="2"/>
            <rect x="20" y="128" width="200" height="4" fill="white" opacity="0.6" rx="2"/>
            <rect x="20" y="136" width="180" height="4" fill="white" opacity="0.5" rx="2"/>
            
            <!-- Luxury Skills -->
            <rect x="20" y="155" width="240" height="1" fill="url(#goldGrad)"/>
            <rect x="20" y="165" width="60" height="6" fill="url(#goldGrad)" opacity="0.8" rx="3"/>
            <rect x="90" y="165" width="50" height="6" fill="url(#goldGrad)" opacity="0.7" rx="3"/>
            <rect x="150" y="165" width="55" height="6" fill="url(#goldGrad)" opacity="0.6" rx="3"/>
            
            <!-- Luxury Footer -->
            <rect x="15" y="180" width="250" height="1" fill="url(#goldGrad)" opacity="0.5"/>
        </svg>`;
    }
    
    async processTemplateQueue() {
        if (this.templateQueue.length === 0) return;

        const template = this.templateQueue.shift();

        // Add to template manager
        if (window.templateManager) {
            templateManager.templates.push(template);

            // Update template grid if visible
            if (document.getElementById('templateGrid')) {
                this.addTemplateToGrid(template);
            }

            // Update recent templates list
            this.addToRecentList(template);

            // Update template count
            this.updateTemplateCount();

            // Show notification
            this.showTemplateAddedNotification(template);
        }
    }

    addTemplateToGrid(template) {
        const grid = document.getElementById('templateGrid');
        if (!grid) return;

        const templateCard = document.createElement('div');
        templateCard.className = 'template-card new-template';
        templateCard.dataset.templateId = template.id;
        templateCard.innerHTML = window.templateManager.createTemplateCard(template);

        // Add with animation
        templateCard.style.opacity = '0';
        templateCard.style.transform = 'scale(0.8) translateY(20px)';

        grid.insertBefore(templateCard, grid.firstChild);

        // Animate in
        setTimeout(() => {
            templateCard.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
            templateCard.style.opacity = '1';
            templateCard.style.transform = 'scale(1) translateY(0)';
        }, 100);

        // Remove new-template class after animation
        setTimeout(() => {
            templateCard.classList.remove('new-template');
        }, 1000);

        // Add click event
        templateCard.addEventListener('click', () => {
            templateManager.selectTemplate(template.id);
        });
    }

    addToRecentList(template) {
        const recentList = document.getElementById('recentList');
        if (!recentList) return;

        const recentItem = document.createElement('div');
        recentItem.className = 'recent-item';
        recentItem.innerHTML = `
            <div class="recent-preview">
                <img src="${template.preview}" alt="${template.name}">
            </div>
            <div class="recent-info">
                <div class="recent-name">${template.name}</div>
                <div class="recent-category">${this.getCategoryName(template.category)}</div>
                <div class="recent-time">الآن</div>
            </div>
            <div class="recent-actions">
                <button class="recent-btn" onclick="templateManager.selectTemplate('${template.id}')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        `;

        recentList.insertBefore(recentItem, recentList.firstChild);

        // Keep only last 5 items
        while (recentList.children.length > 5) {
            recentList.removeChild(recentList.lastChild);
        }
    }

    updateTemplateCount() {
        const countElement = document.getElementById('templateCount');
        if (countElement && window.templateManager) {
            const count = templateManager.templates.length;
            countElement.textContent = `${count}+`;
        }
    }

    showTemplateAddedNotification(template) {
        const notification = document.createElement('div');
        notification.className = 'template-notification';
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-plus-circle"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">قالب جديد مضاف!</div>
                <div class="notification-text">${template.name}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        notification.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 10001;
            max-width: 350px;
            animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        `;

        document.body.appendChild(notification);

        // Auto remove after 4 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutDown 0.3s ease-in forwards';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 4000);
    }

    generateNewTemplates() {
        if (this.templateQueue.length < 50) { // Keep queue filled
            const categories = Object.keys(this.templateDesigns);
            const randomCategory = categories[Math.floor(Math.random() * categories.length)];

            // Generate 5 new templates
            for (let i = 0; i < 5; i++) {
                setTimeout(async () => {
                    const template = await this.generateUniqueTemplate(randomCategory);
                    this.templateQueue.push(template);
                }, i * 1000);
            }
        }
    }

    generateTemplateId(category) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 6);
        return `${category}_${timestamp}_${random}`;
    }

    generateTemplateName(category, style) {
        const names = {
            modern: ['عصري متقدم', 'حديث أنيق', 'معاصر راقي', 'مودرن فاخر'],
            creative: ['إبداعي مميز', 'فني راقي', 'خلاق متطور', 'ابتكاري جذاب'],
            executive: ['تنفيذي فاخر', 'قيادي محترف', 'إداري راقي', 'تنفيذي متميز'],
            academic: ['أكاديمي محترف', 'علمي متقدم', 'بحثي راقي', 'جامعي أنيق'],
            technical: ['تقني متطور', 'هندسي محترف', 'تكنولوجي حديث', 'فني متقدم'],
            medical: ['طبي محترف', 'صحي راقي', 'طبي متقدم', 'صحي أنيق'],
            business: ['تجاري محترف', 'أعمال راقي', 'مؤسسي متقدم', 'تجاري أنيق'],
            legal: ['قانوني محترف', 'حقوقي راقي', 'قضائي متقدم', 'قانوني أنيق']
        };

        const categoryNames = names[category] || names.modern;
        const baseName = categoryNames[Math.floor(Math.random() * categoryNames.length)];
        const number = Math.floor(Math.random() * 999) + 1;

        return `${baseName} ${number}`;
    }

    generateTemplateDescription(category, style) {
        const descriptions = {
            modern: [
                'تصميم عصري بخطوط نظيفة وألوان متناسقة',
                'قالب حديث مع تركيز على البساطة والأناقة',
                'تصميم معاصر يجمع بين الحداثة والاحترافية',
                'قالب مودرن بلمسات إبداعية متميزة'
            ],
            creative: [
                'تصميم إبداعي مع عناصر بصرية جذابة',
                'قالب فني يبرز الشخصية الإبداعية',
                'تصميم خلاق مع استخدام مبتكر للألوان',
                'قالب ابتكاري يجمع بين الفن والاحترافية'
            ],
            executive: [
                'تصميم تنفيذي فاخر للمناصب القيادية',
                'قالب إداري راقي يعكس الخبرة والكفاءة',
                'تصميم قيادي محترف للمدراء التنفيذيين',
                'قالب تنفيذي متميز للمناصب العليا'
            ]
        };

        const categoryDescriptions = descriptions[category] || descriptions.modern;
        return categoryDescriptions[Math.floor(Math.random() * categoryDescriptions.length)];
    }

    generateTemplateTags(category, style) {
        const baseTags = {
            modern: ['عصري', 'حديث', 'نظيف', 'أنيق'],
            creative: ['إبداعي', 'فني', 'ملون', 'مبتكر'],
            executive: ['تنفيذي', 'قيادي', 'فاخر', 'محترف'],
            academic: ['أكاديمي', 'علمي', 'بحثي', 'تعليمي'],
            technical: ['تقني', 'هندسي', 'تكنولوجي', 'متطور'],
            medical: ['طبي', 'صحي', 'طبي', 'علاجي'],
            business: ['تجاري', 'أعمال', 'مؤسسي', 'اقتصادي'],
            legal: ['قانوني', 'حقوقي', 'قضائي', 'تشريعي']
        };

        const styleTags = {
            geometric: ['هندسي', 'أشكال'],
            elegant: ['أنيق', 'راقي'],
            bold: ['جريء', 'قوي'],
            minimal: ['بسيط', 'مينيمال'],
            artistic: ['فني', 'إبداعي'],
            luxury: ['فاخر', 'راقي']
        };

        const tags = [...(baseTags[category] || baseTags.modern)];
        if (styleTags[style]) {
            tags.push(...styleTags[style]);
        }

        return tags;
    }

    generateRandomColors() {
        const colorSchemes = [
            ['#1e40af', '#3b82f6', '#8b5cf6'],
            ['#059669', '#10b981', '#34d399'],
            ['#dc2626', '#ef4444', '#f87171'],
            ['#7c2d12', '#ea580c', '#fb923c'],
            ['#581c87', '#8b5cf6', '#a78bfa'],
            ['#1e293b', '#475569', '#64748b'],
            ['#0f172a', '#1e293b', '#334155'],
            ['#7f1d1d', '#dc2626', '#f87171']
        ];

        return colorSchemes[Math.floor(Math.random() * colorSchemes.length)];
    }

    getRandomLayout() {
        const layouts = ['single-column', 'two-column', 'sidebar-left', 'sidebar-right', 'header-focus'];
        return layouts[Math.floor(Math.random() * layouts.length)];
    }

    getCategoryName(category) {
        const names = {
            modern: 'عصري',
            creative: 'إبداعي',
            executive: 'تنفيذي',
            academic: 'أكاديمي',
            technical: 'تقني',
            medical: 'طبي',
            business: 'تجاري',
            legal: 'قانوني'
        };

        return names[category] || category;
    }

    getModernDesigns() {
        return [
            { style: 'geometric', complexity: 'medium' },
            { style: 'minimal', complexity: 'low' },
            { style: 'elegant', complexity: 'high' },
            { style: 'professional', complexity: 'medium' }
        ];
    }

    getCreativeDesigns() {
        return [
            { style: 'artistic', complexity: 'high' },
            { style: 'bold', complexity: 'medium' },
            { style: 'geometric', complexity: 'high' },
            { style: 'elegant', complexity: 'medium' }
        ];
    }

    getExecutiveDesigns() {
        return [
            { style: 'luxury', complexity: 'high' },
            { style: 'professional', complexity: 'medium' },
            { style: 'elegant', complexity: 'high' },
            { style: 'minimal', complexity: 'low' }
        ];
    }

    getAcademicDesigns() {
        return [
            { style: 'professional', complexity: 'low' },
            { style: 'minimal', complexity: 'low' },
            { style: 'elegant', complexity: 'medium' },
            { style: 'geometric', complexity: 'medium' }
        ];
    }

    getTechnicalDesigns() {
        return [
            { style: 'geometric', complexity: 'high' },
            { style: 'minimal', complexity: 'medium' },
            { style: 'professional', complexity: 'medium' },
            { style: 'bold', complexity: 'low' }
        ];
    }

    getMedicalDesigns() {
        return [
            { style: 'professional', complexity: 'low' },
            { style: 'elegant', complexity: 'medium' },
            { style: 'minimal', complexity: 'low' },
            { style: 'luxury', complexity: 'medium' }
        ];
    }

    getBusinessDesigns() {
        return [
            { style: 'professional', complexity: 'medium' },
            { style: 'luxury', complexity: 'high' },
            { style: 'elegant', complexity: 'medium' },
            { style: 'bold', complexity: 'low' }
        ];
    }

    getLegalDesigns() {
        return [
            { style: 'professional', complexity: 'low' },
            { style: 'elegant', complexity: 'medium' },
            { style: 'luxury', complexity: 'medium' },
            { style: 'minimal', complexity: 'low' }
        ];
    }

    createMinimalTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <rect width="280" height="200" fill="white"/>

            <!-- Minimal Header -->
            <rect x="20" y="20" width="240" height="1" fill="${primary}"/>

            <!-- Minimal Photo -->
            <circle cx="50" cy="50" r="15" fill="none" stroke="${secondary}" stroke-width="2"/>
            <circle cx="50" cy="50" r="10" fill="${accent}" opacity="0.3"/>

            <!-- Minimal Typography -->
            <rect x="80" y="40" width="120" height="6" fill="${primary}" rx="1"/>
            <rect x="80" y="50" width="80" height="4" fill="${secondary}" opacity="0.7" rx="1"/>

            <!-- Minimal Content -->
            <rect x="20" y="80" width="240" height="1" fill="${secondary}" opacity="0.3"/>
            <rect x="20" y="90" width="60" height="4" fill="${primary}" rx="1"/>
            <rect x="20" y="100" width="200" height="2" fill="${secondary}" opacity="0.5"/>
            <rect x="20" y="106" width="180" height="2" fill="${secondary}" opacity="0.4"/>
            <rect x="20" y="112" width="190" height="2" fill="${secondary}" opacity="0.3"/>

            <!-- Minimal Skills -->
            <rect x="20" y="130" width="240" height="1" fill="${accent}" opacity="0.3"/>
            <rect x="20" y="140" width="40" height="4" fill="${accent}" rx="1"/>
            <rect x="70" y="140" width="35" height="4" fill="${accent}" opacity="0.8" rx="1"/>
            <rect x="115" y="140" width="30" height="4" fill="${accent}" opacity="0.6" rx="1"/>

            <!-- Minimal Footer -->
            <rect x="20" y="170" width="240" height="1" fill="${primary}" opacity="0.2"/>
        </svg>`;
    }

    createProfessionalTemplate(primary, secondary, accent) {
        return `
        <svg width="280" height="200" viewBox="0 0 280 200" xmlns="http://www.w3.org/2000/svg">
            <rect width="280" height="200" fill="white" stroke="#e5e7eb" stroke-width="1"/>

            <!-- Professional Header -->
            <rect x="0" y="0" width="280" height="60" fill="${primary}"/>
            <rect x="0" y="55" width="280" height="5" fill="${accent}"/>

            <!-- Professional Photo -->
            <rect x="30" y="15" width="40" height="40" fill="white" rx="4"/>
            <rect x="33" y="18" width="34" height="34" fill="${secondary}" opacity="0.3" rx="2"/>

            <!-- Professional Typography -->
            <rect x="80" y="20" width="140" height="8" fill="white" rx="1"/>
            <rect x="80" y="32" width="100" height="6" fill="white" opacity="0.9" rx="1"/>
            <rect x="80" y="42" width="120" height="4" fill="white" opacity="0.8" rx="1"/>

            <!-- Professional Content -->
            <rect x="20" y="80" width="240" height="2" fill="${primary}"/>
            <rect x="20" y="90" width="80" height="6" fill="${primary}" rx="1"/>
            <rect x="20" y="105" width="220" height="3" fill="${secondary}" opacity="0.7"/>
            <rect x="20" y="112" width="200" height="3" fill="${secondary}" opacity="0.6"/>
            <rect x="20" y="119" width="180" height="3" fill="${secondary}" opacity="0.5"/>

            <!-- Professional Experience -->
            <rect x="20" y="140" width="240" height="2" fill="${accent}"/>
            <rect x="20" y="150" width="70" height="6" fill="${accent}" rx="1"/>
            <rect x="20" y="165" width="190" height="3" fill="${secondary}" opacity="0.6"/>
            <rect x="20" y="172" width="170" height="3" fill="${secondary}" opacity="0.5"/>

            <!-- Professional Skills -->
            <rect x="20" y="185" width="60" height="4" fill="${primary}" opacity="0.8" rx="2"/>
            <rect x="90" y="185" width="50" height="4" fill="${accent}" opacity="0.8" rx="2"/>
            <rect x="150" y="185" width="55" height="4" fill="${secondary}" opacity="0.8" rx="2"/>
        </svg>`;
    }

    updateGenerationStatus(status, type = 'active') {
        const statusElement = document.getElementById('generationStatus');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `status-indicator ${type}`;
        }
    }

    updateProgress(progress) {
        const progressElement = document.getElementById('generationProgress');
        const progressText = document.getElementById('progressText');

        if (progressElement) {
            progressElement.style.width = `${progress}%`;
        }

        if (progressText) {
            progressText.textContent = `جاري الإنشاء... ${Math.round(progress)}%`;
        }
    }

    toggleGeneration() {
        // Implementation for pause/resume generation
        console.log('Toggle generation');
    }

    clearAllTemplates() {
        if (confirm('هل أنت متأكد من حذف جميع القوالب؟')) {
            if (window.templateManager) {
                templateManager.templates = [];
                const grid = document.getElementById('templateGrid');
                if (grid) {
                    grid.innerHTML = '<div class="no-templates">لا توجد قوالب</div>';
                }
                this.updateTemplateCount();
            }
        }
    }

    startAutoGeneration() {
        // Start with initial batch
        setTimeout(() => {
            this.generateTemplatesBatch();
        }, 2000);

        // Continue generating periodically
        setInterval(() => {
            if (window.templateManager && templateManager.templates.length < 500) {
                this.generateNewTemplates();
            }
        }, 60000); // Every minute
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize auto template system
const autoTemplateSystem = new AutoTemplateSystem();

// Export for global access
window.autoTemplateSystem = autoTemplateSystem;
