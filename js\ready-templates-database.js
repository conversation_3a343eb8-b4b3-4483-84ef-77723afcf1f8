// ELASHRAFY CV - Ready Templates Database
// قاعدة بيانات القوالب الجاهزة - الأشرافي للسيرة الذاتية

class ReadyTemplatesDatabase {
    constructor() {
        this.templates = [];
        this.categories = {
            modern: 'عصري',
            creative: 'إبداعي', 
            executive: 'تنفيذي',
            academic: 'أكاديمي',
            technical: 'تقني',
            medical: 'طبي',
            business: 'تجاري',
            legal: 'قانوني',
            artistic: 'فني',
            minimalist: 'مينيمال',
            luxury: 'فاخر',
            corporate: 'مؤسسي',
            startup: 'ناشئ',
            freelancer: 'مستقل',
            designer: 'مصمم'
        };
        
        this.styles = {
            'glass-morphism': 'زجاجي شفاف',
            'neo-brutalism': 'جريء عصري',
            'gradient-mesh': 'تدرج شبكي',
            'organic-shapes': 'أشكال عضوية',
            'geometric-art': 'فن هندسي',
            'watercolor': 'ألوان مائية',
            'paper-cut': 'قطع ورقي',
            'neon-cyber': 'نيون سايبر',
            'vintage-retro': 'كلاسيكي عتيق',
            'crystal-clear': 'كريستال صافي',
            'aurora-borealis': 'شفق قطبي',
            'holographic': 'هولوجرافي'
        };
        
        this.init();
    }
    
    init() {
        this.generateProfessionalTemplates();
        console.log(`✨ تم إنشاء ${this.templates.length} قالب احترافي جاهز`);
    }
    
    generateProfessionalTemplates() {
        // Generate 400+ professional templates
        let templateId = 1;
        
        Object.keys(this.categories).forEach(category => {
            Object.keys(this.styles).forEach(style => {
                // Generate 2-3 templates per category-style combination
                for (let variant = 1; variant <= 3; variant++) {
                    const template = this.createProfessionalTemplate(
                        templateId++,
                        category,
                        style,
                        variant
                    );
                    this.templates.push(template);
                }
            });
        });
        
        // Add some extra premium templates
        this.addPremiumTemplates(templateId);
    }
    
    createProfessionalTemplate(id, category, style, variant) {
        const colors = this.getColorPalette(category, style);
        const layout = this.getLayoutStructure(category, style);
        
        return {
            id: `template_${id}`,
            name: this.generateTemplateName(category, style, variant),
            category: category,
            style: style,
            variant: variant,
            description: this.generateTemplateDescription(category, style),
            preview: this.generateTemplatePreview(category, style, colors, layout),
            thumbnail: this.generateTemplateThumbnail(category, style, colors),
            isPremium: this.isPremiumTemplate(category, style),
            rating: this.generateRating(),
            downloads: this.generateDownloads(),
            tags: this.generateTemplateTags(category, style),
            colors: colors,
            layout: layout,
            fonts: this.getRecommendedFonts(category, style),
            features: this.getTemplateFeatures(category, style),
            atsCompatible: true,
            multiPage: Math.random() > 0.4,
            photoSupport: true,
            customizable: true,
            responsive: true,
            printOptimized: true,
            languages: ['ar', 'en'],
            industries: this.getTargetIndustries(category),
            experience: this.getTargetExperience(category),
            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString(),
            author: 'محمد الأشرافي',
            version: '1.0.0'
        };
    }
    
    generateTemplateName(category, style, variant) {
        const categoryName = this.categories[category];
        const styleName = this.styles[style];
        
        const nameVariants = {
            1: 'كلاسيك',
            2: 'برو',
            3: 'إليت'
        };
        
        return `${categoryName} ${styleName} ${nameVariants[variant]}`;
    }
    
    generateTemplateDescription(category, style) {
        const descriptions = {
            modern: {
                'glass-morphism': 'تصميم عصري بتأثيرات زجاجية شفافة وخطوط نظيفة',
                'neo-brutalism': 'تصميم جريء وقوي مع ألوان صارخة وخطوط واضحة',
                'gradient-mesh': 'تدرجات لونية متطورة تخلق عمقاً بصرياً رائعاً'
            },
            creative: {
                'organic-shapes': 'أشكال طبيعية متدفقة تضفي حيوية وإبداعاً على التصميم',
                'watercolor': 'تأثيرات الألوان المائية الناعمة والفنية الجذابة',
                'geometric-art': 'فن هندسي معاصر مع أشكال هندسية متوازنة ومبتكرة'
            },
            executive: {
                'luxury': 'تصميم فاخر وأنيق يعكس الاحترافية والخبرة القيادية',
                'crystal-clear': 'وضوح كريستالي مع انعكاسات ضوئية راقية',
                'holographic': 'تأثيرات هولوجرافية متقدمة تضفي طابعاً مستقبلياً'
            }
        };
        
        const categoryDescriptions = descriptions[category];
        if (categoryDescriptions && categoryDescriptions[style]) {
            return categoryDescriptions[style];
        }
        
        return `تصميم ${this.categories[category]} بنمط ${this.styles[style]} احترافي ومتميز`;
    }
    
    generateTemplatePreview(category, style, colors, layout) {
        // Generate SVG preview based on category and style
        const { primary, secondary, accent, background } = colors;
        
        const svgContent = this.createAdvancedSVGPreview(style, colors, layout);
        return `data:image/svg+xml;base64,${btoa(svgContent)}`;
    }

    generateTemplateThumbnail(category, style, colors) {
        // Generate smaller thumbnail version
        return this.generateTemplatePreview(category, style, colors, 'thumbnail');
    }
    
    createAdvancedSVGPreview(style, colors, layout) {
        const { primary, secondary, accent, background } = colors;
        
        const templates = {
            'glass-morphism': this.createGlassMorphismSVG(colors),
            'neo-brutalism': this.createNeoBrutalismSVG(colors),
            'gradient-mesh': this.createGradientMeshSVG(colors),
            'organic-shapes': this.createOrganicShapesSVG(colors),
            'geometric-art': this.createGeometricArtSVG(colors),
            'watercolor': this.createWatercolorSVG(colors),
            'neon-cyber': this.createNeonCyberSVG(colors),
            'crystal-clear': this.createCrystalClearSVG(colors),
            'luxury': this.createLuxurySVG(colors),
            'holographic': this.createHolographicSVG(colors)
        };
        
        return templates[style] || templates['glass-morphism'];
    }

    createNeonCyberSVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="neonGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#000;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#1a1a2e;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
                </linearGradient>
                <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>

            <!-- Dark cyber background -->
            <rect width="300" height="400" fill="url(#neonGrad)"/>

            <!-- Neon grid -->
            <defs>
                <pattern id="neonGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="${accent}" stroke-width="0.5" opacity="0.3"/>
                </pattern>
            </defs>
            <rect width="300" height="400" fill="url(#neonGrid)"/>

            <!-- Main container with neon border -->
            <rect x="20" y="20" width="260" height="360" fill="rgba(0,0,0,0.8)"
                  stroke="${primary}" stroke-width="2" rx="10" filter="url(#neonGlow)"/>

            <!-- Header neon section -->
            <rect x="35" y="35" width="230" height="70" fill="rgba(0,0,0,0.9)"
                  stroke="${accent}" stroke-width="1" rx="8" filter="url(#neonGlow)"/>

            <!-- Neon photo frame -->
            <circle cx="75" cy="70" r="20" fill="none" stroke="${secondary}" stroke-width="2" filter="url(#neonGlow)"/>
            <circle cx="75" cy="70" r="15" fill="${accent}" opacity="0.3"/>

            <!-- Neon text lines -->
            <rect x="105" y="55" width="140" height="6" fill="${primary}" rx="3" filter="url(#neonGlow)"/>
            <rect x="105" y="68" width="100" height="4" fill="${secondary}" rx="2" filter="url(#neonGlow)"/>
            <rect x="105" y="78" width="120" height="3" fill="${accent}" rx="1" filter="url(#neonGlow)"/>

            <!-- Experience neon block -->
            <rect x="35" y="125" width="230" height="60" fill="rgba(0,0,0,0.7)"
                  stroke="${primary}" stroke-width="1" rx="6" filter="url(#neonGlow)"/>

            <rect x="50" y="140" width="70" height="4" fill="${primary}" rx="2" filter="url(#neonGlow)"/>
            <rect x="50" y="150" width="180" height="2" fill="${secondary}" rx="1" opacity="0.8"/>
            <rect x="50" y="157" width="160" height="2" fill="${accent}" rx="1" opacity="0.7"/>
            <rect x="50" y="164" width="170" height="2" fill="${primary}" rx="1" opacity="0.6"/>
            <rect x="50" y="171" width="150" height="2" fill="${secondary}" rx="1" opacity="0.5"/>

            <!-- Skills neon bars -->
            <rect x="35" y="205" width="70" height="25" fill="rgba(0,0,0,0.8)"
                  stroke="${primary}" stroke-width="1" rx="4" filter="url(#neonGlow)"/>
            <rect x="115" y="205" width="70" height="25" fill="rgba(0,0,0,0.8)"
                  stroke="${secondary}" stroke-width="1" rx="4" filter="url(#neonGlow)"/>
            <rect x="195" y="205" width="70" height="25" fill="rgba(0,0,0,0.8)"
                  stroke="${accent}" stroke-width="1" rx="4" filter="url(#neonGlow)"/>

            <!-- Education neon section -->
            <rect x="35" y="250" width="230" height="50" fill="rgba(0,0,0,0.6)"
                  stroke="${secondary}" stroke-width="1" rx="5" filter="url(#neonGlow)"/>

            <rect x="50" y="265" width="60" height="3" fill="${secondary}" rx="1" filter="url(#neonGlow)"/>
            <rect x="50" y="275" width="150" height="2" fill="${primary}" rx="1" opacity="0.7"/>
            <rect x="50" y="282" width="130" height="2" fill="${accent}" rx="1" opacity="0.6"/>
            <rect x="50" y="289" width="140" height="2" fill="${secondary}" rx="1" opacity="0.5"/>

            <!-- Contact neon footer -->
            <rect x="35" y="320" width="230" height="40" fill="rgba(0,0,0,0.9)"
                  stroke="${accent}" stroke-width="1" rx="6" filter="url(#neonGlow)"/>

            <!-- Neon decorative elements -->
            <circle cx="280" cy="40" r="5" fill="${accent}" filter="url(#neonGlow)"/>
            <circle cx="25" cy="200" r="3" fill="${primary}" filter="url(#neonGlow)"/>
            <circle cx="285" cy="300" r="4" fill="${secondary}" filter="url(#neonGlow)"/>

            <!-- Scanning line effect -->
            <rect x="0" y="150" width="300" height="2" fill="${accent}" opacity="0.6" filter="url(#neonGlow)">
                <animateTransform attributeName="transform" type="translate"
                                values="0,-150; 0,400; 0,-150" dur="3s" repeatCount="indefinite"/>
            </rect>
        </svg>`;
    }

    createCrystalClearSVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="crystalGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#e2e8f0;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#cbd5e1;stop-opacity:1" />
                </linearGradient>
                <filter id="crystalShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="${primary}" flood-opacity="0.1"/>
                </filter>
                <filter id="crystalReflection" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="2"/>
                </filter>
            </defs>

            <!-- Crystal background -->
            <rect width="300" height="400" fill="url(#crystalGrad)"/>

            <!-- Crystal facets -->
            <polygon points="0,0 150,100 300,0" fill="rgba(255,255,255,0.3)" opacity="0.5"/>
            <polygon points="0,400 150,300 300,400" fill="rgba(255,255,255,0.2)" opacity="0.4"/>

            <!-- Main crystal container -->
            <polygon points="40,40 260,40 270,50 270,350 260,360 40,360 30,350 30,50"
                     fill="rgba(255,255,255,0.9)" filter="url(#crystalShadow)"/>

            <!-- Crystal reflections -->
            <polygon points="40,40 260,40 250,50 50,50" fill="rgba(255,255,255,0.7)" filter="url(#crystalReflection)"/>
            <polygon points="30,50 40,40 40,360 30,350" fill="rgba(255,255,255,0.5)" filter="url(#crystalReflection)"/>

            <!-- Header crystal section -->
            <polygon points="50,50 250,50 245,90 55,90" fill="rgba(255,255,255,0.8)" filter="url(#crystalShadow)"/>

            <!-- Photo crystal frame -->
            <polygon points="70,60 100,60 95,85 75,85" fill="rgba(255,255,255,0.9)" filter="url(#crystalShadow)"/>
            <polygon points="75,65 95,65 90,80 80,80" fill="${primary}" opacity="0.6"/>

            <!-- Crystal text elements -->
            <polygon points="110,62 230,62 225,70 115,70" fill="${primary}" opacity="0.8"/>
            <polygon points="110,75 200,75 195,81 115,81" fill="${secondary}" opacity="0.7"/>
            <polygon points="110,84 220,84 215,89 115,89" fill="${accent}" opacity="0.6"/>

            <!-- Experience crystal block -->
            <polygon points="50,110 250,110 245,170 55,170" fill="rgba(255,255,255,0.85)" filter="url(#crystalShadow)"/>

            <polygon points="65,125 135,125 130,133 70,133" fill="${primary}" opacity="0.7"/>
            <polygon points="65,140 215,140 210,146 70,146" fill="${secondary}" opacity="0.6"/>
            <polygon points="65,152 195,152 190,157 70,157" fill="${accent}" opacity="0.5"/>
            <polygon points="65,162 205,162 200,167 70,167" fill="${primary}" opacity="0.4"/>

            <!-- Skills crystal gems -->
            <polygon points="50,190 110,190 105,220 55,220" fill="${primary}" opacity="0.7" filter="url(#crystalShadow)"/>
            <polygon points="120,190 180,190 175,220 125,220" fill="${secondary}" opacity="0.6" filter="url(#crystalShadow)"/>
            <polygon points="190,190 250,190 245,220 195,220" fill="${accent}" opacity="0.5" filter="url(#crystalShadow)"/>

            <!-- Crystal skill indicators -->
            <polygon points="80,205 90,200 85,210" fill="rgba(255,255,255,0.9)"/>
            <polygon points="150,205 160,200 155,210" fill="rgba(255,255,255,0.8)"/>
            <polygon points="220,205 230,200 225,210" fill="rgba(255,255,255,0.7)"/>

            <!-- Education crystal panel -->
            <polygon points="50,240 250,240 245,290 55,290" fill="rgba(255,255,255,0.8)" filter="url(#crystalShadow)"/>

            <polygon points="65,255 125,255 120,262 70,262" fill="${secondary}" opacity="0.6"/>
            <polygon points="65,270 195,270 190,275 70,275" fill="${primary}" opacity="0.5"/>
            <polygon points="65,280 175,280 170,285 70,285" fill="${accent}" opacity="0.4"/>

            <!-- Contact crystal footer -->
            <polygon points="50,310 250,310 245,350 55,350" fill="rgba(255,255,255,0.75)" filter="url(#crystalShadow)"/>

            <!-- Crystal decorative elements -->
            <polygon points="270,60 280,50 285,65 275,70" fill="${accent}" opacity="0.4" filter="url(#crystalShadow)"/>
            <polygon points="25,180 35,170 40,185 30,190" fill="${primary}" opacity="0.3" filter="url(#crystalShadow)"/>
            <polygon points="275,280 285,270 290,285 280,290" fill="${secondary}" opacity="0.3" filter="url(#crystalShadow)"/>
        </svg>`;
    }

    createLuxurySVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="luxuryGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#2d2d2d;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="goldGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
                    <stop offset="50%" style="stop-color:#ffed4e;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#ffd700;stop-opacity:1" />
                </linearGradient>
                <filter id="luxuryShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000" flood-opacity="0.5"/>
                </filter>
            </defs>

            <!-- Luxury dark background -->
            <rect width="300" height="400" fill="url(#luxuryGrad)"/>

            <!-- Gold border frame -->
            <rect x="10" y="10" width="280" height="380" fill="none"
                  stroke="url(#goldGrad)" stroke-width="3" rx="15" filter="url(#luxuryShadow)"/>

            <!-- Inner luxury frame -->
            <rect x="25" y="25" width="250" height="350" fill="rgba(0,0,0,0.8)"
                  stroke="url(#goldGrad)" stroke-width="1" rx="10" filter="url(#luxuryShadow)"/>

            <!-- Header luxury section -->
            <rect x="40" y="40" width="220" height="70" fill="rgba(255,215,0,0.1)"
                  stroke="url(#goldGrad)" stroke-width="1" rx="8"/>

            <!-- Luxury photo frame -->
            <circle cx="80" cy="75" r="22" fill="none" stroke="url(#goldGrad)" stroke-width="2"/>
            <circle cx="80" cy="75" r="18" fill="rgba(255,215,0,0.2)"/>
            <circle cx="80" cy="75" r="15" fill="${primary}" opacity="0.8"/>

            <!-- Elegant text lines -->
            <rect x="110" y="58" width="130" height="8" fill="url(#goldGrad)" rx="4"/>
            <rect x="110" y="72" width="100" height="6" fill="rgba(255,215,0,0.8)" rx="3"/>
            <rect x="110" y="83" width="120" height="5" fill="rgba(255,215,0,0.6)" rx="2"/>

            <!-- Experience luxury block -->
            <rect x="40" y="130" width="220" height="70" fill="rgba(255,215,0,0.05)"
                  stroke="rgba(255,215,0,0.3)" stroke-width="1" rx="6"/>

            <rect x="55" y="145" width="80" height="6" fill="url(#goldGrad)" rx="3"/>
            <rect x="55" y="158" width="180" height="4" fill="rgba(255,215,0,0.7)" rx="2"/>
            <rect x="55" y="167" width="160" height="4" fill="rgba(255,215,0,0.6)" rx="2"/>
            <rect x="55" y="176" width="170" height="4" fill="rgba(255,215,0,0.5)" rx="2"/>
            <rect x="55" y="185" width="150" height="4" fill="rgba(255,215,0,0.4)" rx="2"/>

            <!-- Skills luxury badges -->
            <rect x="40" y="220" width="60" height="30" fill="rgba(255,215,0,0.1)"
                  stroke="url(#goldGrad)" stroke-width="1" rx="5"/>
            <rect x="110" y="220" width="60" height="30" fill="rgba(255,215,0,0.08)"
                  stroke="rgba(255,215,0,0.8)" stroke-width="1" rx="5"/>
            <rect x="180" y="220" width="60" height="30" fill="rgba(255,215,0,0.06)"
                  stroke="rgba(255,215,0,0.6)" stroke-width="1" rx="5"/>

            <!-- Luxury skill text -->
            <rect x="55" y="232" width="30" height="4" fill="url(#goldGrad)" rx="2"/>
            <rect x="125" y="232" width="30" height="4" fill="rgba(255,215,0,0.8)" rx="2"/>
            <rect x="195" y="232" width="30" height="4" fill="rgba(255,215,0,0.6)" rx="2"/>

            <!-- Education luxury panel -->
            <rect x="40" y="270" width="220" height="50" fill="rgba(255,215,0,0.03)"
                  stroke="rgba(255,215,0,0.2)" stroke-width="1" rx="5"/>

            <rect x="55" y="285" width="70" height="5" fill="rgba(255,215,0,0.8)" rx="2"/>
            <rect x="55" y="295" width="150" height="3" fill="rgba(255,215,0,0.6)" rx="1"/>
            <rect x="55" y="303" width="130" height="3" fill="rgba(255,215,0,0.5)" rx="1"/>
            <rect x="55" y="311" width="140" height="3" fill="rgba(255,215,0,0.4)" rx="1"/>

            <!-- Contact luxury footer -->
            <rect x="40" y="340" width="220" height="30" fill="rgba(255,215,0,0.1)"
                  stroke="url(#goldGrad)" stroke-width="1" rx="6"/>

            <!-- Luxury decorative elements -->
            <circle cx="270" cy="50" r="8" fill="url(#goldGrad)" opacity="0.6"/>
            <polygon points="30,180 40,170 35,190" fill="url(#goldGrad)" opacity="0.4"/>
            <circle cx="275" cy="300" r="6" fill="rgba(255,215,0,0.5)"/>

            <!-- Ornamental corners -->
            <polygon points="25,25 35,25 25,35" fill="url(#goldGrad)" opacity="0.8"/>
            <polygon points="275,25 275,35 265,25" fill="url(#goldGrad)" opacity="0.8"/>
            <polygon points="25,375 35,375 25,365" fill="url(#goldGrad)" opacity="0.8"/>
            <polygon points="275,375 275,365 265,375" fill="url(#goldGrad)" opacity="0.8"/>
        </svg>`;
    }

    createHolographicSVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="holoGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ff00ff;stop-opacity:0.8" />
                    <stop offset="25%" style="stop-color:#00ffff;stop-opacity:0.6" />
                    <stop offset="50%" style="stop-color:#ffff00;stop-opacity:0.7" />
                    <stop offset="75%" style="stop-color:#ff0080;stop-opacity:0.5" />
                    <stop offset="100%" style="stop-color:#8000ff;stop-opacity:0.8" />
                </linearGradient>
                <linearGradient id="holoGrad2" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#00ff80;stop-opacity:0.6" />
                    <stop offset="50%" style="stop-color:#0080ff;stop-opacity:0.8" />
                    <stop offset="100%" style="stop-color:#ff8000;stop-opacity:0.5" />
                </linearGradient>
                <filter id="holoBlur" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
                </filter>
                <filter id="holoGlow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>

            <!-- Holographic background -->
            <rect width="300" height="400" fill="#000"/>

            <!-- Holographic interference patterns -->
            <rect width="300" height="400" fill="url(#holoGrad1)" opacity="0.3"/>

            <!-- Holographic scan lines -->
            <defs>
                <pattern id="holoLines" width="4" height="4" patternUnits="userSpaceOnUse">
                    <rect width="4" height="2" fill="rgba(0,255,255,0.1)"/>
                    <rect y="2" width="4" height="2" fill="rgba(255,0,255,0.1)"/>
                </pattern>
            </defs>
            <rect width="300" height="400" fill="url(#holoLines)"/>

            <!-- Main holographic container -->
            <rect x="30" y="30" width="240" height="340" fill="rgba(0,0,0,0.7)"
                  stroke="url(#holoGrad1)" stroke-width="2" rx="12" filter="url(#holoGlow)"/>

            <!-- Header holographic section -->
            <rect x="45" y="45" width="210" height="70" fill="rgba(0,255,255,0.1)"
                  stroke="url(#holoGrad2)" stroke-width="1" rx="8" filter="url(#holoGlow)"/>

            <!-- Holographic photo -->
            <circle cx="85" cy="80" r="20" fill="none" stroke="#00ffff" stroke-width="2" filter="url(#holoGlow)"/>
            <circle cx="85" cy="80" r="15" fill="rgba(255,0,255,0.3)" filter="url(#holoBlur)"/>
            <circle cx="85" cy="80" r="10" fill="rgba(0,255,255,0.5)"/>

            <!-- Holographic text -->
            <rect x="115" y="60" width="120" height="6" fill="#00ffff" rx="3" filter="url(#holoGlow)"/>
            <rect x="115" y="72" width="90" height="5" fill="#ff00ff" rx="2" filter="url(#holoGlow)"/>
            <rect x="115" y="82" width="110" height="4" fill="#ffff00" rx="2" filter="url(#holoGlow)"/>

            <!-- Experience holographic block -->
            <rect x="45" y="135" width="210" height="60" fill="rgba(255,0,255,0.05)"
                  stroke="rgba(255,0,255,0.5)" stroke-width="1" rx="6" filter="url(#holoGlow)"/>

            <rect x="60" y="150" width="70" height="4" fill="#ff00ff" rx="2" filter="url(#holoGlow)"/>
            <rect x="60" y="160" width="160" height="3" fill="rgba(0,255,255,0.8)" rx="1"/>
            <rect x="60" y="168" width="140" height="3" fill="rgba(255,255,0,0.7)" rx="1"/>
            <rect x="60" y="176" width="150" height="3" fill="rgba(255,0,128,0.6)" rx="1"/>
            <rect x="60" y="184" width="130" height="3" fill="rgba(128,0,255,0.5)" rx="1"/>

            <!-- Skills holographic panels -->
            <rect x="45" y="215" width="60" height="25" fill="rgba(0,255,255,0.1)"
                  stroke="#00ffff" stroke-width="1" rx="4" filter="url(#holoGlow)"/>
            <rect x="115" y="215" width="60" height="25" fill="rgba(255,0,255,0.1)"
                  stroke="#ff00ff" stroke-width="1" rx="4" filter="url(#holoGlow)"/>
            <rect x="185" y="215" width="60" height="25" fill="rgba(255,255,0,0.1)"
                  stroke="#ffff00" stroke-width="1" rx="4" filter="url(#holoGlow)"/>

            <!-- Holographic skill indicators -->
            <rect x="60" y="225" width="30" height="3" fill="#00ffff" rx="1" filter="url(#holoGlow)"/>
            <rect x="130" y="225" width="30" height="3" fill="#ff00ff" rx="1" filter="url(#holoGlow)"/>
            <rect x="200" y="225" width="30" height="3" fill="#ffff00" rx="1" filter="url(#holoGlow)"/>

            <!-- Education holographic section -->
            <rect x="45" y="260" width="210" height="50" fill="rgba(0,255,128,0.05)"
                  stroke="rgba(0,255,128,0.4)" stroke-width="1" rx="5" filter="url(#holoGlow)"/>

            <rect x="60" y="275" width="60" height="4" fill="#00ff80" rx="2" filter="url(#holoGlow)"/>
            <rect x="60" y="285" width="140" height="3" fill="rgba(0,128,255,0.7)" rx="1"/>
            <rect x="60" y="293" width="120" height="3" fill="rgba(255,128,0,0.6)" rx="1"/>
            <rect x="60" y="301" width="130" height="3" fill="rgba(128,255,0,0.5)" rx="1"/>

            <!-- Contact holographic footer -->
            <rect x="45" y="330" width="210" height="30" fill="rgba(255,128,255,0.1)"
                  stroke="url(#holoGrad1)" stroke-width="1" rx="6" filter="url(#holoGlow)"/>

            <!-- Holographic decorative elements -->
            <circle cx="270" cy="60" r="6" fill="#00ffff" filter="url(#holoGlow)"/>
            <circle cx="35" cy="200" r="4" fill="#ff00ff" filter="url(#holoGlow)"/>
            <circle cx="275" cy="320" r="5" fill="#ffff00" filter="url(#holoGlow)"/>

            <!-- Animated holographic effects -->
            <rect x="0" y="0" width="300" height="400" fill="url(#holoGrad1)" opacity="0.1">
                <animateTransform attributeName="transform" type="translate"
                                values="0,0; 10,5; 0,0; -5,10; 0,0" dur="4s" repeatCount="indefinite"/>
            </rect>
        </svg>`;
    }

    createGradientMeshSVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="meshGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.9" />
                    <stop offset="50%" style="stop-color:${accent};stop-opacity:0.7" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:0.8" />
                </linearGradient>
                <radialGradient id="meshGrad2" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" style="stop-color:${accent};stop-opacity:0.6" />
                    <stop offset="100%" style="stop-color:${primary};stop-opacity:0.3" />
                </radialGradient>
                <filter id="meshBlur" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="8"/>
                </filter>
            </defs>

            <!-- Gradient mesh background -->
            <rect width="300" height="400" fill="url(#meshGrad1)" rx="16"/>

            <!-- Mesh overlay shapes -->
            <ellipse cx="100" cy="100" rx="80" ry="60" fill="url(#meshGrad2)" filter="url(#meshBlur)"/>
            <ellipse cx="200" cy="300" rx="70" ry="90" fill="url(#meshGrad2)" filter="url(#meshBlur)" opacity="0.8"/>
            <ellipse cx="250" cy="150" rx="60" ry="40" fill="url(#meshGrad2)" filter="url(#meshBlur)" opacity="0.6"/>

            <!-- Content areas -->
            <rect x="30" y="30" width="240" height="70" fill="rgba(255,255,255,0.95)" rx="15" opacity="0.9"/>
            <circle cx="80" cy="65" r="20" fill="${accent}" opacity="0.8"/>
            <rect x="110" y="50" width="140" height="8" fill="${primary}" rx="4" opacity="0.7"/>
            <rect x="110" y="65" width="100" height="6" fill="${secondary}" rx="3" opacity="0.6"/>
            <rect x="110" y="78" width="120" height="5" fill="${accent}" rx="2" opacity="0.5"/>

            <!-- Experience section -->
            <rect x="30" y="120" width="240" height="80" fill="rgba(255,255,255,0.9)" rx="12" opacity="0.85"/>
            <rect x="45" y="135" width="80" height="6" fill="${primary}" rx="3" opacity="0.8"/>
            <rect x="45" y="150" width="200" height="4" fill="${secondary}" rx="2" opacity="0.6"/>
            <rect x="45" y="158" width="180" height="4" fill="${accent}" rx="2" opacity="0.5"/>
            <rect x="45" y="166" width="190" height="4" fill="${primary}" rx="2" opacity="0.4"/>
            <rect x="45" y="174" width="170" height="4" fill="${secondary}" rx="2" opacity="0.4"/>
            <rect x="45" y="182" width="160" height="4" fill="${accent}" rx="2" opacity="0.3"/>

            <!-- Skills section -->
            <rect x="30" y="220" width="240" height="60" fill="rgba(255,255,255,0.85)" rx="10" opacity="0.8"/>
            <rect x="45" y="235" width="60" height="6" fill="${primary}" rx="3" opacity="0.7"/>
            <rect x="115" y="235" width="55" height="6" fill="${secondary}" rx="3" opacity="0.6"/>
            <rect x="180" y="235" width="50" height="6" fill="${accent}" rx="3" opacity="0.5"/>
            <rect x="45" y="250" width="65" height="6" fill="${accent}" rx="3" opacity="0.6"/>
            <rect x="120" y="250" width="45" height="6" fill="${primary}" rx="3" opacity="0.5"/>
            <rect x="175" y="250" width="55" height="6" fill="${secondary}" rx="3" opacity="0.4"/>

            <!-- Education section -->
            <rect x="30" y="300" width="240" height="50" fill="rgba(255,255,255,0.8)" rx="8" opacity="0.75"/>
            <rect x="45" y="315" width="70" height="5" fill="${secondary}" rx="2" opacity="0.7"/>
            <rect x="45" y="325" width="180" height="3" fill="${primary}" rx="1" opacity="0.5"/>
            <rect x="45" y="332" width="160" height="3" fill="${accent}" rx="1" opacity="0.4"/>
            <rect x="45" y="339" width="140" height="3" fill="${secondary}" rx="1" opacity="0.3"/>
        </svg>`;
    }

    createOrganicShapesSVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="organicGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary}" />
                    <stop offset="50%" style="stop-color:${accent}" />
                    <stop offset="100%" style="stop-color:${secondary}" />
                </linearGradient>
                <filter id="organicShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="${primary}" flood-opacity="0.3"/>
                </filter>
            </defs>

            <!-- Organic background -->
            <path d="M0,50 Q50,0 100,30 T200,20 Q250,40 300,80 L300,350 Q250,400 200,370 T100,380 Q50,360 0,350 Z"
                  fill="url(#organicGrad)" opacity="0.1"/>

            <!-- Main content area -->
            <path d="M30,40 Q80,20 130,35 T230,30 Q270,50 270,90 L270,320 Q230,360 180,345 T80,350 Q30,330 30,290 Z"
                  fill="rgba(255,255,255,0.95)" filter="url(#organicShadow)"/>

            <!-- Header organic shape -->
            <path d="M50,50 Q100,35 150,45 T220,40 Q250,55 250,80 L250,100 Q220,115 170,110 T100,115 Q50,100 50,80 Z"
                  fill="rgba(255,255,255,0.9)"/>

            <!-- Photo organic shape -->
            <ellipse cx="80" cy="75" rx="18" ry="20" fill="${accent}" opacity="0.8" transform="rotate(15 80 75)"/>

            <!-- Text lines with organic curves -->
            <path d="M110,65 Q160,60 210,65" stroke="${primary}" stroke-width="6" fill="none" stroke-linecap="round" opacity="0.8"/>
            <path d="M110,78 Q150,75 190,78" stroke="${secondary}" stroke-width="4" fill="none" stroke-linecap="round" opacity="0.7"/>
            <path d="M110,88 Q170,85 220,88" stroke="${accent}" stroke-width="3" fill="none" stroke-linecap="round" opacity="0.6"/>

            <!-- Experience organic section -->
            <path d="M50,130 Q120,115 190,125 T240,120 Q250,135 250,155 L250,190 Q240,205 190,200 T120,205 Q50,190 50,155 Z"
                  fill="rgba(255,255,255,0.85)" opacity="0.9"/>

            <path d="M70,145 Q110,142 150,145" stroke="${primary}" stroke-width="5" fill="none" stroke-linecap="round" opacity="0.7"/>
            <path d="M70,158 Q140,155 210,158" stroke="${secondary}" stroke-width="3" fill="none" stroke-linecap="round" opacity="0.6"/>
            <path d="M70,168 Q130,165 190,168" stroke="${accent}" stroke-width="3" fill="none" stroke-linecap="round" opacity="0.5"/>
            <path d="M70,178 Q140,175 200,178" stroke="${primary}" stroke-width="3" fill="none" stroke-linecap="round" opacity="0.4"/>

            <!-- Skills organic bubbles -->
            <ellipse cx="80" cy="250" rx="25" ry="15" fill="${primary}" opacity="0.7" transform="rotate(-10 80 250)"/>
            <ellipse cx="150" cy="245" rx="30" ry="18" fill="${secondary}" opacity="0.6" transform="rotate(5 150 245)"/>
            <ellipse cx="210" cy="255" rx="28" ry="16" fill="${accent}" opacity="0.5" transform="rotate(-15 210 255)"/>

            <!-- Education flowing section -->
            <path d="M50,300 Q100,285 150,295 T220,290 Q250,305 250,325 L250,340 Q220,355 170,350 T100,355 Q50,340 50,325 Z"
                  fill="rgba(255,255,255,0.8)" opacity="0.8"/>

            <path d="M70,315 Q120,312 170,315" stroke="${secondary}" stroke-width="4" fill="none" stroke-linecap="round" opacity="0.6"/>
            <path d="M70,325 Q140,322 200,325" stroke="${accent}" stroke-width="3" fill="none" stroke-linecap="round" opacity="0.5"/>

            <!-- Decorative organic elements -->
            <circle cx="260" cy="80" r="8" fill="${accent}" opacity="0.4"/>
            <ellipse cx="40" cy="200" rx="6" ry="10" fill="${primary}" opacity="0.3" transform="rotate(30 40 200)"/>
            <circle cx="270" cy="280" r="5" fill="${secondary}" opacity="0.3"/>
        </svg>`;
    }
    
    createGlassMorphismSVG(colors) {
        const { primary, secondary, accent } = colors;
        
        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="glassGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.8" />
                    <stop offset="50%" style="stop-color:${accent};stop-opacity:0.6" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:0.4" />
                </linearGradient>
                <filter id="blur" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="4"/>
                </filter>
                <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
                    <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>

            <!-- Background -->
            <rect width="300" height="400" fill="url(#glassGrad)" rx="16"/>

            <!-- Glass morphism shapes -->
            <circle cx="80" cy="80" r="60" fill="rgba(255,255,255,0.2)" filter="url(#blur)"/>
            <circle cx="220" cy="320" r="50" fill="rgba(255,255,255,0.15)" filter="url(#blur)"/>

            <!-- Header section -->
            <rect x="30" y="30" width="240" height="80" fill="rgba(255,255,255,0.25)"
                  rx="20" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>

            <!-- Photo -->
            <circle cx="80" cy="70" r="25" fill="rgba(255,255,255,0.9)" filter="url(#glow)"/>
            <circle cx="80" cy="70" r="20" fill="${accent}" opacity="0.7"/>

            <!-- Name and title -->
            <rect x="120" y="50" width="140" height="12" fill="rgba(255,255,255,0.9)" rx="6"/>
            <rect x="120" y="68" width="100" height="8" fill="rgba(255,255,255,0.8)" rx="4"/>
            <rect x="120" y="82" width="120" height="6" fill="rgba(255,255,255,0.7)" rx="3"/>

            <!-- Content sections -->
            <rect x="30" y="130" width="240" height="60" fill="rgba(255,255,255,0.2)"
                  rx="15" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="45" y="145" width="80" height="8" fill="rgba(255,255,255,0.8)" rx="4"/>
            <rect x="45" y="160" width="200" height="4" fill="rgba(255,255,255,0.6)" rx="2"/>
            <rect x="45" y="168" width="180" height="4" fill="rgba(255,255,255,0.5)" rx="2"/>
            <rect x="45" y="176" width="190" height="4" fill="rgba(255,255,255,0.4)" rx="2"/>

            <!-- Experience section -->
            <rect x="30" y="210" width="240" height="80" fill="rgba(255,255,255,0.18)"
                  rx="15" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="45" y="225" width="90" height="8" fill="rgba(255,255,255,0.8)" rx="4"/>
            <rect x="45" y="240" width="200" height="4" fill="rgba(255,255,255,0.6)" rx="2"/>
            <rect x="45" y="248" width="180" height="4" fill="rgba(255,255,255,0.5)" rx="2"/>
            <rect x="45" y="256" width="190" height="4" fill="rgba(255,255,255,0.4)" rx="2"/>
            <rect x="45" y="270" width="170" height="4" fill="rgba(255,255,255,0.4)" rx="2"/>

            <!-- Skills section -->
            <rect x="30" y="310" width="240" height="50" fill="rgba(255,255,255,0.15)"
                  rx="12" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
            <rect x="45" y="325" width="60" height="6" fill="rgba(255,255,255,0.8)" rx="3"/>
            <rect x="115" y="325" width="55" height="6" fill="rgba(255,255,255,0.7)" rx="3"/>
            <rect x="180" y="325" width="50" height="6" fill="rgba(255,255,255,0.6)" rx="3"/>
            <rect x="45" y="340" width="65" height="6" fill="rgba(255,255,255,0.7)" rx="3"/>
            <rect x="120" y="340" width="45" height="6" fill="rgba(255,255,255,0.6)" rx="3"/>

            <!-- Decorative elements -->
            <circle cx="260" cy="50" r="12" fill="rgba(255,255,255,0.3)" filter="url(#blur)"/>
            <circle cx="50" cy="380" r="10" fill="rgba(255,255,255,0.25)" filter="url(#blur)"/>
        </svg>`;
    }

    createGeometricArtSVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="geoGrad1" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:${primary}" />
                    <stop offset="100%" style="stop-color:${accent}" />
                </linearGradient>
                <linearGradient id="geoGrad2" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:${secondary}" />
                    <stop offset="100%" style="stop-color:${primary}" />
                </linearGradient>
                <pattern id="geoPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <polygon points="10,0 20,10 10,20 0,10" fill="${accent}" opacity="0.1"/>
                </pattern>
            </defs>

            <!-- Geometric background -->
            <rect width="300" height="400" fill="url(#geoPattern)"/>

            <!-- Main geometric container -->
            <polygon points="30,30 270,30 270,370 30,370" fill="white" stroke="${primary}" stroke-width="2"/>

            <!-- Header geometric section -->
            <polygon points="40,40 260,40 250,90 50,90" fill="url(#geoGrad1)" opacity="0.9"/>

            <!-- Photo geometric frame -->
            <polygon points="60,50 100,50 95,80 65,80" fill="white" stroke="${accent}" stroke-width="2"/>
            <polygon points="65,55 95,55 90,75 70,75" fill="${secondary}" opacity="0.8"/>

            <!-- Name and title geometric lines -->
            <rect x="110" y="55" width="130" height="8" fill="${primary}"/>
            <polygon points="110,68 200,68 195,76 115,76" fill="${secondary}" opacity="0.8"/>
            <rect x="110" y="80" width="110" height="4" fill="${accent}" opacity="0.7"/>

            <!-- Experience geometric section -->
            <polygon points="40,110 260,110 255,180 45,180" fill="rgba(255,255,255,0.95)" stroke="${primary}" stroke-width="1"/>

            <rect x="55" y="125" width="70" height="6" fill="${primary}" opacity="0.8"/>
            <polygon points="55,140 235,140 230,146 60,146" fill="${secondary}" opacity="0.6"/>
            <rect x="55" y="152" width="200" height="3" fill="${accent}" opacity="0.5"/>
            <polygon points="55,160 215,160 210,164 60,164" fill="${primary}" opacity="0.4"/>
            <rect x="55" y="168" width="180" height="3" fill="${secondary}" opacity="0.4"/>

            <!-- Skills geometric shapes -->
            <polygon points="40,200 120,200 115,230 45,230" fill="${primary}" opacity="0.8"/>
            <polygon points="130,200 210,200 205,230 135,230" fill="${secondary}" opacity="0.7"/>
            <polygon points="220,200 260,200 255,230 225,230" fill="${accent}" opacity="0.6"/>

            <!-- Hexagonal skill indicators -->
            <polygon points="80,215 90,210 90,220" fill="white" opacity="0.9"/>
            <polygon points="170,215 180,210 180,220" fill="white" opacity="0.8"/>
            <polygon points="242,215 252,210 252,220" fill="white" opacity="0.7"/>

            <!-- Education geometric section -->
            <polygon points="40,250 260,250 250,300 50,300" fill="rgba(255,255,255,0.9)" stroke="${secondary}" stroke-width="1"/>

            <rect x="55" y="265" width="80" height="5" fill="${secondary}" opacity="0.7"/>
            <polygon points="55,275 205,275 200,280 60,280" fill="${primary}" opacity="0.5"/>
            <rect x="55" y="285" width="170" height="3" fill="${accent}" opacity="0.4"/>

            <!-- Contact geometric section -->
            <polygon points="40,320 260,320 255,360 45,360" fill="url(#geoGrad2)" opacity="0.8"/>

            <!-- Decorative geometric elements -->
            <polygon points="270,50 280,40 290,50 280,60" fill="${accent}" opacity="0.6"/>
            <polygon points="20,150 30,140 40,150 30,160" fill="${primary}" opacity="0.5"/>
            <polygon points="270,250 285,240 300,250 285,260" fill="${secondary}" opacity="0.4"/>

            <!-- Grid overlay -->
            <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="${primary}" stroke-width="0.5" opacity="0.2"/>
                </pattern>
            </defs>
            <rect width="300" height="400" fill="url(#grid)"/>
        </svg>`;
    }

    createWatercolorSVG(colors) {
        const { primary, secondary, accent } = colors;

        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="watercolor1" cx="50%" cy="50%" r="60%">
                    <stop offset="0%" style="stop-color:${primary};stop-opacity:0.3" />
                    <stop offset="70%" style="stop-color:${accent};stop-opacity:0.2" />
                    <stop offset="100%" style="stop-color:${secondary};stop-opacity:0.1" />
                </radialGradient>
                <radialGradient id="watercolor2" cx="30%" cy="70%" r="50%">
                    <stop offset="0%" style="stop-color:${accent};stop-opacity:0.4" />
                    <stop offset="100%" style="stop-color:${primary};stop-opacity:0.1" />
                </radialGradient>
                <filter id="waterBlur" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="12"/>
                </filter>
            </defs>

            <!-- Watercolor background -->
            <rect width="300" height="400" fill="#fefefe"/>

            <!-- Watercolor splashes -->
            <ellipse cx="100" cy="80" rx="80" ry="60" fill="url(#watercolor1)" filter="url(#waterBlur)" transform="rotate(-15 100 80)"/>
            <ellipse cx="220" cy="200" rx="70" ry="90" fill="url(#watercolor2)" filter="url(#waterBlur)" transform="rotate(20 220 200)"/>
            <ellipse cx="80" cy="300" rx="60" ry="40" fill="url(#watercolor1)" filter="url(#waterBlur)" transform="rotate(-30 80 300)"/>

            <!-- Content areas with watercolor edges -->
            <rect x="40" y="40" width="220" height="70" fill="rgba(255,255,255,0.9)" rx="15" opacity="0.95"/>

            <!-- Photo with watercolor frame -->
            <circle cx="80" cy="75" r="22" fill="${accent}" opacity="0.3" filter="url(#waterBlur)"/>
            <circle cx="80" cy="75" r="18" fill="white" opacity="0.9"/>
            <circle cx="80" cy="75" r="15" fill="${primary}" opacity="0.7"/>

            <!-- Watercolor text lines -->
            <rect x="110" y="55" width="130" height="8" fill="${primary}" rx="4" opacity="0.8"/>
            <rect x="110" y="70" width="100" height="6" fill="${secondary}" rx="3" opacity="0.7"/>
            <rect x="110" y="82" width="120" height="5" fill="${accent}" rx="2" opacity="0.6"/>

            <!-- Experience watercolor section -->
            <ellipse cx="150" cy="150" rx="120" ry="40" fill="${secondary}" opacity="0.2" filter="url(#waterBlur)"/>
            <rect x="50" y="130" width="200" height="60" fill="rgba(255,255,255,0.85)" rx="12" opacity="0.9"/>

            <rect x="65" y="145" width="80" height="6" fill="${primary}" rx="3" opacity="0.7"/>
            <rect x="65" y="158" width="170" height="4" fill="${secondary}" rx="2" opacity="0.6"/>
            <rect x="65" y="166" width="150" height="4" fill="${accent}" rx="2" opacity="0.5"/>
            <rect x="65" y="174" width="160" height="4" fill="${primary}" rx="2" opacity="0.4"/>

            <!-- Skills watercolor bubbles -->
            <circle cx="80" cy="240" r="25" fill="${primary}" opacity="0.3" filter="url(#waterBlur)"/>
            <circle cx="150" cy="235" r="30" fill="${secondary}" opacity="0.25" filter="url(#waterBlur)"/>
            <circle cx="210" cy="245" r="28" fill="${accent}" opacity="0.2" filter="url(#waterBlur)"/>

            <rect x="65" y="235" width="50" height="5" fill="white" rx="2" opacity="0.9"/>
            <rect x="135" y="230" width="45" height="5" fill="white" rx="2" opacity="0.8"/>
            <rect x="195" y="240" width="40" height="5" fill="white" rx="2" opacity="0.7"/>

            <!-- Education watercolor wash -->
            <ellipse cx="150" cy="320" rx="100" ry="30" fill="${accent}" opacity="0.15" filter="url(#waterBlur)"/>
            <rect x="60" y="305" width="180" height="40" fill="rgba(255,255,255,0.8)" rx="10" opacity="0.85"/>

            <rect x="75" y="318" width="70" height="5" fill="${secondary}" rx="2" opacity="0.6"/>
            <rect x="75" y="328" width="150" height="3" fill="${primary}" rx="1" opacity="0.5"/>
            <rect x="75" y="335" width="130" height="3" fill="${accent}" rx="1" opacity="0.4"/>

            <!-- Watercolor decorative elements -->
            <circle cx="250" cy="60" r="15" fill="${accent}" opacity="0.2" filter="url(#waterBlur)"/>
            <circle cx="50" cy="180" r="12" fill="${primary}" opacity="0.15" filter="url(#waterBlur)"/>
            <circle cx="270" cy="280" r="10" fill="${secondary}" opacity="0.1" filter="url(#waterBlur)"/>
        </svg>`;
    }
    
    createNeoBrutalismSVG(colors) {
        const { primary, secondary, accent } = colors;
        
        return `
        <svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="6" dy="6" stdDeviation="0" flood-color="#000" flood-opacity="1"/>
                </filter>
            </defs>
            
            <!-- Bold background -->
            <rect width="300" height="400" fill="${primary}" stroke="#000" stroke-width="4"/>
            
            <!-- Header -->
            <rect x="20" y="20" width="260" height="80" fill="${accent}" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            
            <!-- Photo -->
            <rect x="40" y="35" width="50" height="50" fill="#fff" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="45" y="40" width="40" height="40" fill="${secondary}"/>
            
            <!-- Name and title -->
            <rect x="110" y="40" width="140" height="12" fill="#000"/>
            <rect x="110" y="58" width="100" height="8" fill="#000"/>
            <rect x="110" y="72" width="120" height="6" fill="#000"/>
            
            <!-- Content blocks -->
            <rect x="25" y="120" width="250" height="50" fill="${secondary}" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="40" y="135" width="80" height="8" fill="#000"/>
            <rect x="40" y="150" width="200" height="4" fill="#000"/>
            <rect x="40" y="158" width="180" height="4" fill="#000"/>
            
            <!-- Experience block -->
            <rect x="25" y="190" width="250" height="70" fill="#fff" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="40" y="205" width="90" height="8" fill="#000"/>
            <rect x="40" y="220" width="200" height="4" fill="#000"/>
            <rect x="40" y="228" width="180" height="4" fill="#000"/>
            <rect x="40" y="236" width="190" height="4" fill="#000"/>
            <rect x="40" y="244" width="170" height="4" fill="#000"/>
            
            <!-- Skills blocks -->
            <rect x="25" y="280" width="80" height="30" fill="${accent}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            <rect x="115" y="280" width="75" height="30" fill="${primary}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            <rect x="200" y="280" width="75" height="30" fill="${secondary}" 
                  stroke="#000" stroke-width="3" filter="url(#shadow)"/>
            
            <!-- Footer -->
            <rect x="25" y="330" width="250" height="40" fill="#fff" 
                  stroke="#000" stroke-width="4" filter="url(#shadow)"/>
            <rect x="40" y="345" width="60" height="6" fill="#000"/>
            <rect x="110" y="345" width="50" height="6" fill="#000"/>
            <rect x="170" y="345" width="70" height="6" fill="#000"/>
        </svg>`;
    }
    
    getColorPalette(category, style) {
        const palettes = {
            modern: {
                primary: '#1e40af',
                secondary: '#3b82f6', 
                accent: '#8b5cf6',
                background: '#f8fafc',
                text: '#1e293b'
            },
            creative: {
                primary: '#7c2d12',
                secondary: '#ea580c',
                accent: '#fb923c', 
                background: '#fff7ed',
                text: '#9a3412'
            },
            executive: {
                primary: '#0f172a',
                secondary: '#1e293b',
                accent: '#334155',
                background: '#f1f5f9',
                text: '#020617'
            },
            luxury: {
                primary: '#7f1d1d',
                secondary: '#dc2626',
                accent: '#f87171',
                background: '#fef2f2', 
                text: '#450a0a'
            }
        };
        
        return palettes[category] || palettes.modern;
    }
    
    getLayoutStructure(category, style) {
        const layouts = {
            modern: 'single-column-header',
            creative: 'asymmetric-creative',
            executive: 'traditional-formal',
            academic: 'detailed-sections',
            technical: 'skills-focused',
            medical: 'clean-professional'
        };
        
        return layouts[category] || 'single-column-header';
    }
    
    isPremiumTemplate(category, style) {
        const premiumStyles = ['luxury', 'holographic', 'crystal-clear', 'aurora-borealis'];
        const premiumCategories = ['executive', 'luxury'];
        
        return premiumStyles.includes(style) || premiumCategories.includes(category) || Math.random() > 0.7;
    }
    
    generateRating() {
        return (4.0 + Math.random() * 1.0).toFixed(1);
    }
    
    generateDownloads() {
        return Math.floor(Math.random() * 10000) + 500;
    }
    
    generateTemplateTags(category, style) {
        const baseTags = ['احترافي', 'جميل', 'حديث', 'متميز'];
        const categoryTags = {
            modern: ['عصري', 'نظيف', 'أنيق'],
            creative: ['إبداعي', 'فني', 'ملون'],
            executive: ['تنفيذي', 'قيادي', 'فاخر'],
            academic: ['أكاديمي', 'علمي', 'بحثي'],
            technical: ['تقني', 'هندسي', 'متطور']
        };
        
        const styleTags = {
            'glass-morphism': ['شفاف', 'زجاجي'],
            'neo-brutalism': ['جريء', 'قوي'],
            'gradient-mesh': ['متدرج', 'ملون'],
            'organic-shapes': ['طبيعي', 'متدفق'],
            'geometric-art': ['هندسي', 'متوازن']
        };
        
        return [
            ...baseTags,
            ...(categoryTags[category] || []),
            ...(styleTags[style] || [])
        ];
    }
    
    getRecommendedFonts(category, style) {
        const fonts = {
            modern: ['Cairo', 'Inter', 'Roboto'],
            creative: ['Amiri', 'Playfair Display', 'Montserrat'],
            executive: ['Times New Roman', 'Georgia', 'Crimson Text'],
            academic: ['Noto Sans Arabic', 'Source Sans Pro', 'Lato'],
            technical: ['Fira Code', 'Source Code Pro', 'Ubuntu Mono']
        };
        
        return fonts[category] || fonts.modern;
    }
    
    getTemplateFeatures(category, style) {
        const baseFeatures = ['ATS متوافق', 'متعدد الصفحات', 'دعم الصور', 'قابل للتخصيص'];
        const styleFeatures = {
            'glass-morphism': ['تأثيرات زجاجية', 'شفافية متقدمة'],
            'neo-brutalism': ['تصميم جريء', 'ألوان قوية'],
            'gradient-mesh': ['تدرجات معقدة', 'عمق بصري'],
            'luxury': ['تصميم فاخر', 'تفاصيل راقية']
        };
        
        return [...baseFeatures, ...(styleFeatures[style] || [])];
    }
    
    getTargetIndustries(category) {
        const industries = {
            modern: ['تكنولوجيا', 'تسويق', 'تصميم'],
            creative: ['فنون', 'إعلام', 'تصميم جرافيك'],
            executive: ['إدارة', 'استشارات', 'مالية'],
            academic: ['تعليم', 'بحث علمي', 'جامعات'],
            technical: ['هندسة', 'برمجة', 'تكنولوجيا'],
            medical: ['طب', 'صحة', 'صيدلة'],
            business: ['أعمال', 'مبيعات', 'تجارة'],
            legal: ['قانون', 'محاماة', 'قضاء']
        };
        
        return industries[category] || ['عام'];
    }
    
    getTargetExperience(category) {
        const experience = {
            academic: ['طالب', 'خريج جديد', 'باحث'],
            executive: ['مدير', 'مدير تنفيذي', 'قائد فريق'],
            technical: ['مطور', 'مهندس', 'محلل تقني']
        };
        
        return experience[category] || ['جميع المستويات'];
    }
    
    addPremiumTemplates(startId) {
        // Add 50 extra premium templates
        for (let i = 0; i < 50; i++) {
            const premiumTemplate = this.createPremiumTemplate(startId + i);
            this.templates.push(premiumTemplate);
        }
    }
    
    createPremiumTemplate(id) {
        const premiumCategories = ['luxury', 'executive', 'creative'];
        const premiumStyles = ['holographic', 'crystal-clear', 'aurora-borealis'];
        
        const category = premiumCategories[Math.floor(Math.random() * premiumCategories.length)];
        const style = premiumStyles[Math.floor(Math.random() * premiumStyles.length)];
        
        const template = this.createProfessionalTemplate(id, category, style, 1);
        template.isPremium = true;
        template.name = `${template.name} بريميوم`;
        template.rating = (4.5 + Math.random() * 0.5).toFixed(1);
        template.downloads = Math.floor(Math.random() * 5000) + 2000;
        
        return template;
    }
    
    // Search and filter methods
    searchTemplates(query) {
        if (!query) return this.templates;
        
        const searchTerm = query.toLowerCase();
        return this.templates.filter(template => 
            template.name.toLowerCase().includes(searchTerm) ||
            template.description.toLowerCase().includes(searchTerm) ||
            template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
            this.categories[template.category].toLowerCase().includes(searchTerm) ||
            this.styles[template.style].toLowerCase().includes(searchTerm)
        );
    }
    
    filterByCategory(category) {
        if (category === 'all') return this.templates;
        return this.templates.filter(template => template.category === category);
    }
    
    filterByStyle(style) {
        if (style === 'all') return this.templates;
        return this.templates.filter(template => template.style === style);
    }
    
    sortTemplates(templates, sortBy) {
        switch (sortBy) {
            case 'popular':
                return templates.sort((a, b) => b.downloads - a.downloads);
            case 'newest':
                return templates.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            case 'rating':
                return templates.sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));
            case 'name':
                return templates.sort((a, b) => a.name.localeCompare(b.name));
            default:
                return templates;
        }
    }
    
    getTemplateById(id) {
        return this.templates.find(template => template.id === id);
    }
    
    getTemplatesByCategory(category) {
        return this.templates.filter(template => template.category === category);
    }
    
    getFeaturedTemplates(count = 12) {
        return this.templates
            .filter(template => parseFloat(template.rating) >= 4.5)
            .sort((a, b) => b.downloads - a.downloads)
            .slice(0, count);
    }
    
    getPopularTemplates(count = 20) {
        return this.templates
            .sort((a, b) => b.downloads - a.downloads)
            .slice(0, count);
    }
    
    getNewestTemplates(count = 15) {
        return this.templates
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, count);
    }
    
    getPremiumTemplates() {
        return this.templates.filter(template => template.isPremium);
    }
    
    getFreeTemplates() {
        return this.templates.filter(template => !template.isPremium);
    }
    
    getTemplateStats() {
        return {
            total: this.templates.length,
            premium: this.getPremiumTemplates().length,
            free: this.getFreeTemplates().length,
            categories: Object.keys(this.categories).length,
            styles: Object.keys(this.styles).length,
            averageRating: (this.templates.reduce((sum, t) => sum + parseFloat(t.rating), 0) / this.templates.length).toFixed(1),
            totalDownloads: this.templates.reduce((sum, t) => sum + t.downloads, 0)
        };
    }
}

// Initialize the templates database
const readyTemplatesDB = new ReadyTemplatesDatabase();

// Export for global access
window.readyTemplatesDB = readyTemplatesDB;
