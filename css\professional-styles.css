/* ELASHRAFY CV - Professional Styles */
/* الأنماط الاحترافية - الأشرافي للسيرة الذاتية */

/* CSS Variables */
:root {
    /* Colors */
    --primary-color: #1e40af;
    --primary-light: #3b82f6;
    --primary-dark: #1e3a8a;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Grays */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Typography */
    --font-family-ar: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-en: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family-ar);
    line-height: 1.6;
    color: var(--gray-800);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

[dir="ltr"] body {
    font-family: var(--font-family-en);
}

/* Professional Header */
.professional-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

/* Brand Section */
.brand-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.brand-logo svg {
    transition: transform var(--transition-normal);
}

.brand-logo:hover svg {
    transform: scale(1.05) rotate(2deg);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-title {
    font-size: var(--font-size-2xl);
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-1);
}

.brand-subtitle {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
}

/* Main Navigation */
.main-navigation {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    background: var(--gray-50);
    padding: var(--spacing-2);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    background: transparent;
    color: var(--gray-600);
    font-weight: 500;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    white-space: nowrap;
}

.nav-btn:hover {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
}

.nav-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-md);
}

.nav-btn.active:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-1px);
}

.nav-badge {
    background: var(--accent-color);
    color: white;
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-md);
    margin-left: var(--spacing-1);
}

.nav-btn.active .nav-badge {
    background: rgba(255, 255, 255, 0.2);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-md);
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-btn.secondary {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn.secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.language-toggle:hover {
    background: var(--gray-200);
    transform: translateY(-1px);
}

.lang-text {
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

/* Main Application */
.app-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
    min-height: calc(100vh - 80px);
}

/* App Sections */
.app-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.app-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-container {
    background: white;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

/* Section Header */
.section-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    padding: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.header-content {
    text-align: center;
    margin-bottom: var(--spacing-6);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-3);
}

.section-title i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

/* Templates Controls */
.templates-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.search-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.search-input-wrapper {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-input-wrapper i {
    position: absolute;
    left: var(--spacing-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 2;
}

.search-input-wrapper input {
    width: 100%;
    padding: var(--spacing-4) var(--spacing-4) var(--spacing-4) var(--spacing-12);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    background: white;
    transition: all var(--transition-fast);
}

.search-input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    transform: translateY(-1px);
}

.search-filters {
    display: flex;
    justify-content: center;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.filter-select {
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: white;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* View Controls */
.view-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-4);
}

.view-toggle {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: var(--spacing-1);
    border: 1px solid var(--gray-300);
}

.view-btn {
    padding: var(--spacing-2) var(--spacing-3);
    border: none;
    background: transparent;
    color: var(--gray-600);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.view-btn.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.sort-select {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: white;
    font-size: var(--font-size-sm);
    cursor: pointer;
}

/* Templates Statistics */
.templates-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-bottom: 1px solid var(--gray-200);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 500;
}

/* Templates Container */
.templates-container {
    padding: var(--spacing-8);
}

.loading-templates {
    text-align: center;
    padding: var(--spacing-16);
}

.loading-spinner {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.loading-templates p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
}

/* Placeholder Sections */
.editor-placeholder,
.preview-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-radius: var(--radius-2xl);
    margin: var(--spacing-8);
}

.placeholder-content {
    text-align: center;
    max-width: 400px;
}

.placeholder-content i {
    font-size: var(--font-size-5xl);
    color: var(--gray-400);
    margin-bottom: var(--spacing-4);
}

.placeholder-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-2);
}

.placeholder-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn.primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Features Grid */
.features-container {
    padding: var(--spacing-8);
}

.features-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.features-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.features-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-6);
}

.feature-card {
    padding: var(--spacing-6);
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

.feature-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Professional Footer */
.professional-footer {
    background: linear-gradient(135deg, var(--gray-900), var(--gray-800));
    color: white;
    margin-top: var(--spacing-16);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.footer-text h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-1);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-text p {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

.developer-credit {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.developer-info h4 {
    font-size: var(--font-size-xs);
    color: var(--gray-400);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: var(--spacing-1);
}

.developer-info h3 {
    font-size: var(--font-size-lg);
    font-weight: 700;
    margin-bottom: var(--spacing-1);
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.developer-info p {
    color: var(--gray-300);
    font-size: var(--font-size-sm);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-6);
    text-align: center;
}

.footer-bottom p {
    color: var(--gray-400);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-2);
}

.developer-signature strong {
    color: var(--primary-light);
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-container {
        padding: 0 var(--spacing-4);
    }
    
    .main-navigation {
        gap: var(--spacing-1);
    }
    
    .nav-btn {
        padding: var(--spacing-2) var(--spacing-3);
        font-size: var(--font-size-xs);
    }
    
    .templates-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        height: auto;
        padding: var(--spacing-4);
        gap: var(--spacing-4);
    }
    
    .brand-section {
        order: 1;
    }
    
    .main-navigation {
        order: 2;
        width: 100%;
        justify-content: center;
    }
    
    .header-actions {
        order: 3;
        width: 100%;
        justify-content: center;
    }
    
    .app-main {
        padding: var(--spacing-4);
    }
    
    .section-header {
        padding: var(--spacing-4);
    }
    
    .templates-controls {
        gap: var(--spacing-3);
    }
    
    .search-filters {
        flex-direction: column;
        align-items: center;
    }
    
    .view-controls {
        flex-direction: column;
        gap: var(--spacing-3);
    }
    
    .templates-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
    }
    
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-6);
        text-align: center;
    }
    
    .developer-credit {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .brand-title {
        font-size: var(--font-size-xl);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .nav-btn span {
        display: none;
    }
    
    .action-btn span {
        display: none;
    }
    
    .search-input-wrapper {
        max-width: 100%;
    }
}
