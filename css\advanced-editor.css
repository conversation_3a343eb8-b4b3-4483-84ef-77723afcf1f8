/* ELASHRAFY CV - Advanced Editor Styles */
/* أنماط المحرر المتقدم - الأشرافي للسيرة الذاتية */

/* Editor Layout */
.editor-layout {
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    height: calc(100vh - 120px);
    gap: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

/* Editor Sidebar */
.editor-sidebar {
    background: white;
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.editor-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
}

.editor-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.editor-header h3 i {
    color: var(--primary-color);
}

.template-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.template-name {
    font-weight: 500;
    color: var(--gray-800);
    font-size: var(--font-size-sm);
}

.template-category {
    font-size: var(--font-size-xs);
    color: var(--gray-600);
    background: var(--gray-100);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-md);
    display: inline-block;
    width: fit-content;
}

/* Editor Sections */
.editor-sections {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-4) 0;
}

.editor-section {
    padding: var(--spacing-3) var(--spacing-6);
    cursor: pointer;
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
}

.editor-section:hover {
    background: var(--gray-50);
}

.editor-section.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-left-color: var(--accent-color);
}

.section-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.section-header i {
    width: 20px;
    text-align: center;
}

/* Editor Actions */
.editor-actions {
    padding: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.editor-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    width: 100%;
}

.editor-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-md);
}

.editor-btn.primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.editor-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.editor-btn.secondary:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

.editor-btn.success {
    background: linear-gradient(135deg, var(--success-color), #34d399);
    color: white;
    box-shadow: var(--shadow-md);
}

.editor-btn.success:hover {
    background: linear-gradient(135deg, #059669, var(--success-color));
    transform: translateY(-1px);
}

/* Editor Main */
.editor-main {
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

/* Editor Toolbar */
.editor-toolbar {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    flex-wrap: wrap;
}

.toolbar-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-1);
    background: white;
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-300);
}

.toolbar-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    transition: all var(--transition-fast);
}

.toolbar-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.toolbar-btn.active {
    background: var(--primary-color);
    color: white;
}

.toolbar-select {
    padding: var(--spacing-2) var(--spacing-3);
    border: none;
    background: transparent;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    cursor: pointer;
    min-width: 100px;
}

.toolbar-select:focus {
    outline: none;
}

.color-picker {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    background: none;
}

/* Editor Content */
.editor-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-6);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeInUp 0.3s ease-out;
}

/* Form Styles */
.section-intro {
    margin-bottom: var(--spacing-6);
    padding: var(--spacing-4);
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-color);
}

.section-intro h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.section-intro p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.form-group {
    margin-bottom: var(--spacing-5);
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
}

.form-control {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: var(--gray-400);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

.form-help {
    margin-top: var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.form-help i {
    color: var(--accent-color);
}

/* Photo Upload */
.photo-upload-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.photo-preview {
    width: 150px;
    height: 150px;
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    overflow: hidden;
    background: var(--gray-50);
}

.photo-preview:hover {
    border-color: var(--primary-color);
    background: var(--gray-100);
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

.photo-placeholder {
    text-align: center;
    color: var(--gray-500);
}

.photo-placeholder i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-2);
    color: var(--gray-400);
}

.photo-controls {
    display: flex;
    gap: var(--spacing-2);
}

.btn-small {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: white;
    color: var(--gray-700);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.btn-small:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-small.danger {
    color: var(--error-color);
    border-color: var(--error-color);
}

.btn-small.danger:hover {
    background: var(--error-color);
    color: white;
}

/* Experience Items */
.experience-item {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-4);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.experience-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-3);
    border-bottom: 1px solid var(--gray-200);
}

.item-header h5 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.item-controls {
    display: flex;
    gap: var(--spacing-1);
}

.move-up-btn,
.move-down-btn,
.remove-section-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
}

.move-up-btn,
.move-down-btn {
    background: var(--gray-100);
    color: var(--gray-600);
}

.move-up-btn:hover,
.move-down-btn:hover {
    background: var(--primary-color);
    color: white;
}

.remove-section-btn {
    background: var(--error-color);
    color: white;
}

.remove-section-btn:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Add Section Button */
.add-section-btn {
    width: 100%;
    padding: var(--spacing-4);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    background: transparent;
    color: var(--gray-600);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
}

.add-section-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--gray-50);
}

/* AI Assistant */
.ai-assistant {
    background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
    color: white;
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    margin-top: var(--spacing-4);
}

.ai-assistant h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.ai-assistant p {
    margin-bottom: var(--spacing-4);
    opacity: 0.9;
}

.ai-assistant .btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
}

.ai-assistant .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Editor Preview */
.editor-preview {
    background: white;
    border-left: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.preview-header {
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.preview-controls {
    display: flex;
    gap: var(--spacing-1);
}

.preview-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--gray-300);
    background: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    transition: all var(--transition-fast);
}

.preview-btn:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.preview-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.preview-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-4);
    background: var(--gray-50);
}

.preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--gray-500);
    text-align: center;
}

.preview-placeholder i {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-4);
    color: var(--gray-400);
}

.preview-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--gray-500);
}

.preview-loading i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-2);
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .editor-layout {
        grid-template-columns: 280px 1fr 300px;
    }
}

@media (max-width: 1024px) {
    .editor-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        height: auto;
    }
    
    .editor-sidebar {
        border-right: none;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .editor-sections {
        display: flex;
        overflow-x: auto;
        padding: var(--spacing-2) var(--spacing-4);
    }
    
    .editor-section {
        white-space: nowrap;
        padding: var(--spacing-2) var(--spacing-4);
        border-left: none;
        border-bottom: 3px solid transparent;
    }
    
    .editor-section.active {
        border-left: none;
        border-bottom-color: var(--accent-color);
    }
    
    .editor-preview {
        display: none;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .editor-layout {
        gap: 0;
        border-radius: 0;
    }
    
    .editor-content {
        padding: var(--spacing-4);
    }
    
    .editor-toolbar {
        padding: var(--spacing-3);
        gap: var(--spacing-2);
    }
    
    .toolbar-group {
        gap: var(--spacing-1);
    }
    
    .toolbar-btn {
        width: 32px;
        height: 32px;
    }
    
    .experience-item {
        padding: var(--spacing-4);
    }
    
    .photo-preview {
        width: 120px;
        height: 120px;
    }
}
